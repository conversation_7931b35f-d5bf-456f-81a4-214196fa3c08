/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */


package edu.memphis.ccrg.lida.framework.gui.panels;

import edu.memphis.ccrg.lida.framework.Agent;
import edu.memphis.ccrg.lida.framework.gui.FrameworkGuiController;

import javax.swing.*;

/**
 * Abstract implementation of {@link GuiPanel}
 * 
 * Panels that extend this class inherit the 'initPanel()' method.  
 * This method is called for every GuiPaneluiPanel this created in FrameworkGui.  
 * Since all GuiPanels have a reference to the {@link Agent} object, 
 * the GuiPanel can access the data from the model when its {@link #refresh()} method is called.
 * 
 * Implementations should be added in the guiPanels.properties file 
 * by name of panel and canonical name of class. 
 *
 * <AUTHOR> Snaider
 * <AUTHOR> J. McCall
 */
public abstract class GuiPanelImpl extends javax.swing.JPanel implements GuiPanel{

	/**
	 * the {@link FrameworkGuiController}
	 */
	protected FrameworkGuiController controller;
	/**
	 * the {@link Agent}
	 */
	protected Agent agent;

    /** Creates new form GuiPanelImpl */
    public GuiPanelImpl() {
        initComponents();
    }

    /** 
     * This method is called from within the constructor to
     * initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is
     * always regenerated by the Form Editor.
     */
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

		javax.swing.GroupLayout layout = new javax.swing.GroupLayout(this);
		this.setLayout(layout);
		layout.setHorizontalGroup(layout.createParallelGroup(
				javax.swing.GroupLayout.Alignment.LEADING).addGap(0, 400,
				Short.MAX_VALUE));
		layout.setVerticalGroup(layout.createParallelGroup(
				javax.swing.GroupLayout.Alignment.LEADING).addGap(0, 300,
				Short.MAX_VALUE));
	}// </editor-fold>//GEN-END:initComponents


    // Variables declaration - do not modify//GEN-BEGIN:variables
    // End of variables declaration//GEN-END:variables
    @Override
	public void registerGuiController(FrameworkGuiController lgc) {
        controller = lgc;
    }

    /**
     * Subclasses may override this method.
     */
    @Override
	public void display(Object o) {
    }

    /**
     * Subclasses may override this method.
     */
    @Override
	public void refresh() {
    }
	
    /**
     * Subclasses may override this method.
     */
	@Override
	public void initPanel(String[] param){
	}
    
    @Override
	public JPanel getPanel() {
        return this;
    }

    @Override
	public void registerAgent(Agent agent) {
		this.agent=agent;
	}

}