package edu.memphis.ccrg.lida.nlanguage;

import com.warmer.kgmaker.KgmakerApplication;
import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.LinkImpl;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeImpl;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamImpl0;
import edu.memphis.ccrg.lida.pam.PamLink;
import edu.memphis.ccrg.lida.workspace.WorkspaceContent;
import edu.memphis.ccrg.linars.Term;
import org.opennars.inference.TemporalRules;
import org.opennars.language.Conjunction;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;

/**
 * 一维结构分析，如词序，分析结构为二维，即平面树状。
 * 语义语法分析任务，将词句转化为语义树，多种方案：1、词序结构分离，2、词序结构合并
 * 与pam区别：1、有预测。2、专注arg。3、构建树图。4、可有推理。
 * 语法变量=关联问题和答案，通过蕴含等，蕴含有结构。语法成分可替换。原始词无结构=难推理
 * image和属性等，也可以是一种结构，如颜色，形状等。
 * 预测有两种：意识级预测=通达注意=权重+情绪。激活级预测=结构
 */
public class SemanticAnalyzTask0 extends FrameworkTaskImpl{
	private PAMemory pam;
	private Link link;
	private WorkspaceContent listenNs;
//	private WorkspaceContent yufaNs;
	private WorkspaceContent yuyiNs;
	private String message;
	private List<Node> words = new ArrayList<>();
	int lsize;    // 对应场景总边数量属性
	int actlsize;// 场景已激活的边的数量
	Set<Link> links; //当前词所在的所有构式边
	int linksSize; //当前词所在的所有构式边的数量
	Node pos;
	TreeBag yiTreeBag;
	private AtomicBoolean started = new AtomicBoolean(false);
	Set<String> mmcache0 = PamImpl0.smcache;

	/**
	 * Default constructor
	 * @param link {@link PamLink}
	 * @param pam {@link PAMemory}
	 */
	public SemanticAnalyzTask0(Link link, PAMemory pam , int linksSize) {
		super(1);
		this.pam = pam;
		this.link = link;
		this.linksSize = linksSize;
		// 内听觉，外听觉，内视觉，外视觉，内文本，外文本，至少6个语句分析来源，来源汇总区分，统一到符号语义
		// 语言高频处理，需要常驻？分别尝试下
		listenNs = pam.getWorkspaceBuffer("listen").getBufferContent(null);
//		yufaNs = pam.getWorkspaceBuffer("yufa").getBufferContent(null);
		yuyiNs = pam.getWorkspaceBuffer("yuyi").getBufferContent(null);

		yiTreeBag = ((ChartTreeSet)yuyiNs).chartSet;
	}

	/**
	 * Adds link's sink to the percept and tries to add the link as well then finishes.
	 */
	@Override
	protected synchronized void runThisFrameworkTask() {
//		System.out.println("语义分析任务开始");

		// 案例1：只有自负的人才能坚持研究颠覆性agi。案例2：描述计算15+8的过程

		// 语言脑区，搜索常见搭配+语法结构，前者语义，需要两两结合查，后者则可单查，语法网不大
		// 不能只看语法，多语义会导致语法结构不同，语义太多，需要语法结构来限定
		// amr+构式更靠谱，语义依存需要识别场景角色，构式只需要词性（构式本身也有词性类型）

		// todo 20250421，基本确定短语结构形式，暂时也不依赖词性，词性可以蕴含在nl里，narsese自身可处理

		// 语法搜pos，不同层次+不同时空，如果整合为一个线程，太拥挤，还要两个分别循环。语义复杂很多
		// 语法线程会搜到带属性变量语义嵌套，语义会兼顾单字扩散（可能分开），语义也会搜到带概念变量嵌套
		// 三种模式，#加法=概念属性变量+语义框架嵌套，#形容词=概念+元素，**adj=跨模变量+接地属性，**VP=语法纯结构+属性+嵌套
		// 形容词需要理解，没有直接接地，除了颜色形状，副词有运动属性。##xy。跨模变量=是动物都可有，vp等纯语法结构，以具体模式存在
		// 可以直接关联语义结构与vp等，但嵌套方式要同构，可缺省同构，如只需知道vp，无需完全匹配完整。缺省同构与完全同构分开
		// 整体嵌套语法结构也保存，用进废退，要求低于一定复杂度。语义同理，太复杂语义经验也没意义，要分解精简，抽取抽象模式
		// 整体义法结构关联作用=加速分析，特别是常见结构和内涵，同结构新内涵，同内涵新结构。义法结构杂糅类似xy，范围过大
		// 义法结构杂糅无意义，义法自身体系内变量才有意义。其他跨模变量+跨模结构，自传体本身包含多模态，内涵本身杂糅，不是内涵和结构杂糅
		// 结构本身杂糅，时空结构应该也不能杂糅，时间内部关系，空间内部关系

		// nars的词项链集，冗余存储，可跨级激活，而逐层激活更自然。跨层的用处=缩短路径，针对最短路有利
		// 树图优势：完成度、预测性、实时性、渐次性，也有pam按类型结构推理，也细分，概念化后树图和链集同时存在，
		// 覆盖场景：视觉、语言等理解，都不是nars内部。要覆盖nars内部？自动化预测，目的性预测
		// todo 优先在预测集内扩散激活，甚至调起注意力动机
		actsence(link,null);
		// todo 整合感知
		cancel();
	}

	/**
	 * 处理findingList中的匹配链接
	 * @param matchTreeChart 当前匹配的树图表
	 * @param parent 父链接
	 * @return 是否找到匹配项
	 */
	private boolean processFindingList(TreeChart matchTreeChart, Link parent) {
		boolean isFind = false;
		// 判断当前key的value里，findingList是否包含当前parent链接，有则从findingList里移除，在foundList里添加
		// 无论是否嵌套，是否有匹配完整的嵌套，一旦符合根节点和有find边，都移除。是嵌套构式，要在parent替换整体子构式前移除
		if (matchTreeChart.findingList.contains(parent)) {
			matchTreeChart.findingList.remove(parent);
			isFind = true;
		}
		return isFind;
	}
	/**
	 * 检查found状态并更新匹配尺寸
	 * @param parent 父链接
	 * @param insertChart 要插入的图表
	 * @param matchTreeChart 当前匹配的树图表
	 * @param isFind 是否找到匹配项
	 * @return 是否找到匹配的源节点
	 */
	private boolean checkFoundStatus(Link parent, TreeChart insertChart, TreeChart matchTreeChart, boolean isFind) {
		boolean isFound = false;
		Collection<Term> foundcopy = new ArrayList<>();
		foundcopy.addAll(matchTreeChart.foundList);
		// 跟上面if不能合并，因为found变了，addall也不能放里面
		if (!isFind) {
			int ssize;
			for (Term link : foundcopy) {
				Node ss = ((Link) link).getSource();
				String rr;
				if (ss instanceof TreeChart) {
					rr = ((TreeChart) ss).sceneRoot.getTNname();
					ssize = ((TreeChart) ss).cpsize;
				} else {
					rr = ss.getTNname();
					ssize = 1;
				}
				// 无论是否已嵌套，只要找到匹配的，就替换
				if (rr.equals(parent.getSource().getTNname())) {
					isFound = true;
					((Link) link).setSource(insertChart);// 在原链接改
					((LinkImpl) link).init(((LinkImpl) link).term);
					matchTreeChart.cpsize = matchTreeChart.cpsize + insertChart.cpsize - ssize;
					break;
				}
			}
		}
		rebuildTreeChart(matchTreeChart, foundcopy);
		return isFound;
	}
	/**
	 * 重新构建树图表
	 * @param matchTreeChart 需要重建的树图表
	 */
	private void rebuildTreeChart(TreeChart matchTreeChart, Collection<Term> foundcopy) {
		// 将foundList和findingList里的链接整合为新list
		List<Link> links2 = new ArrayList<>();
		for (Term link : matchTreeChart.foundList) {
			links2.add((Link) link);
		}
		for (Term link : matchTreeChart.findingList) {
			links2.add((Link) link);
		}

		NodeImpl root = (NodeImpl) matchTreeChart.sceneRoot;
		// 然后重新组装并更新matchTreeChart的sceneTerm
		matchTreeChart = bulidTree(root, root.getTNname(), foundcopy, matchTreeChart.foundList.size(), links2);
		matchTreeChart.init(matchTreeChart.term);
	}

	private void actsence(Link parent, TreeChart insertChart) {
		Node sink;
		String sname;
		sink = (Node) parent.getSink();
		sname = sink.getTNname();
		boolean isAct = false;
		TreeChart matchTreeChart = null;

		// 遍历treebag里nameTable的keyset中，左括号前字符是否匹配sname，也就是是否已经有该sink，有则跳过，没有则加入
		for (String key : yiTreeBag.nameTable.keySet()) {
			if (key.substring(0, key.indexOf("(")).equals(sname)) {
				matchTreeChart = yiTreeBag.nameTable.get(key);
				boolean isFind = processFindingList(matchTreeChart, parent);
				boolean isFound = false;

				// 单字扩散可能边类型：isa等所有普通元素边类型，语义框架扩散边类型：顺承、isa、arg、内容、蕴含、感知模态等，可能双向
				// 目前默认+文本模态=文本语义，其他模态+学语言前=非文本含义，感知模态+学语言后=两类同时激活，视听文字理解后=文本语义
				// 感知激活非语言文本，与语言文本类似，也有嵌套结构和义法等？感知场景，文本场景，感知文本杂糅场景=内容杂糅。结构另算
				// 由文本激活感知，由感知激活文本，最终都是同时扩散。感知本身场景、感知文本互相激活场景、文本本身场景，都是场景处理方案

				// insertChart不为空，说明是中间层构式，来自下层激活，在未匹配完整时就要开始嵌套构建
				if (insertChart != null) {
					/*final String sourceName = */handleInsertChart(parent, insertChart, isFind, matchTreeChart);
					isFound = checkFoundStatus(parent, insertChart, matchTreeChart, isFind);
				}
				// 上面加了，这里就不用加了。如已有完整嵌套构式，也要加入foundList，把已匹配的对应部分替换成parent
				if (!matchTreeChart.foundList.contains(parent) && !isFound) {
					matchTreeChart.foundList.add((Term) parent);
				}
				processCompleteConstruction(matchTreeChart, sname);
				isAct = true;
				break; // 构式根节点都唯一，找到一个就跳出
			}
		}
		processSizeAttribute(sink);
		// 根据根节点sink判断buffer里是否有两条边或以上构式，有则尝试构建语yi树
		// 第一个词通常无完整构式，也有可能首词非句首=语句被截断，乱序输入等，越靠近句首=构式角色越靠前
		if (!isAct) {
			handleIncompleteActivation(sname, sink);
		}
	}

	/**
	 * 处理未完全激活的情况
	 * @param sname 场景名称
	 */
	private void handleIncompleteActivation(String sname, Node sink) {
		// 未全激活，加入新链接后，看是否完整激活
		Collection<Term> scenels = ((ChartTreeSet)yuyiNs).getLinksOfSinkT(sname);
		actlsize = scenels.size();
		double sss = (double) actlsize / lsize ;
		// 一个词也能匹配单构式？只有单个词如感叹+拟声等，连词=但和与？np=n等也是单构式
		if (( sss == 0.5) && linksSize < 500 || sss > 0.5) {
			// 如果构式总边数与已激活边数达到一半，则直接激活整个框架为完整构式，优先级最高，放进确定集合里
			// 未匹配的可当预测，也可先不管，不完整构式不好嵌套，完整的嵌套能一次成型，无需每次更新总构式，再有新嵌套直接拼接
			Set<Link> links0 = NeoUtil.getSomeLinks(sink,null, "<", null, null);
			List<Link> links00 = new ArrayList<>(links0);

			bulidTree((Term) sink, sname, scenels, lsize, links00);
		}
	}

	/**
	 * 处理size属性获取
	 * @param sink 当前节点
	 */
	private void processSizeAttribute(Node sink) {
		Object size = sink.getProperty("size");
		if (size != null) {
			// 分别按字符串类型或long类型取int值
			if(size instanceof String){
				lsize = Integer.parseInt((String) size);
			}else {
				lsize = ((Long) size).intValue();
			}
		}else {
			lsize = 100;	// 无size属性，说明是叶子节点或暂没设置，暂设为100，匹配率肯定低于0.5
		}
	}

	/**
	 * 处理完整构式激活逻辑
	 * @param matchTreeChart 当前匹配的树图表
	 * @param sname 场景名称
	 * @return 是否执行了激活操作
	 */
	private boolean processCompleteConstruction(TreeChart matchTreeChart, String sname) {
		// 如果findingList为空，说明该构式已经完整匹配，加入treebag的完整构式列表
		if (matchTreeChart.findingList.isEmpty()) {
			// 不能无限嵌套，复杂度不能大于句子长度
			if (matchTreeChart.complexity < KgmakerApplication.message.size() * 6){
				// 部分完整也可输出部分理解，通达意识。但既不是听觉也不是视觉，只是个语义框架
				// 判断是否完全解析理解，已纳入所有输入词语，并无多余成分等
				// 完全理解，则往下激活顺承，激活动机，不完全也可激活，但需要竞争，不能激活就执行

				// 递归给语义树子节点编号和提交
				matchTreeChart = numberTree(matchTreeChart,0);
				yiTreeBag.completeTerms.put(matchTreeChart.toString(), matchTreeChart);
				activateSubConstructions(matchTreeChart, sname);
			}
		}
		return true;
	}

	/**
	 * 激活下层构式
	 * @param sname 场景名称
	 * @param matchTreeChart 当前匹配的树图表
	 */
	private void activateSubConstructions(TreeChart matchTreeChart, String sname) {
		// 继续往下激活，中间层构式，直接用成分而不是词性，虽然词性与成分一一对应
		Node node = new NodeImpl(sname);
		// todo 直接搜，而不是pam扩散，有针对性，但丢了很多其他信息，如内涵等，节律也不够好
		// 多种结构和多种内涵时，需要判别筛选，如苹果。语法辅助（结构搭配）、内涵辅助（语义搭配）
		// 禁止性搭配，短路抑制机制，不能XX。先天规则层抑制、后天义法层抑制
		Set<Link> links01 = NeoUtil.getLinks(node);
		if (links01 != null && !links01.isEmpty()) {
			for (Link link : links01) {
				// 递归激活下层构式，暂时限制边类型arg
				if (link.getTNname().length() >= 3 && link.getTNname().substring(0,3).equals("arg")) {
					actsence(link, matchTreeChart);
				}
			}
		}
	}

	/**
	 * 处理插入图表时的嵌套构建逻辑
	 * @param parent 父链接
	 * @param insertChart 要插入的图表
	 * @param matchTreeChart 当前匹配的树图表
	 * @param isFind 是否找到匹配项
	 */
	private static String handleInsertChart(Link parent, TreeChart insertChart, boolean isFind, TreeChart matchTreeChart) {
		String sourceName = parent.getSource().getTNname();
		// 如果是正在find的构式，不是已有匹配完整嵌套构式，直接加入构建新嵌套
		if (isFind){
			// 用构式2整体替换foundList里的parent的source节点
			parent.setSource(insertChart);
			((LinkImpl) parent).init(((LinkImpl) parent).term);
			// 如果是已有匹配完整嵌套构式，先移除foundlist的对应部分，再加入构建新嵌套，保留已有的
			matchTreeChart.foundList.add((Term) parent);
			// 完成匹配链接总数
			matchTreeChart.cpsize = matchTreeChart.cpsize + insertChart.cpsize - 1;
		}
		return sourceName;
	}

	// todo 参与优先级排序，框架满足度*原词利用率，小框架容易满足，但没有用完整个原子词序
	private TreeChart numberTree(TreeChart treeChart, int i) {
		// 遍历chart里的foundlist链接，找到order为0的节点，即最左边节点，然后递归给子节点编号
		// foundList是Collection，不能直接用get，需要转成list
		List<Term> foundList0 = new ArrayList<>(treeChart.foundList);
		int num = i; // 从i开始编号，num是累积编号，也是当前编号
		for (int j = 0; j < foundList0.size(); j++) {
			// 找到order与j相等的节点
			// todo foundlist一开始就按order排序，不用遍历。另外有容错纠错需求，词序不对不一定错误
			for (Term term : foundList0) {
				Link link = (Link) term;
				if (Integer.valueOf((String) (link.getProperty("order"))) == j){
					// 如果链接的source节点为chart，则递归给chart的子节点编号
					if (link.getSource() instanceof TreeChart) {
						TreeChart treeChart1 = (TreeChart) link.getSource();
						treeChart1 = numberTree(treeChart1, num);
						num = treeChart1.cpsize + num;
					}else {
						((LinkImpl)link).nowOrder = num;
						// 除了递归编号，还递归提交到语义图理解模块，无论是否完全匹配整句，相当于渐进式逐步理解
						// link是arg结构，提交到边集，但不会提交到nars。点可提交到点集
						// todo 整合到pam
//						pam.getListener().receivePercept(link, ModuleName.UnderstandGraph);

						pam.getListener().receivePercept(link, ModuleName.CurrentSM);

						nar.memory.addDefaultNode(link.getSource());
						nar.memory.addDefaultLink(link);

						System.out.println("提交语义图理解模块：---------"+ link);
						num++;
					}
				}
			}
		}
		return treeChart;
	}

	private TreeChart bulidTree(Term sceneRoot, String rootName, Collection<Term> foundList,
								int lsize , List<Link> links00) {
		List<Term> findingList = new ArrayList<>();
		Term[] components = new Term[lsize];// 无关系元素集合，次序关系在元素属性里，可能有子节点
		Link ll;
		Map<Integer,String> orderMap = new HashMap();
		String subSceneStr = "";
		Object orderObj;
		// 图搜索得到的都是链接，暂不提取转term数组的方法，term数组还要遍历，这里现场实时遍历更便捷
		for (int i = 0; i < lsize; i++) {
			ll = links00.get(i);
			if (!ll.getTNname().startsWith("ar")) {
				continue;
			}
			components[i] = (Term) ll;    // 只是遍历次序，实际次序在元素属性里

//			pam.getListener().receivePercept(ll, ModuleName.GrammarGraph);

			pam.getListener().receivePercept(link, ModuleName.CurrentSM);

			nar.memory.addDefaultNode(link.getSource());
			nar.memory.addDefaultLink(link);

			if (!foundList.contains(ll)) {
				findingList.add((Term) ll); // 未激活的边，都放预测边集里
			}
//			if (ll.getSource() instanceof TreeChart){
//				subSceneStr = ((TreeChart) ll.getSource()).sceneStr;
//			}else {
//				subSceneStr = (String) ll.getSource().getProperty("name");
//			}
//			// 既有arg标号，也有order？
//			orderObj = ll.getProperty("order");
//			if(orderObj instanceof String){
//				orderMap.put(Integer.parseInt((String) orderObj), subSceneStr);
//			}else {
//				orderMap.put(((Long) orderObj).intValue(), subSceneStr);
//			}
		}
		StringBuffer buf = new StringBuffer();
		// 生成场景文本序列=产生式规则，场景为左部，构式其他成分为右部，要注意词语次序，有些框架本身带有嵌套
		// 两种方案，原始单产生式直存到场景节点；或实时生成。后者更好，因为存储费空间，而且不好改，再者嵌套需要实时生成
//		buf.append(rootName + "(");
//		for (int i = 0; i < lsize; i++) {
//			if(buf.length() > 8) buf.append(" , ");// 按字符数，非词数
//			buf.append(orderMap.get(i));
//		}
//		buf.append(")");

//		TreeChart treeChart = new TreeChart(new BudgetValue(0.99f, 0.1f, 0.1f, AgentStarter.nar.narParameters),
//				sceneRoot, components, foundList, findingList);

		Term conjunction = Conjunction.make(findingList, components, TemporalRules.ORDER_NONE, false);

		yiTreeBag.putBack((Conjunction) conjunction, 10f, nar.memory);

		return (TreeChart) conjunction;
	}
}

