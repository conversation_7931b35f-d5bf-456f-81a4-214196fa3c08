/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.nlanguage;

import com.warmer.kgmaker.KgmakerApplication;
import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.LinkImpl;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeImpl;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamImpl0;
import edu.memphis.ccrg.lida.pam.PamLink;
import edu.memphis.ccrg.lida.pam.tasks.ExcitationTask;
import edu.memphis.ccrg.lida.pam.tasks.PropagationTask;
import edu.memphis.ccrg.lida.workspace.WorkspaceContent;
import org.opennars.entity.BudgetValue;
import edu.memphis.ccrg.linars.Term;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;
import static edu.memphis.ccrg.lida.pam.PamImpl0.mmcache2;

/**
 * A task to add a {@link PamLink} and its sink to the percept.
 * 
 * <AUTHOR> J. McCall
 * @see ExcitationTask creates this task
 * @see PropagationTask creates this task
 */
public class GrammarAnalyzTask extends FrameworkTaskImpl {

	private PAMemory pam;
	private Link link;
	private WorkspaceContent listenNs;
	private WorkspaceContent yufaNs;
//	private WorkspaceContent yuyiNs;
	private String message;
	private String query;

	private List<Node> words = new ArrayList<>();
	Collection<Term> scenels;// 当前构式边的场景已激活的边集合
	int lsize;    // 对应场景总边数量属性
	int actlsize;// 场景已激活的边的数量
	int plsize;    // 场景已匹配的边的数量
	boolean isActEqu = false;// 是否已经激活完整
	boolean isMatchEqu = false;// 是否已经匹配完整
	Set<Link> links; //当前词的词性所在的所有构式边
	Node pos;

	// 记录构式根节点即可？每次都要到数据库中查询，如果用map，value是list也要遍历
	// 汉语逐词输入，英语等需处理字形，都是认知程序=图程？从图树构建的预测推理中，动态解决分词和字形，无需硬编码语言模块？
	// 分词和字形是同类问题？第三方母语者同时学汉语和英语，用的都是规则和概念，规则不同，但处理的过程是一样的
	// 方块字意义更紧凑，无需太多分词时的匹配尝试，英语等如果字母连在一起，分词更多，匹配尝试更多
	// 书面语规则不一样，但口语总要逐音输出，书面规则后于语法规则，语法包括结构和形态等

	// 多线程并发，不能在线程内定义变量，要全局共享
//	List<String> all_act_Scene;// 已经激活完整的构式，有可能是部分匹配的，部分是预测
//	List<String> some_act_Scene;// 已经激活部分的构式
//	List<String> all_p_Scene;// 已经匹配完整的构式，匹配完整的肯定是激活完整的
//	List<String> some_p_Scene;// 已经匹配部分的构式

	TreeBag faTreeBag;

	private AtomicBoolean started = new AtomicBoolean(false);

	boolean isAllMatch = false;
	boolean isAllAct = false;

	Set<String> mmcache0 = PamImpl0.gmcache;

	/**
	 * Default constructor
	 * @param link {@link PamLink}
	 * @param pam {@link PAMemory}
	 */
	public GrammarAnalyzTask(Link link, PAMemory pam) {
		super(1);
		this.pam = pam;
		this.link = link;
		// 内听觉，外听觉，内视觉，外视觉，内文本，外文本，至少6个语句分析来源，来源汇总区分，统一到符号语义
		// 语言高频处理，需要常驻？分别尝试下
		listenNs = pam.getWorkspaceBuffer("listen").getBufferContent(null);
		yufaNs = pam.getWorkspaceBuffer("yufa").getBufferContent(null);
//		yuyiNs = pam.getWorkspaceBuffer("yuyi").getBufferContent(null);
		// 专门的语法树集合？语义另外汇总？推理再另开？理解阶段和生成阶段，外界输入和思考输入，视听和文本
		// 语义要分开阶段？生成时，多句表达意图混杂，无法确定哪句，也排序竞争？
		// 语言语义，非语言语义，如看到苹果激活概念，但并非看听到的语言语义。
		// 婴儿学习时区分=分离抽取语言模式，狗都能抽取，说明不用特别对待，只是普通模式表达？
		// 代词推理，各种喻义推理，模糊语义语法歧义推理，语句意图推理，完成语句理解，然后根据语句含义继续推理，认知推理=如分析原因
		// 期间激活各种子动机，某些习惯性自动图程或规则，无需通达和动机。子动机可毫无关系，衡量胜任、成本、需求，完成后继续主动机
		// 扩散理解无需动机，报错和认知需求，如学习和解决问题，才需要动机。没有现成结构，无法扩散才要推理，要激活动机
		faTreeBag = ((ChartTreeSet)yufaNs).chartSet;
	}

	/**
	 * Adds link's sink to the percept and tries to add the link as well then finishes.
	 */
	@Override
	protected synchronized void runThisFrameworkTask() {
//		System.out.println("语义分析任务开始");
		// 不能多线程删除，否则会出现并发问题，删除时，其他线程可能正在遍历，导致遍历出错
		// 解决办法：1.加锁，2.不删除，只标记，3.不删除，只记录，4.不删除，只记录，但是记录的是下标，遍历时跳过
		// 采用加锁方案
		if (started.compareAndSet(false, true)) {
			if (KgmakerApplication.message.size() == 0 && mmcache0.size() == 0) {
				started.set(false);
				return;
			} else if (KgmakerApplication.message.size() > mmcache0.size()) {
				message = KgmakerApplication.message.get(mmcache0.size());
				System.out.println("语法未查看信息----+++++" + KgmakerApplication.message.get(mmcache0.size()));
//			} else if (KgmakerApplication.message.size() == mmcache0.size()) {
//				KgmakerApplication.message.clear();
//				PamImpl0.mmcache.clear();
//				mmcache0.clear();
//				mmcache2.clear();
//				treeBag.clear();
//				started.set(false);
//				return;
			} else {
				// 复杂情况，如语句并行，线程错误执行
				started.set(false);
				return;
			}
		}

		// todo 注意线程周期问题，每个词都一个线程+一个周期=真并行，需要改pam=pam集中分发改各线程并行集中调用
		// 这里是常驻线程运行与词语同等数量的周期，外周期=线程池周期调用，内周期=线程内部循环。无论内外，都要等待
		// 当前线程繁忙时，线程池invoke方法会等待上一个周期的任务执行完成后，再执行当前周期的任务，然后返回结果
		// join()的作用是：“等待该线程终止”，在子线程调用，也就是孙线程执行完，才继续执行子线程

		// 考虑衰减问题，不能总从ns查，有些是上周期的，有些则衰减完，没法参与本周期
		Node mm = listenNs.getNode(message);
		if (mm == null) {
//			words.add(mm); // 存入一个新序列
//		} else {
			started.set(false);
			return;
		}
		mmcache0.add(message + " - 1"); //new出来的都是放进程堆中，各线程共享的
		mmcache2.add(message);

		// 案例1：只有自负的人才能坚持研究颠覆性agi。案例2：描述计算15+8的过程
		// 案例3：“把把把把把把”是个东西，一把 把 把把把把把把 把住。需要明确自定义语义
		// 分析：分词问题，多词性问题，语义问题，语法问题。分词决定语义，语义决定词性，词性决定语法
		// 案例4：咬伤了猎人+的狗，咬伤了+猎人的狗，分词都是一样的，但语义不同，语法也不同。
		// 咬伤了+猎人的狗+的狗。咬伤了+咬伤了猎人+的狗。咬伤了+咬伤了猎人+的狗+的狗，咬伤了+咬伤了+猎人的狗+的狗。

		// 逐词从节点属性拿到词性，通过词性找到对应的构式，存buffer
		// 点投票模式，逐步动态在buffer里组装框架，而不是通过一条边查出所有关系
		pos = NeoUtil.getNode((String) mm.getProperty("pos"));
		//todo 有多种词性的情况，方向等问题
		links = NeoUtil.getLinks(pos);

//		System.out.println("语法分析任务线程 " + Thread.currentThread().getName());
//		System.out.println("mmcache 数量---------" + mmcache0.size());
//		System.out.println("：词性" + pos.getProperty("name") + "的构式边数" + links.size());

		if (mmcache0.size() == 0) {
			started.set(false);
			return;
		}
		if (links != null && links.size() > 0) {
			actsence(links);
		}
		started.set(false);

		// 开始尝试构建语法树+语义树，构式森林先汇总完毕，像漏斗，会有多个局部语法树，筛选要求：无歧义+最大覆盖+最小成本+最大概率


//		query = "match (n:{name:\'" + mm.getProperty("pos").toString() + "\'})-[r]-(m) return *";
//		query = "match (n:{name:\'"+ message +"\'})-[r]->() return r";
//		NeoUtil.addNodesAndRelsToBuffer(query, ModuleName.ListenGraph);

//		cancel();
	}

	private void actsence(Set<Link> links) {
		Node sink;
		String sname;
		String sinkId;// 场景id唯一，完整匹配有多种，只要有一种即可
//		TreeChart treeChart;
//		ArrayList<Term> findingList;
		Object size;
		for (Link parent : links) {
			sink = (Node) parent.getSink();
			sname = sink.getTNname();
			pam.getListener().receivePercept(parent, ModuleName.GrammarGraph);
			size = sink.getProperty("size");
			if(size instanceof String){
				lsize = Integer.parseInt((String) size);
			}else {
				lsize = ((Long) size).intValue();
			}
//			sinkId = String.valueOf(sink.getNodeId());

			boolean isAct = false;
			TreeChart matchTreeChart = null;
			// 遍历treebag里nameTable的keyset中，左括号前字符是否匹配sname，也就是是否已经有该sink，有则跳过，没有则加入
			for (String key : faTreeBag.nameTable.keySet()) {
				if (key.substring(0, key.indexOf("(")).equals(sname)) {
					matchTreeChart = faTreeBag.nameTable.get(key);
					boolean isFind = false;
					// 判断当前key的value里，findingList是否包含当前parent链接，有则从findingList里移除，在foundList里添加
					// 无论是否嵌套，是否有匹配完整的嵌套，一旦符合根节点和有find边，都移除。是嵌套构式，要在parent替换整体子构式前移除
					if (matchTreeChart.findingList.contains(parent)) {
						matchTreeChart.findingList.remove(parent);
						isFind = true;
					}

					String sourceName = parent.getSource().getTNname();
					String sourceName2 = sourceName.substring(sourceName.indexOf("_") + 1);
					boolean isFound = false;
					// 如果parent的source头节点，name的“_”字符后为双字符，说明是中间层构式，在未匹配完整时就要开始嵌套构建
					if (sourceName2.length() >= 2 && faTreeBag.completeTerms != null) {
						// 也可根据激活来源，匹配完整时直接把构式传下来匹配。不过其他中间构式还是要遍历匹配完整的构式，数量不多干脆全用遍历
						for (String key2 : faTreeBag.completeTerms.keySet()) {
							String key2sub = key2.substring(0, key.indexOf("("))
									.substring(sname.indexOf("_") + 1);
							// 在匹配完整的构式里找到根节点匹配此双字符的构式2
							if (key2sub.equals(sourceName2)) {
								TreeChart matchTreeChart2 = faTreeBag.completeTerms.get(key2);

								if (isFind){
									// 用构式2整体替换foundList里的parent的source节点
									parent.setSource(matchTreeChart2);
									((LinkImpl)parent).init(((LinkImpl)parent).term);
									// 如果是已有匹配完整嵌套构式，先移除foundlist的对应部分，再加入构建新嵌套，保留已有的
									matchTreeChart.foundList.add((Term) parent);
								}

								Collection<Term> foundcopy = new ArrayList<>();
								foundcopy.addAll(matchTreeChart.foundList);
								// 如果是正在find的构式，不是已有匹配完整嵌套构式，直接加入构建新嵌套
								if (!isFind) {
									for (Term link : foundcopy) {
										Node ss = ((Link)link).getSource();
										String rr ;
										if(ss instanceof TreeChart){
											rr = ((TreeChart) ss).sceneRoot.getTNname();
										}else {
											rr = ss.getTNname();
										}
										// 无论是否已嵌套，只要找到匹配的，就替换
										if (rr.substring(sname.indexOf("_") + 1).equals(key2sub)) {
											isFound = true;
											((Link) link).setSource(matchTreeChart2);
											((LinkImpl)link).init(((LinkImpl)link).term);
	//										matchTreeChart.foundList = foundcopy;	// 替换后更新foundList
											break;
										}
									}
								}

								// 将foundList和findingList里的链接整合为新list
								List<Link> links2 = new ArrayList<>();
								for (Term link : matchTreeChart.foundList) {
									links2.add((Link) link);
								}
								for (Term link : matchTreeChart.findingList) {
									links2.add((Link) link);
								}

								NodeImpl root = (NodeImpl) matchTreeChart.sceneRoot;
								// 然后重新组装并更新matchTreeChart的sceneTerm
								matchTreeChart = bulidTree(root, root.getTNname(), foundcopy, matchTreeChart.foundList.size(), links2);
								matchTreeChart.init(matchTreeChart.term);
//								break; // 匹配替换一个即可，即使还有其他匹配，也不需要再继续，比如vv？
							}
						}
					}

					// 上面加了，这里就不用加了。如已有完整嵌套构式，也要加入foundList，把已匹配的对应部分替换成parent
					if (!matchTreeChart.foundList.contains(parent) && !isFound) {
						matchTreeChart.foundList.add((Term) parent);
					}

					// 如果findingList为空，说明该构式已经完整匹配，加入treebag的完整构式列表
					if (matchTreeChart.findingList.size() == 0) {
						// 不能无限嵌套，复杂度不能大于句子长度
						if (matchTreeChart.complexity < KgmakerApplication.message.size() * 6){
							faTreeBag.completeTerms.put(matchTreeChart.toString(),matchTreeChart);
							// 继续往下激活，中间层构式，直接用成分而不是词性，虽然词性与成分一一对应
							String noden = "pos_" + sname.substring(sname.indexOf("_") + 1);
							Node node = new NodeImpl(noden);
							Set<Link> links01 = NeoUtil.getLinks(node);
							if (links01 != null && links01.size() > 0) {
								actsence(links01);
							}
						}
					}
//					}
					isAct = true;
					break; // 构式根节点都唯一，找到一个就跳出
				}
			}
			// 根据根节点sink判断buffer里是否有两条边或以上构式，有则尝试构建语法树
			// 第一个词通常无完整构式，也有可能首词非句首=语句被截断，乱序输入等，越靠近句首=构式角色越靠前
			if (!isAct) {
				// 未全激活，加入新链接后，看是否完整激活
				Collection<Term> scenels = ((ChartTreeSet)yufaNs).getLinksOfSinkT(sname);
				actlsize = scenels.size();

				// 一个词也能匹配单构式？只有单个词如感叹+拟声等，连词=但和与？np=n等也是单构式
				if ((double) actlsize / lsize >= 0.5) {
					// 如果构式总边数与已激活边数达到一半，则直接激活整个框架为完整构式，优先级最高，放进确定集合里
					// 未匹配的可当预测，也可先不管，不完整构式不好嵌套，完整的嵌套能一次成型，无需每次更新总构式，再有新嵌套直接拼接
					Set<Link> links0 = NeoUtil.getSomeLinks(sink,null, "<", null, null);
					List<Link> links00 = new ArrayList<>(links0);

					bulidTree((Term) sink, sname, scenels, lsize, links00);
				}

				// 已全激活，看是否匹配完整
			}

			if (actlsize > 0) {

				//不完全匹配则往下预测

				// 如果替换词性为词语后的场景，能适配查到的蕴含树，那可能性更高，需要先查蕴含树，一般两两组合查询
				// 如果后续有一环不匹配（处理完及未处理完），需要抛出疑问或回溯重新构建
				// 回溯时=内在回溯+外在重读语句，需要记录已经匹配的边，避免重复匹配，仍无法理解则需激活学习动机
				// 不完整构式，已匹配部分能适配蕴含树，则该构式未匹配部分作为预测，放进预测集合里
				// 能自动修正局部错误，如词序错位，可能是因为往返扫描+不严格限制词序+无意识修正+浅推理修正
				// 结合意识注意+感知运动，有意控制读取位置+方向+速度+范围等，缺少信息（字面+知识）或有意确认时

			}

		}
	}

	private TreeChart bulidTree(Term sceneRoot, String rootName, Collection<Term> foundList,
						   int lsize , List<Link> links00) {
		List<Term> findingList = new ArrayList<>();
		Term[] components = new Term[lsize];
		Link ll;
		Map<Integer,String> orderMap = new HashMap();
		int order;
		if(links00.size() != lsize){
			order = links00.size();
		}else {
			order = lsize;
		}
		String subSceneStr = "";
		Object orderObj;
		for (int i = 0; i < order; i++) {
			ll = links00.get(i);
			components[i] = (Term) ll;    // 只是遍历次序，实际次序在元素属性里
			pam.getListener().receivePercept(ll, ModuleName.GrammarGraph);
			if (!foundList.contains(ll)) {
				findingList.add((Term) ll); // 未激活的边，都放预测边集里
			}
//			if (ll.getSource() instanceof TreeChart){
//				subSceneStr = ((TreeChart) ll.getSource()).sceneStr;
//			}else {
//				subSceneStr = (String) ll.getSource().getProperty("name");
//			}
//			orderObj = ll.getProperty("order");
//			if(orderObj instanceof String){
//				orderMap.put(Integer.parseInt((String) orderObj), subSceneStr);
//			}else {
//				orderMap.put(((Long) orderObj).intValue(), subSceneStr);
//			}
		}
		StringBuffer buf = new StringBuffer();
		// 生成场景文本序列=产生式规则，场景为左部，构式其他成分为右部，要注意词语次序，有些框架本身带有嵌套
		// 两种方案，原始单产生式直存到场景节点；或实时生成。后者更好，因为存储费空间，而且不好改，再者嵌套需要实时生成
//		buf.append(rootName + "(");
//		for (int i = 0; i < lsize; i++) {
//			if(buf.length() > 8) buf.append(" , ");// 按字符数，非词数
//			buf.append(orderMap.get(i));
//		}
//		buf.append(")");

//		Term sceneTerm = new Product(components); // 无关系元素集合，次序关系在元素属性里，可能有子节点

		TreeChart treeChart = new TreeChart(new BudgetValue(0.99f, 0.1f, 0.1f, AgentStarter.nar.narParameters),
				sceneRoot, components, foundList, findingList);

		faTreeBag.putBack(treeChart, 10f, nar.memory);

		return treeChart;
	}
}

