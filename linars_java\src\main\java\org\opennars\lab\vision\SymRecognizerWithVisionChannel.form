<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
    <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
      <Color blue="0" green="cc" red="66" type="rgb"/>
    </Property>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" max="-2" attributes="0">
                  <Component id="jScrollPane1" alignment="0" max="32767" attributes="0"/>
                  <Component id="jScrollPane3" alignment="0" max="32767" attributes="0"/>
                  <Component id="jLabel5" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Group type="102" alignment="0" attributes="0">
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="jLabel3" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel1" alignment="0" min="-2" pref="200" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="estimate" min="-2" pref="200" max="-2" attributes="0"/>
                          <Component id="jLabel2" min="-2" max="-2" attributes="0"/>
                      </Group>
                  </Group>
                  <Group type="102" alignment="0" attributes="0">
                      <Component id="jButton2" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="addPatternButton" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="jButton3" max="32767" attributes="0"/>
                  </Group>
                  <Group type="102" attributes="0">
                      <Component id="jLabel4" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="32767" attributes="0"/>
                      <Component id="invar1" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="invar" min="-2" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel2" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel3" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="estimate" alignment="3" min="-2" pref="200" max="-2" attributes="0"/>
                  <Component id="jLabel1" alignment="3" min="-2" pref="200" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jButton2" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="addPatternButton" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jButton3" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel4" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="invar" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="invar1" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jScrollPane1" min="-2" pref="69" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jLabel5" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="32767" attributes="0"/>
              <Component id="jScrollPane3" min="-2" pref="69" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="33" green="33" red="ff" type="rgb"/>
        </Property>
        <Property name="text" type="java.lang.String" value="jLabel1"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JButton" name="jButton2">
      <Properties>
        <Property name="text" type="java.lang.String" value="Clear"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jButton2ActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="jButton3">
      <Properties>
        <Property name="actionCommand" type="java.lang.String" value="Determine most similar saved pattern"/>
        <Property name="label" type="java.lang.String" value="Determine most similar saved pattern"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jButton3ActionPerformed"/>
      </Events>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTextPane" name="inputPanel">
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="text" type="java.lang.String" value="Saved patterns"/>
      </Properties>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane3">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTextPane" name="inputPanel2">
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="estimate">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="33" green="33" red="ff" type="rgb"/>
        </Property>
        <Property name="text" type="java.lang.String" value="jLabel1"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JButton" name="addPatternButton">
      <Properties>
        <Property name="text" type="java.lang.String" value="Save pattern"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="addPatternButtonActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="text" type="java.lang.String" value="Input"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel4">
      <Properties>
        <Property name="text" type="java.lang.String" value="Input"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel5">
      <Properties>
        <Property name="text" type="java.lang.String" value="Question"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JCheckBox" name="invar">
      <Properties>
        <Property name="actionCommand" type="java.lang.String" value="Assume translation invariance"/>
        <Property name="label" type="java.lang.String" value="Assume translation invariance"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JCheckBox" name="invar1">
      <Properties>
        <Property name="text" type="java.lang.String" value="Show reasoner GUI"/>
        <Property name="actionCommand" type="java.lang.String" value="Assume translation invariance"/>
      </Properties>
    </Component>
  </SubComponents>
</Form>
