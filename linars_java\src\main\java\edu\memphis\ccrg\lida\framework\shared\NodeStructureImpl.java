/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.framework.shared;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.shared.activation.Activatible;
import edu.memphis.ccrg.lida.framework.shared.scene.SSite;
import edu.memphis.ccrg.lida.framework.shared.scene.SceneStructure;
import edu.memphis.ccrg.lida.framework.shared.scene.WorldStructure;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.globalworkspace.BroadcastContent;
import edu.memphis.ccrg.lida.nlanguage.TreeChart;
import edu.memphis.ccrg.lida.pam.PamLink;
import edu.memphis.ccrg.lida.pam.PamNode;
import edu.memphis.ccrg.lida.proceduralmemory.ProceduralMemoryImpl;
import edu.memphis.ccrg.lida.proceduralmemory.Scheme;
import edu.memphis.ccrg.lida.workspace.WorkspaceContent;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Default implementation of {@link NodeStructure}. The source and sink of a
 * link must be present before it can be added. Links can connect two nodes
 * (simple link) or can connect a node and another SIMPLE link. Nodes and links
 * are copied when added. This prevents having the same node (object) in two
 * different NodeStructures.
 *
 * NodeStructure 的默认实现。链接的源和接收器必须存在才能添加。链路可以连接两个节点（简单链路），
 * 也可以连接一个节点和另一个 SIMPLE 链路。添加时会复制节点和链接。这可以防止在两个不同的
 * NodeStructures 中具有相同的节点（对象）。
 *
 * <AUTHOR> Snaider
 * <AUTHOR> J. McCall
 * <AUTHOR> Dong
 * <AUTHOR> Agrawal
 * @see ExtendedId
 */
public class NodeStructureImpl implements BroadcastContent, WorkspaceContent{
	private static final Logger logger = Logger
			.getLogger(NodeStructureImpl.class.getCanonicalName());

	/**
	 * Standard factory for new objects. Used to create copies when adding
	 * linkables to this NodeStructure
	 */
	private static ElementFactory factory = ElementFactory.getInstance();	
	
	/**
	 * Nodes contained in this NodeStructure indexed by their id 包含的节点按其ID索引
	 * 用于罗列能可视化的点边，无论点属于哪个属性对象，可能是场景等。场景里有副本，总子图里也有副本
	 */
	private ConcurrentMap<Integer, Node> nodes = new ConcurrentHashMap<Integer, Node>();

	/**
	 * Links contained in this NodeStructure indexed by their id String.
	 */
	private ConcurrentMap<ExtendedId, Link> links = new ConcurrentHashMap<ExtendedId, Link>();

	/**
	 * Links that each Linkable (Node or Link) has.
	 */
	private ConcurrentMap<Linkable, Set<Link>> linkableMap = new ConcurrentHashMap<Linkable, Set<Link>>();

	// todo 增加子图集合？子图模式目的是便捷找到所需模式，当前或高频模式，衰减根据整体性？
	// linkableMap就是子图？no，只是包含子图，非全子图。无父子关系。平面和父子结构同时存在？冗余表征缓存
	// 向下和向上找模式，模式内同比例提高激活？等值提高？提高到同一激活？
	// 所有场景等虚点先汇集，按需激活整体模式，不用上来就激活整体。框架模式等可实时查找，也可预先构建缓存，难实时的可缓存
	// nars复合词项就是一个整体模式，完整但体量小，模式既要拆分也要整体形态，场景虚拟点与整体模式对等，nars无场景点

	// todo 增加场景属性？场景是子图？比平面结构多了层级？需要父子等关系？直接构建父子，还是从平面计算出？
	private SceneStructure sceneStructure = new SceneStructure();

	// 世界环境、公理体系，区别独立于场景
	private WorldStructure worldStructure = new WorldStructure();

	private int mainNodeId = 0;

	private List<Link> doMainPath_list = new ArrayList<Link> ();
	private Map<String, List<Link>> doMainPath_map = new HashMap<>();
	private Map<String, List<Link>> createMainPath_map = new HashMap<>();
	private List<Link> createMainPath = new ArrayList<Link> ();

	private long broadSceneCount = 0;

	private Map mainMap = new ConcurrentHashMap<Object,Object>();

	@Override
	public int getMainNodeId() {
		return mainNodeId;
	}
	@Override
	public void setMainNodeId(int mainNodeId) {
		this.mainNodeId = mainNodeId;
	}

	@Override
	public List<Link> getDoMainPath() {
		return doMainPath_list;
	}

	@Override
	public void setDoMainPath(List<Link> mainPath) {
		this.doMainPath_list = mainPath;
	}
	@Override
	public Map<String, List<Link>> getDoMainPath_map() {
		return doMainPath_map;
	}
	@Override
	public void setDoMainPath_map(Map<String, List<Link>> doMainPath_map) {
		this.doMainPath_map = doMainPath_map;
	}

	@Override
	public List<Link> getCreateMainPath() {
		return createMainPath;
	}
	@Override
	public void setCreateMainPath(List<Link> createMainPath) {
		this.createMainPath = createMainPath;
	}

	@Override
	public Map getMainMap() {
		return mainMap;
	}

	@Override
	public void setMainMap(Map mainMap) {
		this.mainMap = mainMap;
	}

	@Override
	public long getBroadSceneCount() {
		return broadSceneCount;
	}
	@Override
	public void setBroadSceneCount(long broadSceneCount) {
		this.broadSceneCount = broadSceneCount;
	}

	/**
	 * Default Node type used.
	 */
	private String defaultNodeType;

	/**
	 * Default Link type used.
	 */
	private String defaultLinkType;

	/**
	 * Default constructor. Uses the default node and link types of the factory
	 */
	public NodeStructureImpl() {
		defaultNodeType = factory.getDefaultNodeType();
//		defaultNodeType = "PamNodeImpl";
//		defaultLinkType = "PamLinkImpl";
		defaultLinkType = factory.getDefaultLinkType();
	}

	/**
	 * Creates a new NodeStructureImpl with specified default Node type and link
	 * Type. If either is not in the factory the factory's defaults are used.
	 * 
	 * @param nodeType kind of node used in this NodeStructure
	 * 
	 * @param linkType kind of link used in this NodeStructure
	 * 
	 * @see ElementFactory
	 */
	public NodeStructureImpl(String nodeType, String linkType) {
		this();
//		if (factory.containsNodeType(nodeType)) {
			defaultNodeType = nodeType;
//		} else {
//			logger.log(Level.SEVERE, "Unsupported Node type: {1}",
//					new Object[]{TaskManager.getCurrentTick(),nodeType});
//			throw new IllegalArgumentException();
//		}

//		if (factory.containsLinkType(linkType)) {
			defaultLinkType = linkType;
//		} else {
//			logger.log(Level.SEVERE, "Unsupported Link type: {1}",
//					new Object[]{TaskManager.getCurrentTick(),linkType});
//			throw new IllegalArgumentException();
//		}
	}

	/**
	 * Copy constructor. Specifies Node and Link types used to copy Node and
	 * Links. Specified types are the default types for the copy.
	 * 
	 * @param ns
	 *            original NodeStructure
	 * @see #mergeWith(NodeStructure)
	 */
	public NodeStructureImpl(NodeStructure ns) {
		this(ns.getDefaultNodeType(), ns.getDefaultLinkType());
		internalMerge(ns);
		this.sceneStructure.setsSite(ns.getSceneSite());
		this.sceneStructure.setsTime(String.valueOf(TaskManager.getCurrentTick()));
	}
	
	@Override
	public Node addDefaultNode(Node n) {
		return addNode(n, "PamNodeImpl");
	}

	@Override
	public Collection<Node> addDefaultNodes(Collection<Node> nodes) {
		if (nodes == null) {
			logger.log(Level.WARNING,
					"Cannot add nodes. Node collection is null", TaskManager
							.getCurrentTick());
			return null;
		}
		Collection<Node> storedNodes = new ArrayList<Node>();
		for (Node n : nodes) {
			if(n == null){
				continue;
			}
			Node stored = addNode(n, "PamNodeImpl");
			storedNodes.add(stored);
		}
		return storedNodes;
	}

	@Override
	public synchronized Node addNode(Node n, String type) {
		if (n == null) {
			logger.log(Level.WARNING, "Cannot add null Node.", TaskManager
					.getCurrentTick());
			return null;
		}
		if (!factory.containsNodeType(type)) {
			logger.log(Level.WARNING,
							"Factory does not contain node type {1}. Check that type is defined in factoryData.xml. Node {2} not added",
							new Object[]{TaskManager.getCurrentTick(),type,n});
			return null;
		}
		Node node = nodes.get(n.getNodeId());
//		Node node = getNeoNode(n.getName());
//		Node node = getNewNode(n, type);
		if (node == null) {
			// 复制 = 将来源节点的数值复制到当前图节点，当前有对应节点则改数值即可，
			// id一样地址不一样，地址一样就会同进退，只同id不会
			node = getNewNode(n, type);	//Calls #updateNodeValues
			if (node != null) {
				nodes.put(node.getNodeId(), node);
				linkableMap.put(node, new HashSet<Link>());
//			} else {
//				logger.log(Level.WARNING, "Could not create new node of type: {1} ",
//						new Object[]{TaskManager.getCurrentTick(),type});
			}
//		} else if(type.equals(node.getFactoryType())){
		} else if(node.getNodeId() != -1){

			if(node.getFactoryType() == null || !node.getFactoryType().equals(type)){
				node = getNewNode(n, type);
				if (node != null) {
					nodes.put(node.getNodeId(), node);
					linkableMap.put(node, new HashSet<Link>());
				}
			}

			if (node.getActivation() < n.getActivation()) {
				node.setActivation(n.getActivation());
			}
			if(n.getTruth() != 0){
				node.setTruth(n.getTruth());
			}
			if (n.getIncentiveSalience() != 0){
				node.setIncentiveSalience(n.getIncentiveSalience());
			}else {
//				System.out.println("激励为零---------------" + n.getName());
			}

			node.setLastAct(n.getLastAct());
			node.setBcastid(n.getBcastid());

			node.setFromsceneid(n.getFromsceneid());
			node.setFromnodeid(n.getFromnodeid());
			node.setFromLinkType(n.getFromLinkType());

			node.setDoneNum(n.getDoneNum());

			node.updateNodeValues(n);
		} else {
			logger.log(Level.WARNING,
							"Cannot add Node {1} of type {2} because another Node {3} having a different type {4} " +
									"and the same id is already present. Existing Node returned.",
							new Object[] { TaskManager.getCurrentTick(), n,
									type, node, node.getFactoryType() });
			node = null;
			throw new IllegalArgumentException();
		}
		return node;
	}
	
	@Override
	public synchronized Node addDefaultNode(String label, double a, double rt){
		return addNode("PamNodeImpl", label, a, rt);
	}
	
	@Override
	public synchronized Node addNode(String type, String label, double a, double rt) {
		Node n = factory.getNode("PamNodeImpl", null, type);
		if(n != null){
			n.setNodeName(label);
			n.setActivation(a);
			n.setActivatibleRemovalThreshold(rt);
			nodes.put(n.getNodeId(), n);
			linkableMap.put(n, new HashSet<Link>());
		}
		return n;	
	}

	/**
	 * If copy is false, this method adds a already generated {@link Node} to
	 * this NodeStructure without copying it. If copy is true,
	 * {@link NodeStructure#addDefaultNode(Node)} is used. If a Node with the
	 * same id is already in this NodeStructure, the new Node is not added.
	 * 
	 * This method is intended for internal use only. 
	 * @param n the Node to add
	 * @param shouldCopy determines if the node is copied or not.
	 * @return The Node stored in this NodeStructure
	 */
	protected Node addNode(Node n, boolean shouldCopy){
		if(shouldCopy){
			// pam为true，更新数值，pm的buffer为false，不更新数值
			// 复制 = 更新某些数值 = 新数值复制到本图里，不是复制为一个新点边
			return addDefaultNode(n);
		}else if (n == null){
			logger.log(Level.WARNING, "Cannot add null Node.", TaskManager
					.getCurrentTick());
			return null;
		}else{
			Node node = nodes.get(n.getNodeId());
			if (node == null) {
					node = n;
					nodes.put(node.getNodeId(), node);
					linkableMap.put(node, new HashSet<Link>());
			} else {
				logger.log(Level.FINE,
						"Cannot add node, it is already in this NodeStructure.", TaskManager.getCurrentTick());
			}
			return node;
		}
	}

	/**
	 * This method can be overwritten to customize the Node Creation.
	 * This implementation returns a new {@link Node} of specified type that is 
	 * compatible in the NodeStrucutreImpl. If an original node is specified the 
	 * new Node will copy the relevant attributes of the original. 
	 * 
	 * @param oNode
	 *            The original Node or null
	 * @param desiredType
	 *            the {@link ElementFactory} name of the desired node type
	 * @return a new Node if is no original was specified, or a copy of the
	 *         specified original node
	 * @see ElementFactory#getNode(String, Node, String)
	 */
	protected Node getNewNode(Node oNode, String desiredType) {
		return factory.getNode("PamNodeImpl", oNode, desiredType);
	}
	
	@Override
	public synchronized Link addDefaultLink(Link l) {
		return addLink(l, defaultLinkType);
	}

	@Override
	public Collection<Link> addDefaultLinks(Collection<Link> links) {
		if (links == null) {
			logger.log(Level.WARNING,
					"Cannot add links. Link collection is null", TaskManager
							.getCurrentTick());
			return null;
		}
		Collection<Link> storedLinks = new ArrayList<Link>();
		// Add simple links
		for (Link l : links) {
			if(l == null){
				continue;
			}
			if (l.isSimpleLink()) {
				Link addedLink = addDefaultLink(l);
				if(addedLink != null){
					storedLinks.add(addedLink);
				}
			}
		}
		// Add complex links
		for (Link l : links) {
			if(l == null){
				continue;
			}
			if (l.isSimpleLink() == false) {
				Link addedLink = addDefaultLink(l);
				if(addedLink != null){
					storedLinks.add(addedLink);
				}
			}
		}
		return storedLinks;
	}
	
	@Override
	public synchronized Link addLink(Link l, String type) {
//		if (!factory.containsLinkType(type)) {
//			logger.log(Level.WARNING,
//							"Cannot add link {1} of type {2} because factory does not contain that " +
//									"Link type. Check that type is defined in factoryData xml file.",
//							new Object[]{TaskManager.getCurrentTick(),l,type});
//			return null;
//		}

		if (!isLinkValid(l)) {
			return null;
		}

		double newActivation = l.getActivation();
		Link link = links.get(l.getExtendedId());
		if (link == null) {
			Node source = l.getSource();
			Node newSource = nodes.get(source.getNodeId());
			Linkable sink = l.getSink();
			Linkable newSink = null;
			if (sink instanceof Node) {
				Node snode = (Node) sink;
				newSink = nodes.get(snode.getNodeId());
			} else {
				System.out.println("sink 居然是 link ------!!!!");
				newSink = links.get(sink.getExtendedId());
			}

			if (newSource == null) {
				newSource = source;
				nodes.put(source.getNodeId(),source);
			}
			if (newSink == null) {
				newSink = sink;
				nodes.put(((Node)l.getSink()).getNodeId(), (Node) sink);
				links.put(sink.getExtendedId(),l);
			}
			// 将点集的点副本传下去，属性与点集同步，而非边集，如虚实属性
			link = generateNewLink(l, type, newSource, newSink,
					l.getCategory(), newActivation,
					l.getActivatibleRemovalThreshold(),
					l.getGroundingPamLink());//Calls #updateLinkValues
		} else
//			if (type.equals(link.getFactoryType()))
			{
			if (newActivation > link.getActivation()) {
				link.setActivation(newActivation);
			}
			link.updateLinkValues(l);
			link.getSink().setIncentiveSalience(l.getSink().getIncentiveSalience());
			link.setIncentiveSalience(l.getIncentiveSalience());
//		} else {
//			logger.log(Level.WARNING,
//							"Cannot add Link {1} of type {2} because another Link {3} having a different type {4} and" +
//									" the same id is already present. Existing Link returned.",
//							new Object[] { TaskManager.getCurrentTick(), l,
//									type, link, link.getFactoryType() });
//			link = null;
		}
		return link;
	}

	@Override
	public synchronized Link addDefaultLink(Node source, Linkable sink,
			LinkCategory category, double activation, double removalThreshold) {
		if(source == null) {
			logger.log(Level.WARNING,
					"Cannot add link because source is null", TaskManager
							.getCurrentTick());
			return null;
		}
		if(sink == null){
			logger.log(Level.WARNING,
					"Cannot add link because sink is null", TaskManager
							.getCurrentTick());
			return null;
		}

		Link link = addDefaultLink(source.getNodeId(), sink.getExtendedId(), category,
				activation, removalThreshold);
		if(link == null){
			System.out.println("------------------------- addDefaultLink == null");
		}
		Node isasource = link.getSource();
		Node isasink = (Node) link.getSink();
		if(isasource.getActivation() < source.getActivation()){
			isasource.setActivation(source.getActivation());
		}
		if(isasink.getActivation() < sink.getActivation()){
			isasink.setActivation(sink.getActivation());
		}

		return link;
	}

	@Override
	public synchronized Link addDefaultLink(int sourceId, ExtendedId sinkId,
			LinkCategory category, double activation, double removalThreshold) {
		return addLink(defaultLinkType, sourceId, sinkId, category, activation,
				removalThreshold);
	}

	@Override
	public synchronized Link addDefaultLink(int sourceId, int sinkId,
			LinkCategory cat, double activation, double removalThreshold) {
		if(cat == null){
			logger.log(Level.WARNING,
					"Cannot add new link because category is null.",
					TaskManager.getCurrentTick());
			return null;
		}
//		if(!isConnectionValid(sourceId, new ExtendedId(sinkId))){
//			return null;
//		}
		Node source = getNode(sourceId);
		Linkable sink = getNode(sinkId);
		ExtendedId newLinkId = new ExtendedId(sourceId, sink.getExtendedId(),
				cat.getNodeId());
		Link link = getLink(newLinkId);
		if (link == null) {
			link = generateNewLink(null, defaultLinkType, source, sink, cat,
					activation, removalThreshold, null);
		}else if(activation > link.getActivation()){
			link.setActivation(activation);
		}
		return link;
	}
	
	@Override
	public synchronized Link addLink(String type, Node src, Linkable sink,
			LinkCategory cat, double a, double rt) {
		if (src == null) {
			logger.log(Level.WARNING, "Cannot add link because source is null",
					TaskManager.getCurrentTick());
			return null;
		}
		if(sink == null){
			logger.log(Level.WARNING,
					"Cannot add link because sink is null", TaskManager
							.getCurrentTick());
			return null;	
		}
		return addLink(type, src.getNodeId(), sink.getExtendedId(), cat, a, rt);
	}
	
	@Override
	public synchronized Link addLink(String type, int srcId, ExtendedId snkId,
			LinkCategory cat, double a, double rt){
//		if (!factory.containsLinkType(type)) {
//			logger.log(Level.WARNING,
//							"Cannot add new link of type {2} because factory does not contain that " +
//									"Link type. Check that type is defined in factoryData xml file.",
//							new Object[]{TaskManager.getCurrentTick(),type});
//			return null;
//		}
		if(cat == null){
			logger.log(Level.WARNING,
					"Cannot add new link because category is null.",TaskManager.getCurrentTick());
			return null;
		}
//		if(!isConnectionValid(srcId, snkId)){
//			return null;
//		}
		ExtendedId newLinkId = new ExtendedId(srcId, snkId, cat.getNodeId());
		Link link = getLink(newLinkId);
		if (link == null) {
			Node source = getNode(srcId);
			Linkable sink = getLinkable(snkId);
			link = generateNewLink(null, type, source, sink, cat,
					a, rt, null);
		}else if(type.equals(link.getFactoryType())){ 
			if(a > link.getActivation()){
				link.setActivation(a);
			}
		}else{
			logger.log(Level.WARNING, "Cannot add new Link of type {2} because another Link {3} having a " +
							"different type {4} and the same id is already present. Existing Link returned.",
					new Object[]{TaskManager.getCurrentTick(),type,link,link.getFactoryType()});
			link = null;
		}
		return link;
	}
	
	/*
	 * Returns true if Link l can currently be added to the NodeStructure.
	 * Calls isConnectionValid().
	 * @see #isConnectionValid
	 */
	private boolean isLinkValid(Link l){
		if (l == null) {
			logger.log(Level.WARNING, "Cannot add null", TaskManager
					.getCurrentTick());
			return false;
		}
		Node src = l.getSource();
		Linkable sink = l.getSink();
		if(src == null){
			logger.log(Level.WARNING,
					"Cannot add Link, its source is null.",
					TaskManager.getCurrentTick());
			return false;
		}
		if(sink == null){
			logger.log(Level.WARNING,
					"Cannot add Link, its sink is null.",
					TaskManager.getCurrentTick());
			return false;
		}
//		return isConnectionValid(src.getNodeId(), sink.getExtendedId());
		return true;
	}
	
	/*
	 * Returns true if a Link from specified source to specified sink can be added
	 * 如果可以添加从指定源到指定接收器的链接，则返回true
	 */
	private boolean isConnectionValid(int srcId, ExtendedId sinkId){
		if (!containsNode(srcId)) {
			logger.log(Level.WARNING,
					"Cannot add Link because its source is not present in this NodeStructure",
					TaskManager.getCurrentTick());
			return false;
		}
		if (!containsLinkable(sinkId)) {
			logger.log(Level.WARNING,
					"Cannot add Link because its sink is not present in this NodeStructure",
					TaskManager.getCurrentTick());
			return false;
		}
		if(sinkId.isComplexLink()){
			logger.log(Level.WARNING,
					"Cannot add Link because its sink is a complex Link, sinks must be a Node or simple Link.",
					TaskManager.getCurrentTick());
			return false;
		}
		if (sinkId.isNodeId() && sinkId.getSourceNodeId() == srcId) {
			logger.log(Level.WARNING,
					"Cannot add Link with the same source and sink.",
					TaskManager.getCurrentTick());
			return false;
		}
		return true;
	}

	/*
	 * Generates a new Link with specified type and values.
	 */
	private Link generateNewLink(Link link, String linkType, Node newSource,
			Linkable newSink, LinkCategory category, double activation,
			double removalThreshold, PamLink groundingLink) {
		Link newLink = getNewLink(link, linkType, newSource, newSink, category);
		if(newLink != null){
			// set values of passed in parameters not handled by 'getNewLink'
			newLink.setActivation(activation);
			newLink.setActivatibleRemovalThreshold(removalThreshold);
			if(groundingLink != null){
				newLink.setGroundingPamLink(groundingLink);
			}
	
			links.put(newLink.getExtendedId(), newLink);
			if (!linkableMap.containsKey(newLink)) {
				linkableMap.put(newLink, new HashSet<Link>());
			}
	
			Set<Link> tempLinks = linkableMap.get(newSource);
			if (tempLinks == null) {
				tempLinks = new HashSet<Link>();
				linkableMap.put(newSource, tempLinks);
			}
			tempLinks.add(newLink);
	
			tempLinks = linkableMap.get(newSink);
			if (tempLinks == null) {
				tempLinks = new HashSet<Link>();
				linkableMap.put(newSink, tempLinks);
			}
			tempLinks.add(newLink);
		}else{
			logger.log(Level.WARNING, "Could not create new link of type: {1} ",
					new Object[]{TaskManager.getCurrentTick(),linkType});
		}
		return newLink;
	}

	/**
	 * This method can be overridden to customize the Link Creation. some of
	 * the parameter could be redundant in some cases.
	 * 
	 * @param oLink
	 *            original {@link Link}
	 * @param newType the {@link ElementFactory} name of the new {@link Link} type
	 * @param src
	 *            The new source
	 * @param snk
	 *            The new sink
	 * @param cat
	 *            the type of the link
	 * 
	 * @return The link to be used in this NodeStructure
	 */
	protected Link getNewLink(Link oLink, String newType, Node src,
			Linkable snk, LinkCategory cat) {
		Link newLink;
		if (oLink == null) {
			newLink = factory.getLink(defaultLinkType, newType, src, snk, cat,null);
		}else {
			newLink = factory.getLink(defaultLinkType, newType, src, snk, cat,oLink);
		}

		if (newLink != null) {
			newLink.updateLinkValues(oLink);
		}
		return newLink;
	}

	@Override
	public NodeStructure copy() {
		return new NodeStructureImpl(this);
	}

	@Override
	public void mergeWith(NodeStructure ns) {
		internalMerge(ns);
		this.sceneStructure.setsSite(ns.getSceneSite());
	}

	/*
	 * This allows subclasses of NodeStructure to override merge but still gives
	 * this class a merge to be called from the constructor.
	 */
	private void internalMerge(NodeStructure ns) {
		if (ns == null) {
			logger.log(Level.WARNING, "Cannot merge with null", TaskManager
					.getCurrentTick());
			return;
		}
		// Add nodes
		for(Node n: ns.getNodes()){
			addNode(n, "PamNodeImpl");
//			addNode(n, n.getFactoryType());
		}

		Collection<Link> cl = ns.getLinks();
		// Add simple links
		for (Link l : cl) {
			if (l.isSimpleLink()) {
//				addLink(l,l.getFactoryType());
//				addLink(l,l.getCategory().getName());
				addLink(l,"PamLinkImpl");
			}
		}

		// Add complex links
		for (Link l : cl) {
			if (l.isSimpleLink() == false) {
//				addLink(l,l.getFactoryType());
//				addLink(l,l.getCategory().getName());
				addLink(l,"PamLinkImpl");
			}
		}
	}
	
	@Override
	public void removeNode(Node n) {
		removeLinkable(n);
	}

	@Override
	public void removeLink(Link l) {
		removeLinkable(l);
	}
	
	@Override
	public synchronized void removeLinkable(Linkable linkable) {
		// First check if the NS actually contains specified linkable to prevent
		// null pointers.
		if (!containsLinkable(linkable)) {
			return;
		}

		// Need to remove all links connected to the linkable specified to be
		// removed.
		Set<Link> tempLinks = linkableMap.get(linkable);
		if(tempLinks != null){ 
			//must put these links in another collection to prevent concurrent modification exception in a recursive call
			Set<Link> connectedLinks = new HashSet<Link>(tempLinks);
			for (Link connectedLink : connectedLinks) {
				// for all of the links connected to linkable
				removeLinkable(connectedLink);
			}
		}

		// finally remove the linkable and its links
		linkableMap.remove(linkable);

		if (linkable instanceof Link) {
			//if removing a link then must also remove the 2 references to the link
			//get actual link object 
			Link aux = links.get(linkable.getExtendedId());
			//get and remove source's reference to link
			Set<Link> sourceLinks = linkableMap.get(aux.getSource());
			if (sourceLinks != null) {
				sourceLinks.remove(aux);
			}
			//get and remove sink's reference to link
			Set<Link> sinkLinks = linkableMap.get(aux.getSink());
			if (sinkLinks != null) {
				sinkLinks.remove(aux);
			}
			//finally remove the link from links map
			links.remove(linkable.getExtendedId());
		}else if (linkable instanceof TreeChart) {
			// todo: remove tree chart 删除树图
		}else if (linkable instanceof Node) {
			nodes.remove(((NodeImpl) linkable).getNodeId());
		}
	}

	@Override
	public void removeLinkable(ExtendedId id) {
		if (!containsLinkable(id)) {
			return;
		}

		if (id.isNodeId()) {
			removeLinkable(nodes.get(id.getSourceNodeId()));
		} else {
			removeLinkable(links.get(id));
		}
	}

	@Override
	public synchronized void clearLinks() {
		for (Link l : links.values()) {
			removeLink(l);
		}
		links.clear();
	}

	@Override
	public synchronized void clearNodeStructure() {
		linkableMap.clear();
		nodes.clear();
		links.clear();
	}

	@Override
	public void decayNodeStructure(long ticks) {
		for (Linkable lnk: linkableMap.keySet()) { // todo decay啥，与remove数据有关
			Activatible a = lnk;
			a.decay(ticks);
			if (a.isRemovable()) {
//				removeLinkable(lnk);
			}
		}
	}

	@Override
	public Node getNode(int id) {
		return nodes.get(id);
	}

	@Override
	public Node getNode(String name) {
		for (Node n : nodes.values()) {
			if (n.getTNname().equals(name)) {
				return n;
			}
		}
		return null;
	}

	@Override
	public Node getNode(ExtendedId id) {
		if (id == null) {
            return null;
        }
        if (id.isNodeId()) {
            return nodes.get(id.getSourceNodeId());
        } else {
            return null;
        }
	}

	@Override
	public Collection<Node> getNodes() {
		Collection<Node> aux = nodes.values();
		return (aux == null) ? null : Collections.unmodifiableCollection(aux);
	}

	public Collection<PamNode> getPamNodes() {
		List list = new ArrayList();
		PamNode p;
		for (Node n: nodes.values()) {
			p = (PamNode) n;
			list.add(p);
		}
		return list;
	}

	@Override
	public int getNodeCount() {
		return nodes.size();
	}

	@Override
	public Link getLink(ExtendedId id) {
		return (id == null)? null:links.get(id);
	}

	@Override
	public Set<Link> getLinks(LinkCategory cat) {
		if(cat == null){
			return null;
		}
		Set<Link> results = new HashSet<Link>();
		for (Link l : links.values()) {
			if (cat.equals(l.getCategory())) {
				results.add(l);
			}
		}
			
		return Collections.unmodifiableSet(results);
	}

	@Override
	public Collection<Link> getLinks() {
		Collection<Link> aux = links.values();
		return (aux == null)? null: Collections.unmodifiableCollection(aux);
	}


	@Override
	public Collection<Link> getLinksOfNode(String nodeName) {
		Collection<Link> aux = links.values();
		return (aux == null)? null: Collections.unmodifiableCollection(aux);
	}

	@Override
	public Collection<Link> getLinksOfSink(String nodeName) {
		Set<Link> results = new HashSet<Link>();

		for (Link l : links.values()) {
			if (nodeName.equals(l.getSink().getTNname())) {
				results.add(l);
			}
		}

		return Collections.unmodifiableCollection(results);
	}

	@Override
	public Collection<Link> getLinksOfSource(String nodeName) {
		Set<Link> results = new HashSet<Link>();

		for (Link l : links.values()) {
			if (nodeName.equals(l.getSource().getTNname())) {
				results.add(l);
			}
		}

		return Collections.unmodifiableCollection(results);
	}

	@Override
	public Collection<Link> getLinksOfNode(int id) {
		Collection<Link> aux = links.values();
		return (aux == null)? null: Collections.unmodifiableCollection(aux);
	}

	@Override
	public Collection<Link> getLinksOfSink(int id) {
		Set<Link> results = new HashSet<Link>();
		Node node;
		for (Link l : links.values()) {
			node = (Node) l.getSink();
			if (id == node.getNodeId()) {
				results.add(l);
			}
		}

		return Collections.unmodifiableCollection(results);
	}

	@Override
	public Collection<Link> getLinksOfSource(int id) {
		Set<Link> results = new HashSet<Link>();

		Node node;
		for (Link l : links.values()) {
			node = l.getSource();
			if (id == node.getNodeId()) {
				results.add(l);
			}
		}

		return Collections.unmodifiableCollection(results);
	}
	
	@Override
	public int getLinkCount() {
		return links.size();
	}

	
	@Override
	public Linkable getLinkable(ExtendedId ids) {
		if(ids == null){
			return null;
		}
		if (ids.isNodeId()) {
			return getNode(ids.getSourceNodeId());
		} else {
			return getLink(ids);
		}
	}

	@Override
	public Collection<Linkable> getLinkables() {
		return Collections.unmodifiableCollection(linkableMap.keySet());
	}

	@Override
	public Map<Linkable, Set<Link>> getLinkableMap() {
		return Collections.unmodifiableMap(linkableMap);
	}
	

	@Override
	public int getLinkableCount() {
		return linkableMap.size();
	}
	
	@Override
	public Set<Link> getAttachedLinks(Linkable lnk) {
		if (lnk == null) {
			return null;
		}
		Set<Link> aux = linkableMap.get(lnk);
		return (aux == null)? null: Collections.unmodifiableSet(aux);
	}

	@Override
	public Set<Link> getAttachedLinks(Linkable lnk, LinkCategory cat) {
		if (lnk == null || cat == null) {
			return null;
		}
		Set<Link> attachedLinks = linkableMap.get(lnk);
		if (attachedLinks == null) {
			return null;
		}
		Set<Link> results = new HashSet<Link>();
		for (Link l : attachedLinks) {
			if (cat.equals(l.getCategory())) {
				results.add(l);
			}
		}
		return Collections.unmodifiableSet(results);
	}

	// todo 改为neo
	@Override
	public Map<Node, Link> getConnectedSources(Linkable lnk) {
		if(lnk == null){
			return null;
		}		
		Set<Link> candidateLinks = linkableMap.get(lnk);
		Map<Node, Link> sourceLinkMap = new HashMap<Node, Link>();
		if (candidateLinks != null) {
			for (Link link : candidateLinks) {
				Node source = link.getSource();
				if (!source.equals(lnk)) {
					sourceLinkMap.put(source, link);
				}
			}
		}
		return Collections.unmodifiableMap(sourceLinkMap);
	}
	
	@Override
//	public Map<Linkable, Link> getConnectedSinks(Node n) {
	public Set<Link> getConnectedSinks(Node n) {
		if(n == null){
			return null;
		}
		// todo link图数据
		Set<Link> candidateLinks = linkableMap.get(n);
		if (candidateLinks == null || candidateLinks.size() == 0) {
			// 一次性查询，以后都在pam里缓存，睡后重新查，激活等都在pam算
			candidateLinks = getNeoLinks(n);

			if (candidateLinks != null) {
				linkableMap.put(n,candidateLinks);
			}
		}

		Map<Linkable, Link> sinkLinkMap = new HashMap<Linkable, Link>();
		if (candidateLinks != null) {
			for (Link link : candidateLinks) {
				Linkable sink = link.getSink();
				if (!sink.equals(n)) {
					links.put(link.getExtendedId(), link);
//					sinkLinkMap.put(sink, link);
				}
			}
		}
//		return Collections.unmodifiableMap(sinkLinkMap);
		return candidateLinks;
	}
	
	@Override
	public String getDefaultLinkType() {
		return defaultLinkType;
	}

	@Override
	public String getDefaultNodeType() {
		return defaultNodeType;
	}
	
	@Override
	public boolean containsNode(Node n) {
		return (n == null)? false : nodes.containsKey(n.getNodeId());
	}

	@Override
	public boolean containsNode(int id) {
		return nodes.containsKey(id);
	}

	@Override
	public boolean containsNode(ExtendedId id) {
		if(id == null){
			return false;
		}
		return id.isNodeId() && nodes.containsKey(id.getSourceNodeId());
	}

	@Override
	public boolean containsLink(Link l) {
		return (l == null)? false : links.containsKey(l.getExtendedId());
	}

	@Override
	public boolean containsLink(ExtendedId id) {
		return (id == null)? false : links.containsKey(id);
	}

	@Override
	public boolean containsLinkable(Linkable l) {
		return (l == null)? false : linkableMap.containsKey(l);
	}

	@Override
	public boolean containsLinkable(ExtendedId id) {
		return (containsNode(id) || containsLink(id));
	}
	
	@Override
	public  NodeStructure getSubgraph(Collection<Node> nodes,int d) {
		return getSubgraph(nodes,d,0.0);
	}

	@Override
	public NodeStructure getSubgraph(Collection<Node> nodes, int d, double threshold) {
		if (nodes == null) {
			logger.log(Level.WARNING,
					"Collection of specified nodes are not available.",
					TaskManager.getCurrentTick());
			return null;
		}
		if (nodes.isEmpty()) {
			logger.log(Level.WARNING,
					"Collection of specified nodes should not be empty.",
					TaskManager.getCurrentTick());
			return null;
		}
		if (d < 0) {
			logger.log(Level.WARNING,
					"Desired distance should not be negative.", TaskManager
							.getCurrentTick());
			return null;
		}

		if (threshold < 0) {
			logger.log(Level.WARNING,
					"Desired threshold should not be negative.", TaskManager
							.getCurrentTick());
			return null;
		}
		//	Distance should be not bigger than number of all links.
		if (d > getLinkCount()){
			d = getLinkCount();
		}

		// Preserve default Node and Link type of the originating NodeStructure
		// 保留原始NodeStructure的默认Node和Link类型
		NodeStructure subNodeStructure = new NodeStructureImpl(
				getDefaultNodeType(), getDefaultLinkType());
		for (Node n : nodes) {
			//Add nodes to the sub node structure and scan from each node
			//将节点添加到子节点结构并从每个节点进行扫描
			if(n != null){
				depthFirstSearch(n, d, subNodeStructure, threshold);
			}
		}
		//	Add all simple links to the sub node structure 将所有简单链接添加到子节点结构
		for (Node subNode : subNodeStructure.getNodes()) {
			//Get the simple links for each Node already in the subgraph
			//获取子图中已有的每个节点的简单链接
			Map<Node, Link> sources = getConnectedSources(subNode);
			for (Node n : sources.keySet()) {
				// Add the simple link only if its source is present in the subgraph
				//仅当子图中存在其源时才添加简单链接
				if (subNodeStructure.containsNode(n)) {
					subNodeStructure.addLink(sources.get(n), sources.get(n)
							.getFactoryType());
				}
			}
		}
		//Add all complex links.
		for (Node subNode : subNodeStructure.getNodes()) {
			// Get the potential complex links for every node present in the subgraph
//			Map<Linkable, Link> sinks = getConnectedSinks(subNode);
//			for (Linkable l : sinks.keySet()) {
//				// If Linkable is a link and the sub graph contains it then
//				// there is a complex link to add.
//				if ((l instanceof Link) && subNodeStructure.containsLinkable(l)) {
//					subNodeStructure.addLink(sinks.get(l), sinks.get(l)
//							.getFactoryType());
//				}
//			}
		}
		return subNodeStructure;
	}
		
	/**
	 * @param currentNode One specified node that be considered as neighbor nodes
	 * or specified nodes in sub NodeStructure 
	 * param step The distance between specified nodes and this current Node
	 * @param distanceLeftToGo The farthest distance between specified nodes and
	 * its neighbor nodes
	 * 
	 * @param subNodeStructure Nodes contained in subNodeStructure.
	 * 
	 * @param threshold Lower bound of Node's activation It involves specified
	 * nodes and all neighbor nodes whose distance from one of specified nodes
	 * is not bigger than farthest distance coming from arguments, and those
	 * nodes' activation is not lower than threshold. Also it involves all links
	 * between these above nodes.
	 *
	 * @param currentNode 一个指定节点被视为邻居节点*或子NodeStructure中的指定节点
	 *                       param step 指定节点与当前节点之间的距离
	 * @param distanceLeftToGo 指定节点与其邻居节点之间的最远距离
	 * @param subNodeStructure subNodeStructure中包含的节点。
	 * @param threshold 节点激活的下限，它涉及指定的节点和与指定节点之一的距离不大于自变量
	 *  的最远距离的所有邻居节点，并且这些节点的激活不低于阈值。它还涉及上述这些节点之间的所有链接*。
	 */
	private void depthFirstSearch(Node currentNode, int distanceLeftToGo, 
			NodeStructure subNodeStructure, double threshold) {
		Node actual = getNode(currentNode.getNodeId());
		if (actual != null && (actual.getActivation() >= threshold)){
			subNodeStructure.addNode(actual, defaultNodeType);
			//Get all connected Sinks
			Set<Link> subSinks = getConnectedSinks(actual);
//			Set<Linkable> subLinkables = subSinks.keySet();
//			for (Linkable l : subLinkables) {
			for (Linkable l : subSinks) {
				if (l instanceof Node && 0 < distanceLeftToGo) {
					depthFirstSearch((Node) l, distanceLeftToGo - 1,
							subNodeStructure, threshold);
				}
			}
			//Get all connected Sources
			Map<Node, Link> subSources = getConnectedSources(actual);
			Set<Node> parentNodes = subSources.keySet();
			for (Node n : parentNodes) {
				if (0 < distanceLeftToGo) {
					depthFirstSearch(n, distanceLeftToGo - 1, subNodeStructure,
							threshold);
				}
			}
		}
	}

	@Override
	public String toString() {
		StringBuilder result = new StringBuilder("Nodes (");
		for (Node n : nodes.values()) {
			result.append(n.toString()).append(",");
		}
		if(nodes.size() != 0){
			result.deleteCharAt(result.length()-1);
		}
		result.append(") Links (");
		for (Link l : links.values()) {
			result.append(l.toString()).append(",");
		}
		if(links.size() != 0){
			result.deleteCharAt(result.length()-1);
		}
		result.append(")");
		return result.toString();
	}

	/**
	 * Returns true if two NodeStructures are meaningfully equal, else false.
	 * Two NodeStructures are equal if they have the same exact nodes and links
	 * and the nodes and links are of the same type.
	 * 
	 * @param ns1
	 *            first {@link NodeStructure}
	 * @param ns2
	 *            second {@link NodeStructure}
	 * @return boolean if the {@link NodeStructure}s are equal
	 */
	public static boolean compareNodeStructures(NodeStructure ns1,
			NodeStructure ns2) {
		if(ns1 == null || ns2==null){
			return false;
		}
		if (ns1.getNodeCount() != ns2.getNodeCount()) {
			return false;
		}
		if (ns1.getLinkCount() != ns2.getLinkCount()) {
			return false;
		}
		for (Node n1 : ns1.getNodes()) {
			if (!ns2.containsNode(n1)) {
				return false;
			}
		}
		for (Link l1 : ns1.getLinks()) {
			if (!ns2.containsLink(l1)) {
				return false;
			}
		}
		return true;
	}

	public Node getNeoNode(String name){
		Node Node = NeoUtil.getNode(name);
		if (Node == null) return null;
		return Node;
	}

	public Set<Link> getNeoLinks(Node n){
		Set<Link> links = NeoUtil.getLinks(n);
		if (links == null) return null;
		return links;
	}

	@Override
	public Set<Scheme> getActions(Node n, ProceduralMemoryImpl pm) throws Exception {
		if (n == null) {
			return null;
		}
		Set<Scheme> schemes = NeoUtil.getSchemes(n, pm);
		return schemes;
	}

	@Override
	public void setSceneTime(String time){
		sceneStructure.setsTime(time);
	}

	@Override
	public String getSceneTime() {
		return sceneStructure.getsTime();
	}

	@Override
	public void setSceneSite(String site){
		SSite sSite = new SSite();
		sSite.setTermName(site);
		sceneStructure.setsSite(sSite);
	}

	@Override
	public SSite getSceneSite() {
		return sceneStructure.getsSite();
	}

	@Override
	public void setSceneWorld(){
		WorldStructure ww = sceneStructure.getWorldStructure();
		ww.setSnodes(nodes);
		ww.setSlinks(links);
	}

	@Override
	public void saveScene(NodeStructure ns) {
		String query;
		Long count = ns.getBroadSceneCount();

//		String query = "merge (n:scene{name:\'" + count + "\',time:\'" + ns.getSceneTime() + "\'}) return n";
//		NeoUtil.excuteSql(query);

//		query = "merge (n:site{name:\'" + ns.getSceneSite().getName() + "\'}) with n merge (a:bcast{name:\'s" + count + "\'})-[r:site]->(n)";
//		NeoUtil.excuteSql(query);

		String name;
//		for (Node node: ns.getNodes()) {
//			name = node.getName();
//			if("health".equals(name) || "emptyFront".equals(name) || "badHealth".equals(name) || "front".equals(name)  || "origin".equals(name)){
//				continue;
//			}
//
//			query = "match (n:bcast{name:\'s" + count + "\'}),(b{name:\'" + name + "\' }) merge (n)-[r:attr]->(b)";
//			NeoUtil.excuteSql(query);
//			System.out.println(query);
//
//			if ("predatorOrigin".equals(name)) {
//				query = "match (n:bcast{name:\'s" + count + "\'}),(b:Verb{name:'attack'}) merge (n)-[r:event]->(b)";
////				query = "match (n:bcast{name:\'" + count + "\'}) merge (n)-[r:event]->(b:Verb{name:'attack' })";
//
//				NeoUtil.excuteSql(query);
//				System.out.println(query);
//			}
//		}

		query = "match (n:bcast{name:\'s" + count + "\'}) set n.time = \'" + TaskManager.getCurrentTick() + "\' return n";
	NeoUtil.excuteOnly(query);
//	System.out.println(query);

//		Thread t = Thread.currentThread();
//		System.out.println("saveScene 进入线程-------》" + t.getName());

	}


}