<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Component id="jPanel2" pref="330" max="32767" attributes="0"/>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Component id="jPanel2" max="32767" attributes="0"/>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="jPanel2">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" attributes="0">
                  <EmptySpace min="-2" pref="12" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="1" attributes="0">
                      <Group type="102" alignment="0" attributes="0">
                          <Component id="jPanel1" min="-2" max="-2" attributes="0"/>
                          <EmptySpace max="32767" attributes="0"/>
                      </Group>
                      <Group type="102" alignment="1" attributes="0">
                          <Component id="jLabel6" max="32767" attributes="0"/>
                          <EmptySpace type="unrelated" max="-2" attributes="0"/>
                          <Component id="jLabel11" min="-2" pref="151" max="-2" attributes="0"/>
                          <EmptySpace min="-2" pref="21" max="-2" attributes="0"/>
                      </Group>
                  </Group>
              </Group>
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace max="32767" attributes="0"/>
                  <Component id="jLabel9" min="-2" pref="151" max="-2" attributes="0"/>
                  <EmptySpace max="-2" attributes="0"/>
                  <Component id="jLabel10" min="-2" pref="151" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="22" max="-2" attributes="0"/>
              </Group>
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" max="-2" attributes="0">
                      <Component id="jButton4" pref="87" max="32767" attributes="0"/>
                      <Component id="jButton1" max="32767" attributes="0"/>
                  </Group>
                  <EmptySpace max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" max="-2" attributes="0">
                      <Component id="jButton7" max="32767" attributes="0"/>
                      <Component id="jButton3" max="32767" attributes="0"/>
                  </Group>
                  <EmptySpace max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="102" attributes="0">
                          <Component id="jButton2" min="-2" max="-2" attributes="0"/>
                          <EmptySpace max="-2" attributes="0"/>
                          <Component id="jButton5" max="32767" attributes="0"/>
                      </Group>
                      <Component id="jButton6" max="32767" attributes="0"/>
                  </Group>
                  <EmptySpace max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace max="32767" attributes="0"/>
                  <Component id="jPanel1" min="-2" max="-2" attributes="0"/>
                  <EmptySpace type="separate" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="jButton1" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="jButton3" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="jButton2" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="jButton5" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace type="separate" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="jButton4" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="jButton6" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="jButton7" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="jLabel6" alignment="3" min="-2" pref="78" max="-2" attributes="0"/>
                      <Component id="jLabel11" alignment="3" min="-2" pref="78" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="jLabel9" alignment="3" min="-2" pref="78" max="-2" attributes="0"/>
                      <Component id="jLabel10" alignment="3" min="-2" pref="78" max="-2" attributes="0"/>
                  </Group>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Container class="javax.swing.JPanel" name="jPanel1">
          <Properties>
            <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="ff" type="rgb"/>
            </Property>
          </Properties>

          <Layout>
            <DimensionLayout dim="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="0" attributes="0">
                      <Component id="jLabel1" min="-2" pref="304" max="-2" attributes="0"/>
                      <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
                  </Group>
              </Group>
            </DimensionLayout>
            <DimensionLayout dim="1">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jLabel1" min="-2" pref="175" max="-2" attributes="0"/>
              </Group>
            </DimensionLayout>
          </Layout>
          <SubComponents>
            <Component class="javax.swing.JLabel" name="jLabel1">
            </Component>
          </SubComponents>
        </Container>
        <Component class="javax.swing.JButton" name="jButton1">
          <Properties>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="fe" green="ff" red="ff" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Main GUI"/>
            <Property name="actionCommand" type="java.lang.String" value="jButton1"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jButton1ActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JButton" name="jButton2">
          <Properties>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="fe" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Website"/>
            <Property name="actionCommand" type="java.lang.String" value="jButton1"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jButton2ActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel6">
          <Properties>
            <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="ff" type="rgb"/>
            </Property>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="fe" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Test Chamber"/>
            <Property name="toolTipText" type="java.lang.String" value=""/>
          </Properties>
          <Events>
            <EventHandler event="mouseClicked" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="jLabel6MouseClicked"/>
          </Events>
        </Component>
        <Component class="javax.swing.JButton" name="jButton5">
          <Properties>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="fe" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value="IRC"/>
            <Property name="actionCommand" type="java.lang.String" value="jButton1"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jButton5ActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JButton" name="jButton3">
          <Properties>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="fe" green="ff" red="ff" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Web GUI"/>
            <Property name="actionCommand" type="java.lang.String" value="jButton1"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jButton3ActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel9">
          <Properties>
            <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="ff" type="rgb"/>
            </Property>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="fe" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value="NAR Pong"/>
            <Property name="toolTipText" type="java.lang.String" value=""/>
          </Properties>
          <Events>
            <EventHandler event="mouseClicked" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="jLabel9MouseClicked"/>
            <EventHandler event="mouseEntered" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="jLabel9MouseEntered"/>
          </Events>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel10">
          <Properties>
            <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="ff" type="rgb"/>
            </Property>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="fe" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Language Lab"/>
            <Property name="toolTipText" type="java.lang.String" value=""/>
          </Properties>
          <Events>
            <EventHandler event="mouseClicked" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="jLabel10MouseClicked"/>
            <EventHandler event="mouseEntered" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="jLabel10MouseEntered"/>
          </Events>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel11">
          <Properties>
            <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="ff" type="rgb"/>
            </Property>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="fe" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Micro World"/>
            <Property name="toolTipText" type="java.lang.String" value=""/>
          </Properties>
          <Events>
            <EventHandler event="mouseClicked" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="jLabel11MouseClicked"/>
            <EventHandler event="mouseEntered" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="jLabel11MouseEntered"/>
          </Events>
        </Component>
        <Component class="javax.swing.JButton" name="jButton4">
          <Properties>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="fe" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Perception"/>
            <Property name="actionCommand" type="java.lang.String" value="jButton1"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jButton4ActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JButton" name="jButton6">
          <Properties>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="fe" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Prediction test"/>
            <Property name="actionCommand" type="java.lang.String" value="jButton1"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jButton6ActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JButton" name="jButton7">
          <Properties>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="fe" green="ff" red="ff" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Vision"/>
            <Property name="actionCommand" type="java.lang.String" value="jButton1"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jButton7ActionPerformed"/>
          </Events>
        </Component>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
