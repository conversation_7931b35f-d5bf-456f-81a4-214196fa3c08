"""
FunctionOperator class for OpenNARS.

Superclass of functions that execute synchronously (blocking, in thread) and take
N input parameters and one variable argument (as the final argument), generating a new task
with the result of the function substituted in the variable's place.
"""
from typing import List, Optional, Any

from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.entity.budget_value import BudgetValue
from linars.org.opennars.entity.sentence import Sentence
from linars.org.opennars.entity.stamp import Stamp
from linars.org.opennars.entity.task import Task
from linars.org.opennars.entity.truth_value import TruthValue
from linars.org.opennars.io.symbols import JUDGMENT_MARK
from linars.org.opennars.language.variable import Variable
from linars.org.opennars.operator.operator import Operator


class FunctionOperator(Operator):
    """
    Superclass of functions that execute synchronously and take N input parameters.
    """
    
    def __init__(self, name: str):
        """
        Constructor.
        
        Args:
            name: The name of the operator
        """
        super().__init__(name)
    
    def function(self, memory, x: List[Term]) -> Optional[Term]:
        """
        y = function(x)
        
        Args:
            memory: The memory
            x: The input arguments
            
        Returns:
            Term: The result, or None if unsuccessful
        """
        raise NotImplementedError("Subclasses must implement function()")
    
    def get_range(self) -> Term:
        """
        The term that the output will inherit from; analogous to the 'Range' of a function in mathematical terminology.
        
        Returns:
            Term: The range term
        """
        raise NotImplementedError("Subclasses must implement get_range()")
    
    def equals_terms(self, a: Term, b: Term) -> float:
        """
        The extent to which it is truth that the 2 given terms are equal. In other words, a distance metric.

        Args:
            a: First term
            b: Second term

        Returns:
            float: 1.0 if equal, 0.0 if not equal
        """
        return 1.0 if a == b else 0.0
    
    def execute(self, operation, args: List[Term], memory, time) -> Optional[List[Task]]:
        """
        Execute the function operator.
        
        Args:
            operation: The operation to execute
            args: Arguments of the operation
            memory: The memory to work on
            time: The time
            
        Returns:
            List[Task]: The direct collectable results and feedback of the execution
        """
        # Check if first argument is SELF
        is_self = False
        if args[0].name() == "SELF" or args[0].name() == "{SELF}":
            is_self = True
            num_args = len(args) - 1
        else:
            num_args = len(args)
        
        # Check argument count
        if num_args < 1:
            raise ValueError(f"Requires at least 1 argument: {args}")
        
        if num_args < 2:
            raise ValueError(f"Requires at least 2 arguments: {args}")
        
        # Prepare parameters for function
        num_param = num_args - 1
        if not is_self:
            num_param += 1
        
        x = args[:num_param]
        
        # Execute function
        y = self.function(memory, x)
        if y is None:
            return None
        
        # Create sentence
        var = Variable("$1")
        confidence = memory.narParameters.DEFAULT_JUDGMENT_CONFIDENCE
        
        if is_self:
            # Replace the variable in the operation with the result
            operation = operation.set_component(0,
                                                operation.get_subject().set_component(num_args, y, memory),
                                                memory)
            s = Sentence(
                operation,
                JUDGMENT_MARK,
                TruthValue(1.0, confidence, memory.narParameters),
                Stamp(time, memory)
            )
        else:
            s = Sentence(
                y,
                JUDGMENT_MARK,
                TruthValue(1.0, confidence, memory.narParameters),
                Stamp(time, memory)
            )
        
        # Create budget and task
        budget = BudgetValue(
            memory.narParameters.DEFAULT_JUDGMENT_PRIORITY,
            memory.narParameters.DEFAULT_FEEDBACK_DURABILITY,
            s.truth.get_confidence(),  # truthToQuality
            memory.narParameters
        )
        from linars.org.opennars.entity.task import EnumType
        new_task = Task(s, budget, EnumType.INPUT)
        return [new_task]
