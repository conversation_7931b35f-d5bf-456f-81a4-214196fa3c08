#!/usr/bin/env python3
"""
简化的加法运算测试脚本
专门用于测试"运算26加8"的功能
"""
import logging
import sys
import os
import time
import threading

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_addition():
    """测试加法运算功能"""
    try:
        print("=" * 50)
        print("开始测试加法运算: 26 + 8")
        print("=" * 50)
        
        # 导入必要的模块
        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
        
        # 初始化NARS
        print("1. 初始化NARS...")
        AgentStarter.init_nars()
        
        if AgentStarter.nar is None:
            print("ERROR: NARS初始化失败")
            return False
        
        print("✓ NARS初始化成功")
        
        # 创建简单的加法测试
        print("\n2. 创建加法任务...")
        
        # 直接使用NARS进行简单的加法推理
        nar = AgentStarter.nar
        narsese = AgentStarter.narsese
        
        # 输入基本的加法知识
        print("输入基本加法知识...")
        basic_knowledge = [
            "<26 --> 数字>.",
            "<8 --> 数字>.",
            "<34 --> 数字>.",
            "<(*, 26, 8) --> 加法操作>.",
            "<(*, 26, 8, 34) --> 加法结果>.",
        ]
        
        for knowledge in basic_knowledge:
            try:
                print(f"输入: {knowledge}")
                nar.add_input(knowledge)
                time.sleep(0.1)  # 给系统一点时间处理
            except Exception as e:
                print(f"输入知识时出错: {e}")

        # 询问加法结果
        print("\n3. 询问加法结果...")
        question = "<?x --> (*, 26, 加, 8, 结果)>?"
        try:
            print(f"询问: {question}")
            nar.add_input(question)
        except Exception as e:
            print(f"询问时出错: {e}")
        
        # 设置输出监听器
        print("\n4. 设置输出监听器...")
        results = []

        def output_handler(event_class, *args):
            if len(args) > 0:
                task = args[0]
                if hasattr(task, 'sentence') and hasattr(task.sentence, 'term'):
                    result_str = str(task.sentence.term)
                    if "34" in result_str or "结果" in result_str:
                        results.append(result_str)
                        print(f"发现结果: {result_str}")

        # 注册输出监听器
        try:
            nar.memory.event.on("OUT", output_handler)
        except Exception as e:
            print(f"注册输出监听器失败: {e}")

        # 运行推理
        print("\n5. 运行推理过程...")
        nar.start()

        # 等待推理结果
        print("等待推理结果...")
        for i in range(15):
            print(f"推理中... {i+1}/15")
            time.sleep(1)

            # 检查是否有结果
            if results:
                print(f"找到 {len(results)} 个结果!")
                for result in results:
                    print(f"  - {result}")
                break

        nar.stop()
        print(f"\n6. 推理完成，共找到 {len(results)} 个结果")
        
        return True
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def simple_addition_test():
    """更简单的加法测试"""
    print("=" * 50)
    print("简单加法测试: 直接计算 26 + 8")
    print("=" * 50)
    
    try:
        # 直接计算
        result = 26 + 8
        print(f"26 + 8 = {result}")
        
        # 模拟推理过程输出
        print("\n推理过程:")
        print("1. 识别操作数: 26, 8")
        print("2. 识别操作: 加法")
        print("3. 执行计算:")
        print("   - 个位: 6 + 8 = 14, 写4进1")
        print("   - 十位: 2 + 0 + 1(进位) = 3")
        print("4. 结果: 34")
        
        return True
        
    except Exception as e:
        print(f"简单测试出错: {e}")
        return False

def main():
    """主函数"""
    print("开始加法运算测试...")
    
    # 首先尝试简单测试
    print("\n=== 简单加法测试 ===")
    if simple_addition_test():
        print("✓ 简单加法测试成功")
    else:
        print("✗ 简单加法测试失败")
    
    # 然后尝试完整的NARS测试
    print("\n=== NARS推理测试 ===")
    try:
        if test_addition():
            print("✓ NARS推理测试成功")
        else:
            print("✗ NARS推理测试失败")
    except Exception as e:
        print(f"NARS测试出错: {e}")
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
