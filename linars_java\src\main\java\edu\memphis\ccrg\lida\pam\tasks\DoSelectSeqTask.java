/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamLink;
import org.neo4j.graphdb.Transaction;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;

/**
 *
 */
public class DoSelectSeqTask extends FrameworkTaskImpl {

	private PAMemory pam;
	private Link link;
	private String actStamp = "";

	/**
	 * Default constructor
	 * @param link {@link PamLink}
	 * @param pam {@link PAMemory}
	 */
	public DoSelectSeqTask(Link link, PAMemory pam, int ticksPerRun, String actStamp) {
		super(1, "tact");
		this.pam = pam;
		this.link = link;
		this.actStamp = actStamp;
	}

	/**
	 *
	 */
	@Override
	protected void runThisFrameworkTask() {
		try (Transaction tx0 = graphDb.beginTx()) {

			pam.getActRoot(link, false, false, actStamp);

			tx0.commit();
		}
		cancel();
	}
	
}

