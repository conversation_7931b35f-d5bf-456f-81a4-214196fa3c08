' weighted sum of all elements - used all over the place for NN's
' with a compact vector representation because the (NARS-term)complexity is a issue here
' we need to add a bias
<(&/, <(*,$1,$2,$3) --> [maddv]>, +1, <(*,$1,bias,$1) --> [add]>, +1) =/> <(*,$1,$2,$3) --> [weighted]>>.


' multiply add  $1 = $1 + $2 * $3
< (^pick, {SELF}, madd, $1, $2, $3) =/> <(*, $1, $2, $3) --> [madd]> >.

' multiply add vector, multiAct0 is $1, vectors are $2 and $3
< (^pick, {SELF}, maddv, $1, $2, $3) =/> <(*, $1, $2, $3) --> [maddv]> >.

' add
< (^pick, {SELF}, add, $1, $2, $3) =/> <(*, $1, $2, $3) --> [add]> >.


' indirection to call an action which finishes the recording of the macro
<(&/, <(*, res0,  a0, b0) --> [weighted]>, +10, (^pick, {SELF}, fin), +10) =/> <(*, res0,  a0, b0) --> [weighted2]> >.

' request to generate the code for a weighted sum
<(*, res0,  a0, b0) --> [weighted2]>!
