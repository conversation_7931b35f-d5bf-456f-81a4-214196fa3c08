package edu.memphis.ccrg.lida.motivation.workspace;

import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.motivation.shared.FeelingNode;
import edu.memphis.ccrg.lida.pam.PamImpl0;
import edu.memphis.ccrg.lida.workspace.structurebuildingcodelets.StructureBuildingCodeletImpl;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;

import java.util.ArrayList;
import java.util.Collection;

/**
 * Tries to associate feeling nodes with coincidentally active event nodes.
 * <AUTHOR>
 */
public class FeelingStructureBuildingCodelet extends StructureBuildingCodeletImpl {
	
	@Override
	protected void runThisFrameworkTask() {
		// 海马构造数据？推理构建？海马只在学习阶段？
		NodeStructure bufferContent = writableBuffer.getBufferContent(null);
		Collection<FeelingNode> feelingNodes = new ArrayList<FeelingNode>();
		Collection<Node> eventNodes = new ArrayList<Node>();
		for(Node n: bufferContent.getNodes()){
			if(n instanceof FeelingNode){
				feelingNodes.add((FeelingNode) n);
			}else {
				eventNodes.add(n);
			}
		}
		for(Node feelingNode: feelingNodes){
			for(Node eventNode: eventNodes){
				//Add a link from Feeling to Max active node. 
				//Runs the risk of connecting Feelings to non-event nodes if such nodes occur.
				//如果发生非事件节点，则存在将情感连接到非事件节点的风险
				//Activation is harmonic mean 激活是调和平均
				double linkActivation = 2*feelingNode.getActivation()*eventNode.getActivation()/(feelingNode.getActivation()+eventNode.getActivation());
//				bufferContent.addDefaultLink(feelingNode, eventNode,
//											PamImpl0.PARENT_LINK_CATEGORY,
//											linkActivation, 0.0);
			}
		}
	}
	
	@Override
	public NodeStructure retrieveWorkspaceContent(WorkspaceBuffer buffer) {
		return null;
	}
	@Override
	public boolean bufferContainsSoughtContent(WorkspaceBuffer buffer) {
		return false;
	}
}