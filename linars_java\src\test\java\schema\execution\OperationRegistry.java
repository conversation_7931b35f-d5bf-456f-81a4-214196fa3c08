package schema.execution;

import test.schema.execution.operations.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 操作注册表，管理所有可用的操作
 */
public class OperationRegistry {
    private static final Map<String, Operation> operations = new HashMap<>();

    static {
        // 注册所有操作
        registerOperation("初始化", new InitializationOperation());
        registerOperation("获取当前位", new GetCurrentDigitOperation());
        registerOperation("计算当前位和进位", new CalculateDigitAndCarryOperation());
        registerOperation("更新结果", new UpdateResultOperation());
        registerOperation("移动到下一位", new MoveToNextDigitOperation());
        registerOperation("添加进位", new AddFinalCarryOperation());
        registerOperation("跳过进位", new SkipCarryOperation());
        registerOperation("返回结果", new ReturnResultOperation());
    }

    public static void registerOperation(String name, Operation operation) {
        operations.put(name, operation);
    }

    public static Operation getOperation(String name) {
        return operations.get(name);
    }

    public static boolean hasOperation(String name) {
        return operations.containsKey(name);
    }
}
