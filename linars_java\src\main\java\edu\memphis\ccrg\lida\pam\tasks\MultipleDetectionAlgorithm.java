/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.environment.Environment;
import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.initialization.Initializable;
import edu.memphis.ccrg.lida.framework.shared.*;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PamLinkable;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.sensorymemory.SensoryMemory;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * This class implements the FeatureDetector interface and provides default
 * methods. Users should extend this class and overwrite the detect() and
 * excitePam() methods. A convenience init() method is added to initialize the
 * class. This method can be overwritten as well. This implementation is
 * oriented to detect features from sensoryMemory, but the implementation can be
 * used to detect and send excitation from other modules, like Workspace,
 * emotions or internal states.
 * 这个类实现了FeatureDetector接口并提供了默认方法。用户应该扩展这个类并覆盖detect()和excitePam()方法。
 * 方便的init()方法用于初始化类。这个方法也可以被覆盖。这个实现是为了从sensoryMemory检测特征，
 * 但是这个实现可以用来检测和从其他模块发送激活，比如Workspace，情感或内部状态。
 * <AUTHOR> J. McCall
 * 
 */
public abstract class MultipleDetectionAlgorithm extends FrameworkTaskImpl implements DetectionAlgorithm {

	private static final Logger logger = Logger.getLogger(MultipleDetectionAlgorithm.class.getCanonicalName());
	/**
	 * Map of {@link PamLinkable}
	 */
	protected Map<String, PamLinkable> pamNodeMap = new HashMap<String, PamLinkable>();
	/**
	 * the {@link PAMemory}
	 */
	protected PAMemory pam;
	/**
	 * {@link PamLinkable} this algorithm detects
	 */
	protected SensoryMemory sensoryMemory;

	protected Environment environment;

	protected String message0;

	private NodeStructure nodeStructure = new NodeStructureImpl();

	protected Map<String,Object> sensorParam = new HashMap<String, Object>();
	/**
	 * 
	 */
	public MultipleDetectionAlgorithm(){
	}
	
	@Override
	public void setAssociatedModule(FrameworkModule module, String moduleUsage){
		if(module instanceof PAMemory){
			pam = (PAMemory) module;
		}else if(module instanceof SensoryMemory) {
			sensoryMemory = (SensoryMemory) module;
		}else if (module instanceof Environment){
			environment = (Environment) module;
		}else{
			logger.log(Level.WARNING, "Cannot set associated module {1}",
					new Object[]{TaskManager.getCurrentTick(),module});
		}
	}

	public void excite(String object, double amount, String from) {

		pam.excite(object,amount,from);

//		Linkable linkable = pam.getNode(object);
//
//		if (linkable == null) {
//			// 模态+场景+意象=三节点都要出现在无意识buffer
//			// 如果只是取场景，又难联想到相关，太唯一+太确定，联想相关又可能爆炸
//			linkable = nodeStructure.getNeoNode(object);
//
//			// 查各感知节点
//			// 感知节点，方案1：模态做场景标签，一个光秃秃的起点，查还需要标签，联动曲折
//			// 方案2：模态与场景边链接，有外联动，模态本身可自行关联其他
//			// pam查询大都通过点边联动，而非标签
////			linkable = NeoUtil.getNodeFrom(object,from);
//
//			if (linkable != null) {
//				// 首次执行
//				// 上面linkable和节点本身激活为0，这里激活也为0
//				pam.addDefaultNode((Node) linkable);	// todo 性能、逻辑优化
//			}
//		}
//		pam.receiveExcitation(linkable, amount, from);
	}

	/**
	 * This task can be initialized with the following parameters:<br><br/>
	 * 
	 * <b>nodes type=string</b>labels of the Nodes in {@link PAMemory} this algorithm detects<br/>
	 * 
	 * @see Initializable
	 */
	@Override
	public void init (){
		super.init();
//		String nodeLabels = (String) getParam("nodes", "");
//		if (nodeLabels != null) {
//			GlobalInitializer globalInitializer = GlobalInitializer
//					.getInstance();
//			String[] labels = nodeLabels.split(",");
//			for (String label : labels) {
//				label = label.trim();
//				PamNode node = (PamNode) globalInitializer.getAttribute(label);
//				if (node != null) {
//					addPamLinkable(node);
//				}else{
//					logger.log(Level.WARNING, "could not get node with label {1} from global initializer",
//							new Object[]{TaskManager.getCurrentTick(),label});
//				}
//			}
//		}
	}
	
	/**
	 * Adds {@link PamLinkable}.
	 * @param linkable {@link PamLinkable} that will be detected by this algorithm
	 */
	public void addPamLinkable(PamLinkable linkable) {
		pamNodeMap.put(linkable.getTNname(), linkable);
	}
	
	@Override
	protected void runThisFrameworkTask(){
		detectLinkables();
		if(logger.isLoggable(Level.FINEST)){
			logger.log(Level.FINEST,"detection performed {1}"
					,new Object[]{TaskManager.getCurrentTick(),this});
		}
	}

	/**
	 * Override this method for domain-specific feature detection
	 */
	public abstract void detectLinkables();

	public void setMessage0(String message0){
		this.message0 = message0;
	}

	//Methods below are not applicable
	@Override
	public double detect() {
		return 0;
	}
	@Override
	public PamLinkable getPamLinkable() {
		Collection<PamLinkable> nodes = pamNodeMap.values();
		if(nodes.size() != 0){
			return nodes.iterator().next();
		}
		return null;
	}
	@Override
	public void setPamLinkable(PamLinkable linkable) {
	}
}
