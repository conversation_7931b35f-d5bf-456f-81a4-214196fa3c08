/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
/**
 * 
 */
package edu.memphis.ccrg.lida.framework.strategies;

import edu.memphis.ccrg.lida.framework.initialization.Initializable;

/**
 * Generic designation for all types of strategies such as decay, excite, etc.
 * 各种策略的通用名称，例如衰减，激发等
 * <AUTHOR>
 */
public interface Strategy extends Initializable{
	
}
