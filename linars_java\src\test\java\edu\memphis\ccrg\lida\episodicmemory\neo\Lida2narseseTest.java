package edu.memphis.ccrg.lida.episodicmemory.neo;

import com.alibaba.fastjson.JSONObject;
import edu.memphis.ccrg.lida.alifeagent.Run0;
import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.LinkImpl;
import edu.memphis.ccrg.lida.framework.shared.NodeImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Term;
import org.junit.Test;
import org.neo4j.graphdb.Node;
import org.neo4j.graphdb.Relationship;
import org.neo4j.graphdb.Result;
import org.neo4j.graphdb.Transaction;
import org.opennars.inference.TemporalRules;
import org.opennars.io.Parser;
import org.opennars.language.*;
import org.opennars.operator.Operation;
import org.opennars.operator.Operator;

import java.util.*;
import java.util.stream.Collectors;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;
import static edu.memphis.ccrg.lida.data.NeoUtil.sortLinks;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.arg;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;

public class Lida2narseseTest {
    public static Map<String, Object > parameters = new HashMap<String, Object>();
    private PAMemory pam = (PAMemory) AgentStarter.pam;

    @Test
    public void exPand() {
        Run0.main(new String[]{});
        // 将所有标签为“形容词”的点查到
//        String query ="match (n:形容词) set n.fx = n.fx + 100, n.fy = n.fy + 50";
        String query ="match (n:形容词) return n";
        try (Transaction tx = graphDb.beginTx()) {
            try ( Result result = tx.execute( query, parameters ) ) {
                Map<String, Object> row;
                Relationship re;
                Node node;
                Map<String, Object> map;

                String id;
                while ( result.hasNext() ) {
                    row = result.next();
                    JSONObject jj;
                    float x = 0;
                    float y = 0;
                    for (String key : result.columns()) {
                        node = (Node) row.get(key);
                        // fx属性，每隔500加100。看与500的倍数，多少倍加多少个100
                        // 字符串转long
                        x = Float.parseFloat(String.valueOf(node.getProperty("fx")));
                        y = Float.parseFloat(String.valueOf(node.getProperty("fy")));
                        x = x + x / 500 * 100;
                        y = y + y / 500 * 50;
                        // 用cypher更新回到图谱中，执行语句
                        query = "match (n:形容词) where id(n) = " + node.getId() + " set n.fx = " + x + ", n.fy = " + y;
                        System.out.println(query);
                        try (Transaction tx0 = graphDb.beginTx()) {
                            try (Result result0 = tx0.execute(query, parameters)) {

                            }
                            tx0.commit();
                        }
                    }
                }
            }
            tx.commit();
        }
        while (true){

        }
    }

    @Test
    public void transVar() {
        Run0.main(new String[]{});
        String query ="match (n)-[r:参数2]->(m) return n";
        try (Transaction tx = graphDb.beginTx()) {
            try ( Result result = tx.execute( query, parameters ) ) {
                Map<String, Object> row;
                Relationship re;
                Node node;
                Map<String, Object> map;

                String id;
                while ( result.hasNext() ) {
                    row = result.next();
                    for (String key : result.columns()) {
                        node = (Node) row.get(key);
                        // 在name前加$符号，用cypher更新回到图谱中，执行语句
                        query = "match (n) where id(n) = " + node.getId() + " set n.name = \"$" + node.getProperty("name") + "\"";
                        System.out.println(query);
                        try (Transaction tx0 = graphDb.beginTx()) {
                            try (Result result0 = tx0.execute(query, parameters)) {

                            }
                            tx0.commit();
                        }
                    }
                }
            }
            tx.commit();
        }
        while (true){

        }
    }

    @Test
    public void getalltypes(){
        Run0.main(new String[]{}); // 启动lida
        pam = (PAMemory) AgentStarter.pam;
        Set<Link> conscene;
        conscene = NeoUtil.getSomeLinks(null, null, "<", "形容词", null);

        // 遍历所有意象，找到所有意象的关系类型，存为列表
        Set<String> types = conscene.stream().map(Link::getTNname).collect(Collectors.toSet());
        // 打印所有意象的关系类型
        for (String s : types) {
//            System.out.println("case \"" + s + "\": break;");
            System.out.print("\"" + s + "\",");
        }

        // 遍历所有意象，找到所有意象的关系类型，并统计每个类型的数量，存为map
//        Map<String, Integer> types = conscene.stream().collect(Collectors
//                .groupingBy(Link::getTNname, Collectors.summingInt(e -> 1)));
        // 将map按数量排序
//        types = types.entrySet().stream().sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
//                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));

//        // 遍历所有意象，找到所有意象的关系类型，并统计每个类型的数量，存为map，并排序
//        Map<String, Integer> types = conscene.stream().collect(Collectors
//                .groupingBy(Link::getTNname, Collectors.summingInt(e -> 1)))
//                .entrySet().stream().sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
//                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));
//
//        // 打印所有意象的关系类型及其数量
//        for (String s : types.keySet()) {
//            System.out.println( s + " ---- " + types.get(s));
//        }

//        switch (type){
//            case "领属": break;
//            case "属性": break;
//            case "对象": break;
//            case "地点": break;
//            case "状态": break;
//            case "实例": break;
//            case "时间": break;
//            // 语句语法结构组成部分
//            case "语序": break;
//            case "句序": break;
//            case "goushi0": break;
//            case "goushi": break;
//            case "关系12": break;
//            case "fto": break;
//            case "顺接": break;
//            // 共现结构组成部分
//            case "arg1": break;
//            case "arg0": break;
//            case "arg2": break;
//
//            case "条件2": break;
//            case "条件1": break;
//            case "else": break;
//            case "判断": break;
//            case "回应内容": break;
//            case "感知动作": break;
//            case "整体赋值": break;
//            case "内容场景": break;
//            case "心理计划": break;
//            case "返回赋值": break;
//            case "方向起点": break;
//            case "参数": break;
//            case "参数2": break;
//            case "参数1": break;
//            case "表示": break;
//            case "break": break;
//            case "手段": break;
//            case "时态": break;
//            case "具象": break;
//            case "抽象": break;
//            case "情绪": break;
//            case "名字": break;
//            case "媒介": break;
//            // 场景间关系
//            case "拆分": break;
//            case "顺发": break;
//            case "蕴含":
////                    term = Equivalence.make(l.term[0], l.term[2], TemporalRules.ORDER_NONE);
//                break;
//            case "isa": break;
//            case "顺承": break;
//            case "时序": break;
//            case "时序首": break;
//            case "判断首": break;
//            // 语句场景组成部分
//            case "客事": break;
//            case "修饰": break;
//            case "动作": break;
//            case "涉事": break;
//            case "领事": break;
//            case "施事": break;
//            case "类事": break;
//            case "受事": break;
//
//            case "内容要求": break;
//            case "动作要求": break;
//            case "欲求": break;
//            case "内容": break;
//            default: break;
//        }
    }

    @Test
    public void transArg() {
        Run0.main(new String[]{}); // 启动lida
        String query;
        List<String> querys = new ArrayList<String>();
        // 将所有类型为“施事”的边，转为arg0
//        query ="match (n:形容词)-[r:施事]->(m) remove r:施事 set r:arg0";
        query = "MATCH (n:形容词)-[r:参数1]->(m) CREATE (n)-[r2:arg0]->(m) SET r2 = r WITH r,r2 DELETE r with r2 return r2\n";
        querys.add(query);
        // 将所有类型为“受事”的边，转为arg2
        query = "MATCH (n:形容词)-[r:类事]->(m) CREATE (n)-[r2:arg3]->(m) SET r2 = r WITH r,r2 DELETE r with r2 return r2\n";
        querys.add(query);
        // 将所有类型为“动作”的边，转为arg1
        query = "MATCH (n:形容词)-[r:参数2]->(m) CREATE (n)-[r2:arg2]->(m) SET r2 = r WITH r,r2 DELETE r with r2 return r2\n";
        querys.add(query);
        // 模糊查询所有类型包含“事”的边，转为arg3
//        query = "MATCH (n:形容词)-[r]->(m) where r.name =~ '.*事.*' CREATE (n)-[r2:arg3]->(m) SET r2 = r WITH r,r2 DELETE r with r2 return r2\n";
        // 执行所有语句
        for (String s : querys) {
            try (Transaction tx = graphDb.beginTx()) {
                try (Result result = tx.execute(s, parameters)) {

                }
                tx.commit();
            }
        }
        while (true){

        }
    }

    @Test
    public void transTotal0() throws Parser.InvalidInputException {
        Run0.main(new String[]{}); // 启动lida
        pam = (PAMemory) AgentStarter.pam;
        int num = 0;
        Set<Link> conscene;
        conscene = NeoUtil.getSomeLinks(null, null, "<", "形容词", null);
        String type;
        edu.memphis.ccrg.lida.framework.shared.Node source, sink;

        List<String> all = Arrays.asList(new String[]{"属性","对象","判断","地点","施事1","顺发","状态","词序2","词序3","领属",
                "词序4","拆分","条件2","方向起点","条件1","goushi","else","词序1","受事","回应内容","partof","客事","实例","蕴含",
                "感知动作","整体赋值","isa1","涉事","arg2","时间","arg1","arg0","内容场景","心理计划","goushi0","动作","句序",
                "具象","参数2","内容要求","表示","动作1","顺承1","参数1","手段","时态","修饰","抽象","情绪","备选词序4","关系12",
                "语序","时序","顺承","时序首","领事","动作要求","受事1","欲求","内容","fto","名字","施事","b","返回赋值","参数",
                "break","g","判断首","顺接1","媒介","r","isa","情绪1","顺接","y","z","类事"});

        List<String> exclude1 = Arrays.asList(new String[]{"心理计划","具身计划",
                "返回赋值", "整体赋值", "子类", "媒介", "赋值", "情绪","时间","顺发","关键帧","变量句",
                "具象","抽象","时态","名字","对象","实例","手段","表示","break","拆分"});

        List<String> exclude2 = Arrays.asList(new String[]{"心理计划", "欲求", "顺承1",
                "partof ", "施事1", "词序2", "词序3", "词序4", "词序1", "isa1", "动作1","情绪1",
                "备选词序4", "受事1", "顺接1", "b", "g", "r", "y", "z"});

        List<String> exclude3 = Arrays.asList(new String[]{"isa","顺承","蕴含"}); // 蕴含对等和相似不分方向

        List<String> exclude4 = Arrays.asList(new String[]{"顺接","fto"});

        // 反向结构列表，如时序
        List<String> reverse = Arrays.asList(new String[]{"时序","时序首","判断","判断首","else"});

        // 继承和顺承列表，英文为
        List<String> shun = Arrays.asList(new String[]{"继承者","被继承","顺承者","被顺承","相似者","被相似"});

        // 除了以上列表1234的关系类型，剩下的汇总
        List<String> other = Arrays.asList(new String[]{"属性","地点","状态","领属","条件2","方向起点","条件1","goushi","受事","回应内容",
               "客事","感知动作","涉事", "arg2","arg1","arg0","内容场景","goushi0","动作","句序","参数2","内容要求","参数1","arg3","arg4","arg5","arg",
               "修饰","关系12","语序", "领事","动作要求","内容","施事","参数","类事"});

//        List<String> arg = Arrays.asList(new String[]{"arg0","arg1","arg2","arg3","arg4","arg5","arg"});

        // 筛选除了all列表中，除了以上列表1234的关系类型，剩下的汇总
//        for(String s : all){
//            if(!exclude1.contains(s) && !exclude2.contains(s) && !exclude3.contains(s) && !exclude4.contains(s) && !reverse.contains(s)){
//                other.add(s);
//                System.out.print("\"" + s + "\",");
//            }
//        }

        // 已用过的边列表
        Set<Link> used = new HashSet<Link>();

        for (Link cslink : conscene) {
            // 已用过的边不再处理
            // todo 增加‘是否处理’属性，子节点处理完，才能处理父节点，否则放回队列
            if (used.contains(cslink)) {
                continue;
            }
            Term term = null;
            Term term2 = null;

            type = cslink.getTNname();
            if (type.equals("相似") || type.equals("对等") ) {
                used.add(cslink);
                continue;
            }

            source = cslink.getSource();
            sink = (edu.memphis.ccrg.lida.framework.shared.Node) cslink.getSink();
            LinkImpl l = (LinkImpl) cslink;

            // 条件场景多个元素，还是要分个类
//            pam.putMap(source, source.getTNname());
//            pam.putMap(sink, sink.getTNname());

            Set<Link> links = new HashSet<Link>();
            boolean isreverse = false;
            // 如果type不在三个排除列表中。结构建模
            if (!exclude1.contains(type) && !exclude2.contains(type) && !exclude3.contains(type) && !exclude4.contains(type)) {
                if (reverse.contains(type)) { // 时序等反向结构
                    isreverse = true;
                    // 从conscene中查出所有以source为头结点，并关系类型在reverse里的边
                    edu.memphis.ccrg.lida.framework.shared.Node finalSource = source;
                    links = conscene.stream().filter(e -> e.getSource().equals(finalSource)
                            && reverse.contains(e.getTNname())).collect(Collectors.toSet());
                    // 尾节点汇总，构建conjunction。按顺序组装成（#,点1，点2，...）的形式的term
                    Term[] terms = new Term[links.size()];
                    int i = 0;

                    if(links.size() > 3){
                        System.out.println("-----------------links.size() ====== " + links.size());
                    }

                    // 从links里找到类型为时序首或判断首的边，如果有，放在第一个位置
                    Link link = links.stream().filter(e -> e.getTNname().equals("时序首") || e.getTNname().equals("判断首")).findFirst().orElse(null);
                    if(link != null){
                        terms[0] = (Term) link.getSink();
                        // 从link的尾节点开始，在conscene中找类型为顺承的边，然后继续找顺承的边，直到找不到为止
                        while (true) {
                            i++;
                            if (i >= links.size()) {
                                break;
                            }
                            Link finalLink = link;
                            Link link2 = conscene.stream().filter(e -> e.getSource().getNodeId()
                                    == ((NodeImpl)finalLink.getSink()).getNodeId()
                                    && e.getTNname().equals("顺承")).findFirst().orElse(null);
                            if (link2 != null) {
                                terms[i]= (Term) link2.getSink();
                                link = link2;
                            } else {
                                if (i < links.size() - 1) {
                                    System.out.println("找不到顺承的边，但还有边，说明有问题");
                                    int j = 0;
                                    for (Link link0 : links) {
                                        terms[j] = (Term) link0.getSink();
                                        j++;
                                    }
                                    for (Link cslink0 : conscene) {
                                        System.out.println("所有边----- " + cslink0.toString());
                                    }
                                }
                                break;
                            }
                        }
                    }else {
                        for (Link link0 : links) {
                            terms[i] = (Term) link0.getSink();
                            i++;
                        }
                    }
                    used.addAll(links);
                    used.add(cslink);
                    term = Conjunction.make(terms, TemporalRules.ORDER_FORWARD);
                } else if(shun.contains(type)) {
                    // 如果是继承和继承，则分别构建
                    edu.memphis.ccrg.lida.framework.shared.Node sink1 = sink;
                    edu.memphis.ccrg.lida.framework.shared.Node source1 = source;
//                    List<String> finalShun = shun;
//                    links = conscene.stream().filter(e -> e.getSink().equals(finalSink)
//                            && finalShun.contains(e.getTNname())).collect(Collectors.toSet());
//                    Term[] terms = new Term[links.size()];
//                    int i = 0;
//                    for (Link link : links) {
//                        terms[i] = (Term) link.getSource();
//                        i++;
//                    }

                    switch (type){
                        case "顺承者":
                            // 找出以头结点为头且关系类型为顺承的边2，再通过边2的头结点找到类型为“被顺承”的边3，三条边组成三角形，构建顺承term
//                            links = conscene.stream().filter(e -> e.getSource().equals(finalSource)
//                                    && e.getTNname().equals("顺承")).collect(Collectors.toSet());
//                            if(links.size() > 0){
//                                Link link2 = links.iterator().next();
//                                edu.memphis.ccrg.lida.framework.shared.Node node2 = (edu.memphis.ccrg.lida.framework.shared.Node) link2.getSink();
//                                links = conscene.stream().filter(e -> e.getSource().equals(node2)
//                                        && e.getTNname().equals("被顺承")).collect(Collectors.toSet());
//                                if(links.size() > 0){
//                                    Link link3 = links.iterator().next();
//                                    edu.memphis.ccrg.lida.framework.shared.Node node3 = (edu.memphis.ccrg.lida.framework.shared.Node) link3.getSink();
//                                    term = Inheritance.make((Term) node3, (Term) node2);
//                                    term2 = Inheritance.make((Term) node2, (Term) finalSink);
//                                }
//                            }
                            // 查询三角形的三边中一条
                            String query = "match (n)-[r1:顺承]->(m)-[r2:被顺承]->(p) where id(n) = " + source1.getNodeId() + " and id(p) = " + sink1.getNodeId() + " return r1";
                            links = NeoUtil.getLinksCypher(query, null);
                            // 构建顺承term
                            if(links.size() > 0){
                                Link link2 = links.iterator().next();
                                edu.memphis.ccrg.lida.framework.shared.Node node2 = (edu.memphis.ccrg.lida.framework.shared.Node) link2.getSink();
                                term = Implication.make((Term) source1, (Term) node2, TemporalRules.ORDER_FORWARD);
                            }
                            break;
                        case "被顺承":
                            String query1 = "match (n)<-[r1:顺承]-(m)-[r2:顺承者]->(p) where id(n) = " + source1.getNodeId() + " and id(p) = " + sink1.getNodeId() + " return r1";
                            links = NeoUtil.getLinksCypher(query1, null);
                            if(links.size() > 0){
                                Link link2 = links.iterator().next();
                                edu.memphis.ccrg.lida.framework.shared.Node node2 = link2.getSource();
                                term = Implication.make((Term) node2, (Term) source1, TemporalRules.ORDER_FORWARD);
                            }
                            break;
                        case "继承者":
                            String query2 = "match (n)-[r1:isa]->(m)-[r2:被继承]->(p) where id(n) = " + source1.getNodeId() + " and id(p) = " + sink1.getNodeId() + " return r1";
                            links = NeoUtil.getLinksCypher(query2, null);
                            if(links.size() > 0){
                                Link link2 = links.iterator().next();
                                edu.memphis.ccrg.lida.framework.shared.Node node2 = (edu.memphis.ccrg.lida.framework.shared.Node) link2.getSink();
                                term = Inheritance.make((Term) source1, (Term) node2);
                            }
                            break;
                        case "被继承":
                            String query3 = "match (n)<-[r1:isa]-(m)-[r2:继承者]->(p) where id(n) = " + source1.getNodeId() + " and id(p) = " + sink1.getNodeId() + " return r1";
                            links = NeoUtil.getLinksCypher(query3, null);
                            if(links.size() > 0){
                                Link link2 = links.iterator().next();
                                edu.memphis.ccrg.lida.framework.shared.Node node2 = link2.getSource();
                                term = Inheritance.make((Term) node2, (Term) source1);
                            }
                            break;
                        case "相似者":
                            String query4 = "match (n)-[r1:相似]->(m)-[r2:被相似]->(p) where id(n) = " + source1.getNodeId() + " and id(p) = " + sink1.getNodeId() + " return r1";
                            links = NeoUtil.getLinksCypher(query4, null);
                            if(links.size() > 0){
                                Link link2 = links.iterator().next();
                                edu.memphis.ccrg.lida.framework.shared.Node node2 = (edu.memphis.ccrg.lida.framework.shared.Node) link2.getSink();
                                term = Similarity.make((Term) source1, (Term) node2);
                            }
                            break;
                        case "被相似":
                            String query5 = "match (n)<-[r1:相似]-(m)-[r2:相似者]->(p) where id(n) = " + source1.getNodeId() + " and id(p) = " + sink1.getNodeId() + " return r1";
                            links = NeoUtil.getLinksCypher(query5, null);
                            if(links.size() > 0){
                                Link link2 = links.iterator().next();
                                edu.memphis.ccrg.lida.framework.shared.Node node2 = link2.getSource();
                                term = Similarity.make((Term) node2, (Term) source1);
                            }
                            break;
                        default: break;
                    }

                    used.addAll(links);
                    used.add(cslink);
//                    term = Inheritance.make(terms);
                } else {
                    // 从conscene中查出所有以sink为尾结点，并关系类型在other里的边
                    edu.memphis.ccrg.lida.framework.shared.Node finalSink = sink;
                    List<String> finalOther2 = arg;
                    links = conscene.stream().filter(e -> e.getSink().equals(finalSink)
                            && finalOther2.contains(e.getTNname())).collect(Collectors.toSet());
                    // 排序需要，先转为list，set无序
                    List<Link> linkList = new ArrayList<Link>();
                    if (!links.isEmpty()){
                        linkList.addAll(links);
                        sortLinks(linkList);
                    }else {
                        List<String> finalOther1 = other;
                        linkList = conscene.stream().filter(e -> e.getSink().equals(finalSink)
                                && finalOther1.contains(e.getTNname())).collect(Collectors.toList());
                    }
                    // 将links的所有头节点汇总
                    Term[] terms = new Term[linkList.size()];
                    int i = 0;
                    boolean action = false;
                    Term term0 = null;
                    for (Link link : linkList) {
                        term0 = (Term) link.getSource();
                        String name = term0.toString();
                        // 如果name包含字符“^”，且无其他项，则是单动作，有其他则跳过
                        if (name.contains("^") && !name.contains(",")) {
                            if (linkList.size() == 2) {
                                action = true;
                                // 该term作为term[0]
                                terms[0] = term0;
                                // 除了当前link，links剩下的另一条边作为term[1]，这种情况links只会有两条边
                                Link link2 = linkList.stream().filter(e -> !e.equals(link)).findFirst().orElse(null);
                                if (link2 != null) {
//                                  terms[1] = (Term) link2.getSource();
                                    final Operator oRegistered = nar.memory.getOperator(term0.toString());
                                    if (oRegistered != null) {
                                        term = Operation.make(oRegistered, new Term[]{(Term) link2.getSource()}, true);
                                    }else{
                                        System.out.println("-----oRegistered------ null ----- " + term0);
                                    }
                                }
                                // 构建动作term
//                            term = Operation.make(terms[0], terms[1], true);

                                // 然后跳出
                                break;
                            }else if (linkList.size() == 1){
                                // 只有一个，如左转，但也有缺省场景，以ft开头
                                term = new Term("ft_" + term0.name() + "_" + ((edu.memphis.ccrg.lida.framework.shared.Node)linkList.get(0).getSink()).getNodeId());
//                                NeoUtil.setTermToNode(term, sink);
                            }else {
                                action = true;
                                terms = new Term[linkList.size() - 1];
                                // 有多个参数，如左加(1,2,3)，找到含“^”的term，作为term[0]
//                                terms[0] = term0;
                                i = 0;
                                // 除了当前link，还有多条边，全找到
                                for (Link link2 : linkList) {
                                    if (!link2.equals(link)) {
                                        terms[i] = (Term) link2.getSource();
                                        i++;
                                        continue;
                                    }
                                    System.out.println("-----多参数操作------ 不匹配 ----- " + link2);
                                }
                                Operator oRegistered = nar.memory.getOperator(term0.toString());
                                // 构建动作term
                                term = Operation.make(oRegistered, terms, true);
                                System.out.println("-----多参数操作------term0 ----- " + term);
                                break;
                            }
                        }
                        terms[i] = term0;
                        i++;
                    }
                    used.addAll(linkList);
                    used.add(cslink);
                    if (!action && terms.length > 0) {
                        CharSequence nn = terms[0].name();
                        if(terms.length == 1) {
                            if (!nn.toString().startsWith("(")){
                                terms[0] = new Term("ft_" + nn.toString().replace("$","") + "_" + ((edu.memphis.ccrg.lida.framework.shared.Node)linkList.get(0).getSink()).getNodeId());
                            }else {
                                // 单个复合词项，用下划线替换所有标点符号，如(&&,倒数,第一位)
                                terms[0] = new Term("ft_" + nn.toString().replace("(", "_")
                                        .replace(")", "").replace(",", "_")
                                        .replace(" ", "_").replace("$",""));
                                // 加上id，保证唯一性，因为可能有多个相同
                                terms[0] = Term.get(terms[0].toString() + "_" + ((edu.memphis.ccrg.lida.framework.shared.Node)linkList.get(0).getSink()).getNodeId());
                            }
                        }
                        boolean isConjunction = false;
                        for (Term term1 : terms) {
                            if (term1.toString().contains(",") || term1.toString().contains(")")
                                    || (term1.toString().contains(">") && term1 instanceof CompoundTerm)) {
                                // 尖括号，可能与大于小于号冲突，所以要复合词项
                                isConjunction = true;
                                break;
                            }
                        }
//                        if (isConjunction) {
//                            // 如果是复合词项，用合取即可，否则用product，有顺序，不被合取
//                            term = Conjunction.make(terms, TemporalRules.ORDER_FORWARD, true);
//                        }else {
                            // 都先默认有顺序？
                            term = Product.make(terms);
//                        }
                    }
                }
            }else {
//                used.add(cslink);
                continue;
            }

            // 如果links数量大于1，则将term设置回图数据中，reverse设置到头节点，否则设置到尾节点，用cypher
            if (!links.isEmpty() && term != null) { //|| shun.contains(type)
                if (isreverse) {
                   num = NeoUtil.setTermToNode(term, source, num);
                } else {
                   num = NeoUtil.setTermToNode(term, sink, num);
                }
            }

            System.out.println("term1 ----- " + term);
            System.out.println("csl ----- " + cslink);
            if (term2 != null) {
                System.out.println("---- term2 ----- " + term2);
            }
        }
        while (true) {
//            try {
//                Thread.sleep(1000);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
        }
    }



}
