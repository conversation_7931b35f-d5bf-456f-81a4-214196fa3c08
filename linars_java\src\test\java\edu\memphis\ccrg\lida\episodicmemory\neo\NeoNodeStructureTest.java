package edu.memphis.ccrg.lida.episodicmemory.neo;

import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import org.junit.Test;
import org.opennars.main.Nar;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class NeoNodeStructureTest {

    @Test
    public void getLinks() {
//        NeoInitizer.startNeo();
//        Node n = new NeoNodeImpl();
//        n.setName("fairHealth");
//        Set<Link> candidateLinks = neoNodeStructure.getLinks((NeoNode) n);
//        for (Link link: candidateLinks) {
//            System.out.println(link.getSink().getName());
//        }
    }

    @Test
    public void testSet() {
        Set<String> set = new HashSet<>();
        set.add("aaa");
        set.add("bbb");
        set.add("ccc");
        set.add("ddd");
        set.add("aaa");
        set.add("bbb");

        //1. list构造方法
        List<String> list = new ArrayList<>(set);

        //2. stream
        List<String> list2 = set.stream().collect(Collectors.toList());

        System.out.println(list.toString());
        System.out.println(list2.toString());

//        PamNode pamNode = new SetInt();
//        Statement statement = new PamLinkImpl();
//        Term term = new PamLinkImpl();
//        Term term1 = statement;
//        Node node = term1;
    }

    @Test
    public void testNode(){
//        final EmbeddedProxySPI spi = null;
//        Nnode nnode = new Nnode(spi, (long) 1.0);

        String message;
        message = "花，去，哪里，了，？";
//        String message = "花,去,哪里,了,？";

        String[] mms = message.split("，");
        List<String> pairs;
        System.out.println(mms);
    }

    @Test
    public void testproduct(){
        Nar nar = null;
        try {
            nar = new Nar();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (ParserConfigurationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (SAXException e) {
            e.printStackTrace();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (ParseException e) {
            e.printStackTrace();
        }

        nar.addInput("<(*,ss1,ss2) --> 顺承>.:|:");
    }

    @Test
    public void testcompute(){
        int and = (9 - 4) & 255;
        int and0 = 9 - 4 & 255;
        int or = (9 - 4) | 255;
        int or0 = 9 - 4 | 255;
    }

}
