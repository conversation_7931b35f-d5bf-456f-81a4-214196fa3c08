/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

/*
 * AddPanel.java
 *
 * Created on Aug 2, 2010, 4:24:41 PM
 * <AUTHOR> (edits)
 */

package edu.memphis.ccrg.lida.framework.gui.panels;

import edu.memphis.ccrg.lida.framework.initialization.FrameworkGuiPanelDef;

import javax.swing.*;
import java.awt.*;
import java.util.List;

/**
 * Allows users to add and edit {@link GuiPanel}s from the main {@link FrameworkGui1}
 * 
 * <AUTHOR>
 * <AUTHOR> (edits)
 */
public class AddEditPanel extends GuiPanelImpl {
    private static final long serialVersionUID = 1L;

    /** Creates new form AddPanel */
    public AddEditPanel() {
        initComponents();
    }

    /**
     * initializes this panel's name
     * @param name
     *            Panel name
     */
    public void init(String name) {
        this.setName(name);
        for (java.awt.Component c : this.getComponents()) {
            if (c instanceof javax.swing.JTextField)
                ((javax.swing.JTextField) c).setText("");
        }
    }

    /**
     *
     * @param classNames List of class names
     */
    public void initClassnames(List<String> classNames) {
        for (String c : classNames) {
            cmbClassname.addItem(c);
        }
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents
        jLabel1 = new JLabel();
        txtName = new JTextField();
        jLabel2 = new JLabel();
        jLabel3 = new JLabel();
        cmbPosition = new JComboBox<>();
        chkRefresh = new JCheckBox();
        btnAddPanel = new JButton();
        jLabel5 = new JLabel();
        txtParameters = new JTextField();
        jLabel6 = new JLabel();
        cmbClassname = new JComboBox<>();

        //======== this ========

        //---- jLabel1 ----
        jLabel1.setText("Name:");

        //---- txtName ----
        txtName.setPreferredSize(new Dimension(60, 20));

        //---- jLabel2 ----
        jLabel2.setText("Classname:");

        //---- jLabel3 ----
        jLabel3.setText("Position:");

        //---- cmbPosition ----
        cmbPosition.setModel(new DefaultComboBoxModel<>(new String[] {
            "A",
            "B",
            "C",
            "D",
            "E",
            "FLOAT",
            "TOOL"
        }));
        cmbPosition.setSelectedIndex(1);

        //---- chkRefresh ----
        chkRefresh.setSelected(true);
        chkRefresh.setText("Refresh after load");

        //---- btnAddPanel ----
        btnAddPanel.setText("OK");
        btnAddPanel.addActionListener(e -> btnAddPanelActionPerformed(e));

        //---- jLabel5 ----
        jLabel5.setText("Parameters:");

        //---- txtParameters ----
        txtParameters.setPreferredSize(new Dimension(60, 20));

        //---- jLabel6 ----
        jLabel6.setText("(separated by \",\")");

        //---- cmbClassname ----
        cmbClassname.setEditable(true);
        cmbClassname.setModel(new DefaultComboBoxModel<>(new String[] {

        }));

        GroupLayout layout = new GroupLayout(this);
        setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createSequentialGroup()
                    .addContainerGap()
                    .addGroup(layout.createParallelGroup()
                        .addGroup(layout.createSequentialGroup()
                            .addGroup(layout.createParallelGroup()
                                .addGroup(layout.createSequentialGroup()
                                    .addGroup(layout.createParallelGroup()
                                        .addComponent(jLabel2)
                                        .addComponent(jLabel1)
                                        .addComponent(jLabel5)
                                        .addComponent(jLabel3))
                                    .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                                    .addGroup(layout.createParallelGroup()
                                        .addComponent(cmbPosition, GroupLayout.DEFAULT_SIZE, 319, Short.MAX_VALUE)
                                        .addComponent(txtName, GroupLayout.DEFAULT_SIZE, 319, Short.MAX_VALUE)
                                        .addComponent(jLabel6)
                                        .addComponent(txtParameters, GroupLayout.DEFAULT_SIZE, 319, Short.MAX_VALUE)
                                        .addComponent(cmbClassname, GroupLayout.DEFAULT_SIZE, 319, Short.MAX_VALUE)))
                                .addGroup(layout.createSequentialGroup()
                                    .addComponent(chkRefresh)
                                    .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED, 197, GroupLayout.PREFERRED_SIZE)))
                            .addContainerGap())
                        .addGroup(GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                            .addComponent(btnAddPanel, GroupLayout.PREFERRED_SIZE, 126, GroupLayout.PREFERRED_SIZE)
                            .addGap(94, 94, 94))))
        );
        layout.setVerticalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createSequentialGroup()
                    .addContainerGap()
                    .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                        .addComponent(jLabel1)
                        .addComponent(txtName, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE))
                    .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                    .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                        .addComponent(jLabel2)
                        .addComponent(cmbClassname, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE))
                    .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                    .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                        .addComponent(cmbPosition, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE)
                        .addComponent(jLabel3))
                    .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                    .addGroup(layout.createParallelGroup(GroupLayout.Alignment.TRAILING)
                        .addComponent(jLabel5)
                        .addComponent(txtParameters, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE))
                    .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                    .addComponent(jLabel6)
                    .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                    .addComponent(chkRefresh)
                    .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED, 90, Short.MAX_VALUE)
                    .addComponent(btnAddPanel, GroupLayout.PREFERRED_SIZE, 33, GroupLayout.PREFERRED_SIZE)
                    .addContainerGap())
        );
        // JFormDesigner - End of component initialization  //GEN-END:initComponents
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables
    private JLabel jLabel1;
    private JTextField txtName;
    private JLabel jLabel2;
    private JLabel jLabel3;
    private JComboBox<String> cmbPosition;
    private JCheckBox chkRefresh;
    private JButton btnAddPanel;
    private JLabel jLabel5;
    private JTextField txtParameters;
    private JLabel jLabel6;
    private JComboBox<String> cmbClassname;
    // JFormDesigner - End of variables declaration  //GEN-END:variables

    private void btnAddPanelActionPerformed(java.awt.event.ActionEvent evt) {

    }

    private static int PANEL_NAME = 0;
    private static int CLASS_NAME = 1;
    private static int PANEL_POSITION = 2;
    private static int TAB_ORDER = 3;
    private static int MUST_REFRESH = 4;
    private static int FIRST_PARAM = 5;

    private String tabOrder = "1";

    /**
     *
     * @return a array with the panel parameters
     */
    public FrameworkGuiPanelDef getPanelConfig() {
        FrameworkGuiPanelDef config = new FrameworkGuiPanelDef();
        config.setName(txtName.getText());
        config.setTitle(txtName.getText());
        config.setClassName(cmbClassname.getSelectedItem().toString());
        config.setPosition(cmbPosition.getSelectedItem().toString());
        config.setTabOrder(Integer.parseInt(tabOrder));
        config.setRefreshAfterLoad(chkRefresh.isSelected());
        config.setOptions(txtParameters.getText().split(","));
        return config;
    }

    /**
     *
     * @param panelParams the panel parameters
     */
    public void setPanelParams(String[] panelParams) {
        txtName.setText(panelParams[PANEL_NAME]);
        cmbClassname.setSelectedItem(panelParams[CLASS_NAME]);
        cmbPosition.setSelectedItem(panelParams[PANEL_POSITION]);
        tabOrder = panelParams[TAB_ORDER];
        chkRefresh.setSelected(panelParams[MUST_REFRESH].equalsIgnoreCase("y"));

        if (panelParams.length > FIRST_PARAM) {
            String extraParams = "";
            for (int i = FIRST_PARAM; i < panelParams.length; i++) {
                extraParams += panelParams[i] + ",";
            }
            txtParameters.setText(extraParams.substring(0, extraParams.length() - 1));
        }
    }

}
