/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.data.TermUtil;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamNode;
import edu.memphis.ccrg.lida.pam.PamNodeImpl;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
import org.opennars.entity.Task;
import org.opennars.io.Parser;
import org.opennars.operator.Operation;
import org.opennars.operator.Operator;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.narsese;
import static edu.memphis.ccrg.lida.pam.tasks.DoSimpleSceneTask.varTask;

/**
 */
public class DoMindActTask extends FrameworkTaskImpl {
	private String sinks;
	private String sources;
	private Node sink;
	private Node source;
	private PAMemory pam;
	private NodeStructure seqNs;
	private NodeStructure goalNs;
	private NodeStructure sceneNs;
	private String actStamp;
	/**
	 * Default constructor
	 * @param sink the {@link Node} to add
	 * @param pam {@link PAMemory}
	 */
	public DoMindActTask(Node sink, Node source, PAMemory pam, NodeStructure seqNs, NodeStructure goalNs, String actStamp) {
		// 时序线程，没有固定周期间隔？有defaultTicksPerRun，其实每个都会有间隔
		super(1, "tact");
		this.sink = sink;
		this.source = source;
		this.pam = pam;
		this.seqNs = seqNs;
//		this.sceneNs = sceneNs;
		this.goalNs = goalNs;
		this.actStamp = actStamp;
	}

	public DoMindActTask(String sink, String source, PAMemory pam, NodeStructure seqNs, NodeStructure sceneNs) {
		super(1, "tact");
		this.sinks = sink;
		this.sources = source;
		this.pam = pam;
		this.seqNs = seqNs;
		this.sceneNs = sceneNs;
	}

	/** */
	@Override
	protected void runThisFrameworkTask() {
		try {
			CompoundTerm term = null;
			Term term1 = narsese.parseTerm(sink.toString());
			if (term1 instanceof CompoundTerm) {
				term = (CompoundTerm) term1;
			}else {
				System.out.println("---------不是复合词项----------term1 = " + term1);
				doSucc();
				return;
			}
			Term[] terms = term.term;
			String tt = term.toString();
			// 简单分割，带有嵌套。如果是找到复合词项所有原子词项，则难复原嵌套关系。另外这也只是符号替换，不用做其他
			String[] gTerms = tt.split(",");
			// 原方案变量实例化，有受事等硬编码
			if (tt.contains("$")) {
				// 遍历找到变量词项，然后到seqns中一一匹配，找到变量的值，然后替换
				for (Link link : seqNs.getLinks()) {
					if (link.getTNname().equals("nowisa")) {
						String catname = link.getSource().getTNname();
						// 遍历gTerms，找到变量，然后全部替换
						for (int i = 0; i < gTerms.length; i++) {
							String varname = gTerms[i];
							// 有不确定数量的右括号，先去掉，然后补上相同数量，先统计数量，再去掉
							if (varname.contains(")")) {
								int rnum = TermUtil.countStr(varname, ")");
								varname = varname.replace(")", "");
								if (varname.equals(catname)) {
									// 加上相同数量的右括号
									gTerms[i] = link.getSink().getTNname();
									for (int j = 0; j < rnum; j++) {
										gTerms[i] += ")";
									}
								}
							}else {
								if (varname.equals(catname)) {
									gTerms[i] = link.getSink().getTNname();
								}
							}
						}
					}
				}
				// 拼接字符串gTerms，然后解析成term，最后一个不要逗号
				StringBuilder gTerms1 = new StringBuilder();
				for (int i = 0; i < gTerms.length; i++) {
					if (i == gTerms.length - 1) {
						gTerms1.append(gTerms[i]);
					} else {
						gTerms1.append(gTerms[i]).append(",");
					}
				}
				term = (CompoundTerm) narsese.parseTerm(gTerms1.toString());
				terms = term.term;
			}
			// 如果是操作，直接执行，返回结果
			// todo 假如是无符操作，查找最接近时序和原子带符操作，以相似、对等、isa等为准，然后执行
			for (int i = 0; i < terms.length; i++) {
				if (terms[i] instanceof Operation) {
					Operation op = (Operation)terms[i];
					Operator oper = op.getOperator();
					List<Task> feedback = oper.execute(op, op.getArguments().term, (Memory) goalNs, nar);
					if(feedback != null){
						terms[i] = feedback.get(0).getTerm();
//						System.out.println("-----句中执行----------结果------- " + terms[i]);
					}
				}
			}

			StringBuilder terms1 = new StringBuilder();
			if(!terms.toString().contains("$")){
				// 拼接字符串terms，然后解析成term，最后一个不要逗号
				for (int i = 0; i < terms.length; i++) {
					if (i == terms.length - 1) {
						terms1.append(terms[i] + ")");
					} else if(i == 0){
						terms1.append("(#,").append(terms[i]).append(",");
					} else {
						terms1.append(terms[i]).append(",");
					}
				}
				nar.addInputTo("(^say,{SELF}," + terms1.toString() + ")! :|:", (Memory) goalNs);
			}
			// 原方案返回赋值。给下一环节变量实例化，位数等整体赋值由上一环节的变量实例化完成
			varTask(sink, terms1.toString());
		} catch (Parser.InvalidInputException e) {
			throw new RuntimeException(e);
		}

		doSucc();
	}

	private void doSucc() {
		pam.setSceneMainNode(sink);
		// 变量句，定义变量并赋值，用具体值实例化，提交到参数表中
		varTask(sink, sink.getTNname());
		// 用于某些初始化
		varTask(sink, null);

//		pam.getSceneNode(sink,sink.getTNname(),true);

		System.out.println("-----执行心理操作时序中-------|||---" + sink.getTNname() + "-----DoMindActTask");

		DoSuccTask doSuccTask = new DoSuccTask(sink,source,pam, 80, actStamp);
		pam.getAssistingTaskSpawner().addTask(doSuccTask);

		AgentStarter.isDoVar = true;
		AgentStarter.doStartick = TaskManager.getCurrentTick();

		cancel();
	}

	protected void runThisFrameworkTask0() {
		Set<Link> Links = NeoUtil.getSomeLinks(sink,null,null, null,"变量");
		// 找到所有变量现值边，以便生成和操作
		List<Link> varlinks = new ArrayList<Link>();
		// 各变量槽位=施事+受事+领事
		String shouvar = "",shivar = "",lingvar = "";
		List<String> varList = new ArrayList<> ();
		for (Link link00: Links) {
			boolean iscover = false;
			String catname;
			for (Link l : seqNs.getLinksOfSource(link00.getSource().getTNname())) {
				if (l.getTNname().equals("nowisa")) {
					iscover = true;
					varlinks.add(l);
					catname = link00.getTNname();
					if(catname.equals("受事")){	//原加数12
						shouvar = l.getSink().getTNname();
					}else if (catname.equals("领事")){
						lingvar = link00.getSource().getTNname();
					}else if (catname.equals("参数1")){
						varList.add(l.getSink().getTNname());
					}else if (catname.equals("参数2")){
						varList.add(l.getSink().getTNname());
					}
				}
			}
			// 如果是变量，但又没有赋值，那可能是纯参数初始化，需要额外找整体赋值。如位数
			if (!iscover) {
				Set<Link> totalvar = NeoUtil.getSomeLinks(link00.getSource(),"整体赋值",null, null,null);
				for (Link l : totalvar) {
					PamNode category = new PamNodeImpl();
					category.setNodeName("nowisa");
					pam.addDefaultLink(l);
					// 两个都是source，在前，以实际方向为准，而不是查询语句先后
					Link nowlink = pam.addDefaultLink((Node)l.getSink(), l.getSource(),category);
					pam.getListener().receivePercept(nowlink.getSource(), ModuleName.SeqGraph);
					pam.getListener().receivePercept((Node) nowlink.getSink(), ModuleName.SeqGraph);
					pam.getListener().receivePercept(nowlink,ModuleName.SeqGraph);
					varlinks.add(nowlink);
					if(link00.getTNname().equals("受事")){
						shouvar = l.getSink().getTNname();
					}else {
						lingvar = link00.getSource().getTNname();
					}
				}
			}
		}
//		CoverVar result = new CoverVar(result.shouvar, result.lingvar);

		if(AgentStarter.mindactmap.containsKey(sink.getTNname())){
			Set<Link> mindverb = NeoUtil.getSomeLinks(sink,"动作",null, null,null);
			String name;
			int index = 0;
			// 倒数 负一二三，正数 一二三，java字符串按正数，原则上需要认知，先定方向+再定位置
			switch (lingvar){
				case "ft144":
					index = -1;
					break;
				case "ft145":
					index = -2;
					break;
				case "ft146":
					index = -3;
					break;
			}

			String getstr = "";
			for (Link l : mindverb) {
				name = l.getSource().getTNname();
				switch (name){
					case "获取":
						int star = shouvar.length() + index - 1;
						int last = shouvar.length() + index;
						if(star < 0 || last > shouvar.length() || star > shouvar.length()){
							getstr = "空";
						}else {
							getstr = shouvar.substring(star, last);
						}
						break;
					case "加上":
						getstr = String.valueOf(Integer.parseInt(varList.get(0)) + Integer.parseInt(varList.get(1)));
						break;
				}
			}
			// 取得结果，返回，并赋值给另一个变量，
			Set<Link> returnset = NeoUtil.getSomeLinks(sink,"返回赋值",null, null,"变量");
			Node varnode = NeoUtil.getNode(getstr);
			pam.addDefaultNode(varnode);
			if(returnset.size() != 0 && varnode != null) {
				for (Link l : returnset) {
					PamNode category = new PamNodeImpl();
					category.setNodeName("nowisa");
					pam.addDefaultLink(l);
					// 两个都是source，在前，以实际方向为准，而不是查询语句先后
					Link nowlink = pam.addDefaultLink((Node) l.getSink(), varnode, category);

					pam.getListener().receivePercept(nowlink.getSource(), ModuleName.SeqGraph);
					pam.getListener().receivePercept((Node) nowlink.getSink(), ModuleName.SeqGraph);
					pam.getListener().receivePercept(nowlink,ModuleName.SeqGraph);
				}
			}
		}
		nar.addInputTo("(^say,{SELF}," + sink.getTNname() + ")! :|:", (Memory) goalNs);
		doSucc();
	}

	private static class CoverVar {
		public final String shouvar;
		public final String lingvar;

		public CoverVar(String shouvar, String lingvar) {
			this.shouvar = shouvar;
			this.lingvar = lingvar;
		}
	}
}
