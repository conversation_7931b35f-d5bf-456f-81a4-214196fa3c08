/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.workspace;

import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.globalworkspace.Coalition;
import edu.memphis.ccrg.lida.globalworkspace.CoalitionImpl;
import edu.memphis.ccrg.lida.globalworkspace.GlobalWorkspace;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * A background task in the {@link Workspace} which transfers percepts from the
 * Perceptual buffer to the Current Situational Model
 * 
 * <AUTHOR> J. McCall
 * 
 */
public class AttentionBackgroundTask extends FrameworkTaskImpl {

	private static final Logger logger = Logger
			.getLogger(AttentionBackgroundTask.class.getCanonicalName());

	private WorkspaceBuffer perceptualBuffer;
	private WorkspaceBuffer csm;

	@Override
	public void setAssociatedModule(FrameworkModule module, String moduleUsage) {
		if (module instanceof Workspace) {
			perceptualBuffer = (WorkspaceBuffer) module
												.getSubmodule(ModuleName.PerceptualBuffer);
			csm = (WorkspaceBuffer) module.getSubmodule(ModuleName.CurrentSM);
		}
	}

	/**
	 * Retrieves nodes from PAM and provides them to attentional codelets. This
	 * function gets PAM's nodes and provides them to CurrentSituationalModel,
	 * which will be accessed by attentional codelets.
	 * 从PAM检索节点，并将其提供给注意代码。 *该函数获取PAM的节点并将其提供给 当前情景模型*，注意小码将访问该节点
	 */
	@Override
	protected void runThisFrameworkTask() {
		if (logger.isLoggable(Level.FINEST)) {
			logger.log(Level.FINEST, "Updating CSM with perceptual buffer content.",
					TaskManager.getCurrentTick());
		}
		// todo csm做广播子图，由pb转入二次加工模块，然后输出到csm，如语言
		// pb需要多个副本，代表多个模态，独立进行
//		csm.addBufferContent(perceptualBuffer.getBufferContent(null));

		// todo 环境信息，场景预测，当前场景缓存，意识链，关系都是图像，符号没有
		//  显式描述，隐式沉淀，全隐式=场景框架+语义，全显式=案例实例+约定搭配
//		WorkspaceBuffer csm = (WorkspaceBuffer) ((WorkspaceImpl) KgmakerApplication.pam
//				.getListener()).getSubmodule(ModuleName.CurrentSituationalModel);

		// todo 场景、意图、目标
		GlobalWorkspace globalWorkspace = AgentStarter.pam.getGlobalWorkspace();
		// 无意识提交的报告，生产消费模式=这里充当通道桥梁
		// 必须以完整场景为准？缺失要素都不能通过阈值，如果没有备选则新建场景
		// 要素齐备=动作+施受事，可能仅有施事，被动+不确定施事=某人 + 动作 + 某物，对空气做动作
		// 其他非核心要素，如时间、地点、程度、工具、方式等，还有属性的属性，
		// 时间等属性导致不同结果=与常规场景整合，共同约束后面场景，在某些场景是核心要素？

		// 持续场景 + 场景转换 = 事件分割，受未衰减元素影响，近期事件影响也更大
		// 按帧=每帧的效果类似=分子机制和刺激元素类似，情感衰减率更低=持续更久
		// 体验+想到+自传回忆+假装场景，情感独立=需要多一层联动，整合到场景=激活下一层都是一度
		// 实时情感=实时动机？场景激发情感=其实是每次都实时判断价值？当前场景情感价值=不必要实时
		NodeStructure csmContent = csm.getBufferContent(null);

		if(csmContent == null){
			logger.log(Level.WARNING, "Null WorkspaceContent returned in {1}. Coalition cannot be formed.",
					new Object[]{TaskManager.getCurrentTick(), this});
		}else if (csmContent.getLinkableCount() > 0) {

			// 本来是注意小码联盟，报告与否+筛选与否，筛选是增强大于抑制？只是辅助=不能阻塞？
			// 毫秒级的阻塞=难以觉察，形成流水线=也几乎等于不阻塞？pam到广播到pam？
			Coalition coalition = new CoalitionImpl(csmContent);
			// 多线程汇总交互？
			globalWorkspace.addCoalition(coalition);
			logger.log(Level.FINER, "{1} adds new coalition with activation {2}",
					new Object[]{TaskManager.getCurrentTick(), this, coalition.getActivation()});
			setNextTicksPerRun(8);
		}
	}
}
