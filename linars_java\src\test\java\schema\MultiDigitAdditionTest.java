package schema;

import schema.execution.ExecutionResult;
import schema.execution.SchemaExecutionEngine;

import java.util.HashMap;
import java.util.Map;

/**
 * 多位数加法测试类，用于测试多位数加法的可执行图式
 */
public class MultiDigitAdditionTest {
    public static void main(String[] args) {
        // 构建多位数加法的可执行图式
        ExecutableSchema schema = MultiDigitAdditionSchemaBuilder.build();
        
        // 创建执行引擎
        SchemaExecutionEngine engine = new SchemaExecutionEngine();
        
        // 准备测试用例
        runTest(engine, schema, "123", "456", "579");
        runTest(engine, schema, "999", "1", "1000");
        runTest(engine, schema, "0", "0", "0");
        runTest(engine, schema, "1234", "5678", "6912");
        runTest(engine, schema, "9999", "9999", "19998");
    }
    
    /**
     * 运行测试用例
     * @param engine 执行引擎
     * @param schema 可执行图式
     * @param operand1 操作数1
     * @param operand2 操作数2
     * @param expected 期望结果
     */
    private static void runTest(SchemaExecutionEngine engine, ExecutableSchema schema, 
                               String operand1, String operand2, String expected) {
        // 准备输入参数
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("操作数1", operand1);
        parameters.put("操作数2", operand2);
        
        System.out.println("===== 测试用例: " + operand1 + " + " + operand2 + " = " + expected + " =====");
        
        // 执行图式
        ExecutionResult result = engine.execute(schema, parameters);
        
        // 验证结果
        if (result.isSuccess()) {
            String actual = (String) result.getResult();
            boolean passed = expected.equals(actual);
            
            System.out.println("结果: " + actual);
            System.out.println("期望: " + expected);
            System.out.println("测试结果: " + (passed ? "通过" : "失败"));
            System.out.println("执行时间: " + result.getExecutionTime() + " ms");
            System.out.println("内存使用: " + result.getMemoryUsage() + " bytes");
        } else {
            System.out.println("执行失败: " + result.getErrorMessage());
        }
        
        System.out.println();
    }
}
