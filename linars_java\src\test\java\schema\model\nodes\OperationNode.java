package schema.model.nodes;

import schema.model.Node;

import java.util.ArrayList;
import java.util.List;

/**
 * 操作节点，表示具体操作，如获取当前位、计算和、更新结果等
 */
public class OperationNode extends Node {
    private String operationType; // 操作类型：initialization, data_access, arithmetic, data_update, return, etc.
    private List<String> parameters = new ArrayList<>(); // 操作参数

    public OperationNode(String id, String name, String operationType, List<String> parameters) {
        super(id, name, "OperationNode");
        this.operationType = operationType;
        if (parameters != null) {
            this.parameters.addAll(parameters);
        }
        setProperty("operation_type", operationType);
        setProperty("parameters", parameters);
    }

    public String getOperationType() {
        return operationType;
    }

    public List<String> getParameters() {
        return parameters;
    }
}
