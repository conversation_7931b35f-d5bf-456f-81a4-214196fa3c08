package schema.model;

import java.util.HashMap;
import java.util.Map;

/**
 * 节点基类，所有类型的节点都继承自此类
 */
public abstract class Node {
    private String id;
    private String name;
    private String nodeType; // 节点类型：ContextNode, DataNode, OperationNode, ControlNode
    private Map<String, Object> properties = new HashMap<>();
    private double activation = 0.0;

    public Node(String id, String name, String nodeType) {
        this.id = id;
        this.name = name;
        this.nodeType = nodeType;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setProperty(String key, Object value) {
        properties.put(key, value);
    }

    public Object getProperty(String key) {
        return properties.get(key);
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public double getActivation() {
        return activation;
    }

    public void setActivation(double activation) {
        this.activation = activation;
    }

    @Override
    public String toString() {
        return name + "(" + nodeType + ")";
    }
}
