/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.operator.misc;

import edu.memphis.ccrg.lida.attentioncodelets.AttentionCodeletImpl;
import edu.memphis.ccrg.lida.attentioncodelets.NeighborhoodAttentionCodelet;
import edu.memphis.ccrg.lida.pam.tasks.LearnTask;
import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
import org.opennars.entity.Task;
import org.opennars.interfaces.Timable;
import org.opennars.operator.Operation;
import org.opennars.operator.Operator;

import java.util.List;

import static edu.memphis.ccrg.lida.framework.FrameworkModuleImpl.taskSpawner;

/**
 * 学习操作符，精确构建时序模式
 */
public class Attent extends Operator {
    public Attent() {
        super("^attent");
    }

    @Override
    public List<Task> execute(final Operation operation, final Term[] args, final Memory memory, final Timable time) {
        // 这里是narsese操作，也就是图程执行，调用底层算法，等待输入=耗时操作，不能用narsese操作符
        // 底层有底层的调用，图程有图程的调用，先后天区别
        AttentionCodeletImpl attentionCodelet = new NeighborhoodAttentionCodelet();
        taskSpawner.addTask(attentionCodelet);
        return null;
    }

}
