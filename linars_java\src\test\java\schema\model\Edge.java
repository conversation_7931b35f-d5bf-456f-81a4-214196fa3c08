package schema.model;

import java.util.HashMap;
import java.util.Map;

/**
 * 边基类，所有类型的边都继承自此类
 */
public abstract class Edge {
    private String id;
    private Node source;
    private Node target;
    private String edgeType; // 边类型：SequenceEdge, SuccessionEdge, ConditionalEdge, LoopEdge, VariableEdge, DataFlowEdge
    private Map<String, Object> properties = new HashMap<>();

    public Edge(String id, Node source, Node target, String edgeType) {
        this.id = id;
        this.source = source;
        this.target = target;
        this.edgeType = edgeType;
    }

    public String getId() {
        return id;
    }

    public Node getSource() {
        return source;
    }

    public Node getTarget() {
        return target;
    }

    public String getEdgeType() {
        return edgeType;
    }

    public void setProperty(String key, Object value) {
        properties.put(key, value);
    }

    public Object getProperty(String key) {
        return properties.get(key);
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    @Override
    public String toString() {
        return source.getName() + " -[" + edgeType + "]-> " + target.getName();
    }
}
