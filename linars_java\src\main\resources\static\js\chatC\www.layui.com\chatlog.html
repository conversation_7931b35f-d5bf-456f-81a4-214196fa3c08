<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title>聊天记录</title>

  <link rel="stylesheet" href="../res.layui.com/layui/src/css/layui.css">
  <style>
    body .layim-chat-main{height: auto;padding-bottom: 45px;}
    .lay_page{position: fixed;bottom: 0;margin: 0 10px;background: #fff;width: 100%;}
    .layui-laypage{width: 105px;margin:0 auto;display: block}
  </style>
</head>
<body>

<div class="layim-chat-main pb45">
  <ul id="LAY_view"></ul>
</div>

<div class="lay_page" id="LAY_page" ></div>


<textarea title="消息模版" id="LAY_tpl" style="display:none;">
{{# layui.each(d.data, function(index, item){ }}
  {{# if(item.from == '0'){ }}
      <li class="layim-chat-system"><cite>{{ item.timestamp }}</cite><span>{{ layui.layim.content(item.content) }}</span></li>
  {{# } else { }}
    {{# if(item.from == parent.layui.layim.cache().mine.id){ }}
      <li class="layim-chat-mine"><div class="layim-chat-user"><img src="http://test.guoshanchina.com/uploads/person/{{ item.from }}.jpg"><cite><i>{{ item.timestamp }}</i>{{ item.fromName }}</cite></div><div class="layim-chat-text">{{ layui.layim.content(item.content) }}</div></li>
    {{# } else { }}
      <li><div class="layim-chat-user"><img src="http://test.guoshanchina.com/uploads/person/{{ item.from }}.jpg"><cite>{{ item.fromName }}<i>{{item.timestamp }}</i></cite></div><div class="layim-chat-text">{{ layui.layim.content(item.content) }}</div></li>
    {{# } }}
  {{# }
}); }}
</textarea>

<!--
上述模版采用了 laytpl 语法，不了解的同学可以去看下文档：http://www.layui.com/doc/modules/laytpl.html

-->
<script type='text/javascript' src='../res.layui.com/mods/webim.config.js'></script>
<!--<script type='text/javascript' src='../../../../../../static/js/strophe-1.2.8.min.js'></script>-->
<script type='text/javascript' src='../res.layui.com/mods/websdk.js'></script>

<script src="../res.layui.com/layui/src/layui.js"></script>
<script>
    layui.config({
        base: '../res.layui.com/mods/'
    }).extend({
        socket: 'socket'
    });
    layui.use(['layim', 'laypage','socket'], function (socket) {
        var layim = layui.layim
            ,layer = layui.layer
            ,laytpl = layui.laytpl
            ,$ = layui.jquery
            ,laypage = layui.laypage
            ,cachedata = parent.layui.layim.cache()

            //开始请求聊天记录
            ,param =  location.search; //获得URL参数。该窗口url会携带会话id和type，他们是你请求聊天记录的重要凭据

        function formatDate(nS) {
            return new Date(parseInt(nS)).toLocaleString();
        }

        var url = cachedata.base.getChatLogTotal.url || {};
//
        $.get(url + param, function(data){
            var res = eval('(' + JSON.stringify(data) + ')');
            if(res.code != 0){
                return layer.msg(res.msg);
            }

            laypage.render({
                elem: 'LAY_page'
                ,count: res.data.count
                ,limit: res.data.limit
                ,prev: '<i class="layui-icon">&#58970;</i>'
                ,next: '<i class="layui-icon">&#xe65b;</i>'
                ,layout: ['prev', 'next']
                ,curr: res.data.limit
                ,jump: function(obj, first){
                    //obj包含了当前分页的所有参数，比如：
                    var url = cachedata.base.getChatLog.url || {};
                    //首次不执行
//                    if(first){
//                        var page = res.data.limit;
//                    }else{
//                        var page = obj.curr - 1;
//                    }
//                    var group = param.substring(param.length - 5);
//                    var param0 = param.split("=");

//                    if ( param0[2] == "group"){
//
//                        var idStr = "?search_LIKE_group.name=";
//                        var searchId = param0[1].substring(0,param0[1].length - 5);
//                        var searchName = param0[1].substring(0,param0[1].length - 5);
//
//                        $.get(url + idStr + searchId , {
//                                        page: obj.curr - 1 || 0
//                                        ,type:"group"
//                                                      }, function(data){
//
//                            renderData(data);
//
//                        });
//                    } else {

//                    $.get(url+"LIKE_receiver.id = " + param.id + "", {}, function(data){
//                    $.get(url+'&'+param, {page: obj.curr || 1}, function(data){
//                    $.get(url + param, {page : 1}, function(data){

                    $.get(url + param, {page: obj.curr - 1 || 0}, function(data){
                          renderData(data);

//                        var res = eval('(' + JSON.stringify(data) + ')');
//                        if(res.code != 0){
//                            return layer.msg(res.msg);
//                        }
//                        layui.each(res.data.data, function(index, item){
//                            res.data.data[index]['timestamp'] =  formatDate(item.timestamp);
//                        });
//                        var html = laytpl(LAY_tpl.value).render({
//                            data: res.data.data
//                        });
//                        $('#LAY_view').html(html);
                    });
//                    }
                }
            });
        });

        function renderData(data) {
            var res = eval('(' + JSON.stringify(data) + ')');
            if(res.code != 0){
                return layer.msg(res.msg);
            }
            layui.each(res.data.data, function(index, item){
                res.data.data[index]['timestamp'] =  formatDate(item.timestamp);
            });
            var html = laytpl(LAY_tpl.value).render({
                data: res.data.data
            });
            $('#LAY_view').html(html);
        }

    });
</script>
</body>
</html>
