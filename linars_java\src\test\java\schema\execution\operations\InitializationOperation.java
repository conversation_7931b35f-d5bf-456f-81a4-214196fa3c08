package schema.execution.operations;

import schema.execution.ExecutionContext;
import schema.execution.Operation;

/**
 * 初始化操作，设置必要的变量和初始状态
 */
public class InitializationOperation implements Operation {
    @Override
    public Object execute(ExecutionContext context) {
        String operand1 = (String) context.getVariable("操作数1");
        String operand2 = (String) context.getVariable("操作数2");
        
        context.setVariable("结果", "");
        context.setVariable("进位", 0);
        context.setVariable("当前位索引", Math.min(operand1.length(), operand2.length()) - 1);
        
        return null;
    }
}
