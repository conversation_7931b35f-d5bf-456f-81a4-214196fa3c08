:root {
  --main-bg-color: #141414;
  --main-surface-bg-color: rgba(48, 48, 48, 0.9);
  --main-text-color:#ACACAC;
  --mask-color: #141414;
  --second-text-color:#7D7D7D;
  --main-text-active:#DBDBDB;
  --main-border-color:gray;
  --main-border-hover-color:green;
  --main-border-active:green;
  --main-icon-color:gray;
  --main-hover-bg-color:gray;
  --main-hover-text-color:white;
  --waring-color:#F37370;
  --disable-color:gray;
  --icon-hover-color:#65B7F3;
  --button-bg-color:transparent;
  --button-active-color:green;
  --divider-color:gray;
}

 body {
   margin: 0;
   padding: 0;
   font-family: "Lato", "Helvetica", sans-serif;
   font-size: 14px;
   overflow: hidden;
   overscroll-behavior-x: none;
   -webkit-touch-callout: none;
   /* iOS Safari */
   -webkit-user-select: none;
   /* Chrome/Safari/Opera */
   -khtml-user-select: none;
   /* Konqueror */
   -moz-user-select: none;
   /* Firefox */
   -ms-user-select: none;
   /* Internet Explorer/Edge */
   user-select: none;
   min-height: 768px;
 }

 .code-font {
   font-family: 'Times New Roman', Times, serif;
 }

 #react-dom {
   /* background: black; */
   height: 100%;
 }

 #main-panle {
   height: 100%;
 }

 a {
   cursor: pointer;
 }

 span,
 td {
   -webkit-touch-callout: none !important;
   /* iOS Safari */
   -webkit-user-select: text !important;
   /* Chrome/Safari/Opera */
   -khtml-user-select: text !important;
   /* Konqueror */
   -moz-user-select: text !important;
   /* Firefox */
   -ms-user-select: text !important;
   /* Internet Explorer/Edge */
   user-select: text !important;
 }

 span {
   display: inline;
 }

 /* ::selection {  
    color: gold;
   background-color: gray; 
  } */

 button {
   outline: none !important;
   background: rgb(44, 44, 44);
   margin: 10px;
   color: white;
 }

 .ignore-filter {
   pointer-events: visible !important;
 }

 .ignore-left-padding {
   padding-left: 0px !important;
   /* border: 1px red solid; */
 }

 .break-all {
   word-break: break-all;
 }

 /*fixed bootstap popover*/
 .popover {
   border-color: rgb(48, 48, 48);
   border-radius: 4px;
   z-index: 1490;
   max-width: unset;
   background-color: rgba(0,0,0,0.01);
   margin-right: 35px;
 }

 .bs-popover-right>.arrow {
   transform: rotate(180deg);
   right: calc(-.5rem - 1px);
   left: auto;
 }
 .bs-popover-auto[x-placement^=left] .arrow::after,
 .bs-popover-left .arrow::after {
   border-left-color: rgb(48, 48, 48);
 }

 .bs-popover-auto[x-placement^=right] .arrow::after,
 .bs-popover-right .arrow::after {
   border-right-color: rgb(48, 48, 48) !important;
 }

 .popover-inner {
   background-color: rgba(48, 48, 48, .9);
   border-radius: 5px;
 }

 #renderContainer {
   display: block !important;
   background-color:var(--main-bg-color);
}

 #canvas-renderContainer {
  background-color:var(--main-bg-color);
}

 #vr-actions {
   position: fixed;
   bottom: 10px;
   width: 100%;
   text-align: center;
   display: flex;
   justify-content: center;
   z-index: 1390;
 }

 /* #page {
     min-width: 865px;
     width: 100%;
     height: 100%;
     position: absolute;
     top: 0;
     left: 0;
     pointer-events: none;
 }

 #page>* {
     z-index: 999;
     position: fixed;
 } */

 /* .top-bottom-group {
     display: flex;
     flex-direction: column;
     width: 100%;
     height: 100%;
 } */

 /* .bottom-group {
     width: 100%;
     flex: 1;
 } */

 .kinput {
   /* position: relative; */
   height: 32px;
 }
 .kinput .loading{
   color: #7D7D7D;
 }
.kinput i.clear-icon {
   height: 100%;
   float: right;
   /* margin-top: -20px; */
   color: #7D7D7D;
   /* color: #fff; */
   cursor: pointer;
   z-index: 1390;
   pointer-events: auto;
   line-height: 31px;
}

 .kinput i.clear-icon:hover {
   color: #ACACAC;
 }

 .map-search .kinput i.clear-icon {
  position: absolute;
  top: 10px;
  right: 20px;
}

 .kinput>input {
   margin: 0;
   background: transparent;
   width: 100%;
   height: 100%;
   color: #DBDBDB;
   float: left;
   border: none;
   padding: 3px 30px 3px 6px
 }

 .kinput>.btn-clear {
   width: 34px;
   height: 26px;
   float: right;
   margin-top: -28px;
   font-size: 12px;
   line-height: 26px;
   padding: 0 5px;
   color: gray;
   display: none;
 }

 .kinput.has-clear .btn-clear {
   display: block;
   cursor: pointer;
 }

 ::-webkit-scrollbar {
   visibility: hidden;
   width: 0px;
   pointer-events: visible;
 }

 .kv-scrollbar ::-webkit-scrollbar {
   visibility: visible;
   width: 5px;
   height: 5px;
   pointer-events: visible !important;
 }

 .kv-scrollbar ::-webkit-scrollbar-track {
   /* box-shadow: inset 0 0 5px #dddddd;  */
   border-radius: 50px;
   pointer-events: visible;
   background-color: #303030;
 }

 .kv-scrollbar {
   pointer-events: visible;
 }

 .kv-scrollbar ::-webkit-scrollbar-thumb {
   border-radius: 50px;
   background-color: #434343;
   pointer-events: visible;
   cursor: pointer;
   transition: all .2s ease;
 }

 .kv-scrollbar ::-webkit-scrollbar-thumb:hover {
   background-color: #7D7D7D;
   pointer-events: visible;
 }

 .kv-scrollbar ::-webkit-scrollbar-corner {
   background-color: #303030;
   pointer-events: visible;
 }

 .kv-scrollbar.kv-scrollbar-default ::-webkit-scrollbar-track {
   background-color: #7D7D7D;
 }

 .kv-button {
   display: inline-block;
   padding: 5px 13px;
   margin: 5px;
   min-height: 32px;
   font-size: 14px;
   border-radius: 50px;
   border-style: solid;
   border-width: 1px;
   pointer-events: visible;
   background-color: #434343 ;
   border: 1px solid #434343 !important;
   color: #ACACAC !important;
   cursor: pointer;
   transition: all .15s ease;
 }

 .kv-button.active,
 .kv-button.active:hover{
   border-color: #ACACAC;
   color: #000 !important;
   cursor: pointer;
   background-color: #65B7F3;
 }

 .kv-button.inactive {
   color: #666666;
   border-color: #666666;
   cursor: default;
 }

 .kv-button:hover {
   color: #DBDBDB !important;
   background-color: rgba(146, 142, 141, 0.4);
 }

 .kv-button.btn-dark {
   border-color: gray;
 }

 .kv-input {
   border: 1px solid gray;
   color: #444;
   background: #DDD;
   margin: 10px 15px 20px 15px;
   height: 32px;
   border-radius: 32px;
   padding: 0 15px;
 }

 .kv-switch {
   display: flex;
   margin-bottom: 0;
 }

 .kv-switch .kv-button {
   height: 32px;
   line-height: 30px;
   margin: 0px 0;
   padding: 0;
   font-size: 14px;
   border: 1px solid #ACACAC;
   width: 50%;
   border-radius: 50px;
 }

 .kv-switch .kv-button:first-child {
   border-bottom-right-radius: 0;
   border-top-right-radius: 0;
 }

 .kv-switch .kv-button:last-child {
   border-bottom-left-radius: 0;
   border-top-left-radius: 0;
 }

 .kv-table-group {
   color: #dddddd;
 }

 .kv-ul {
   padding: 0;
   margin: 10px 0 !important;
   list-style: none;
 }

 .kv-ul li {
   float: left;
   margin: 2px;
 }

 .kv-ul li:last-child {
   margin-bottom: 1.5rem;
 }

 .kv-search {
   margin: 10px 6px;
   float: right;
 }

 .kv-search span {
   font-size: 14px;
   color: #ACACAC;
 }

 .kv-search input {
   border: none;
   border-radius: 50px;
   height: 32px;
   line-height: 32px;
   padding: 12px 20px;
   color: #ACACAC;
   background-color: #1d1d1d;
 }

 .kv-table-group .content {
   margin-top: 10px;
   clear: both;
   width: 100%;
   padding: 0 px;
   color: #DDD;
 }

 .kv-table,
 .kv-table>tbody>tr>td {
    border-color: transparent;
    border-bottom: 1px solid #262626;
    color: #ACACAC;
 }

 .kv-table>tbody tr:first-child td{
   color: #DBDBDB;
 }

 .kv-table>tbody>tr>td {
   padding-right: 15px;
 }

 .kv-table>tbody {
   border: none;
 }

 .kv-table tr:nth-child(2n+1) {
   /*background-color: rgba(100, 100, 100, 0.1);*/
 }
 .kv-table tr:nth-child(n+2):hover {
  background-color: rgba(0,0,0,.2);
}
.kv-table tr.active:hover {
  background-color: #3C9AE8;
}

 .kv-table {
   border-style: solid;
   border-width: 0 1px 1px 1px;
 }

 .kv-table tr:nth-child(n+2) {
   cursor: pointer;
 }

 .kv-table tr:first-child:hover {
   background-color: transparent;
 }

 .kv-range {
   display: flex;
   flex-direction: column;
   pointer-events: visible;
   padding: 10px 0;
 }

 .kv-range .min-max {
   font-size: 10px;
 }

 .kv-range .min-max .min {
   float: left;
 }

 .kv-range .min-max .max {
   float: right;
 }

 .kv-check-box {
   display: flex;
 }

 .kv-check-box .fa {
   font-size: 18px;
   margin-left: 10px;
   margin-top: 2px;
 }


 .kv-select {
   z-index: 1490;
   height: 32px;
 }

 .kv-select .select__control {
   min-height: 40px;
   height: 40px;
 }

 .kv-select.multi,
 .kv-select.multi .select__control {
   height: 32px;
 }

 .kv-select .select__control input {
   height: 26px !important;
 }

 .kv-select .select__control,
 .kv-select .select__control.select__control--menu-is-open {
  background-color: rgba(67, 67, 67, .9);
   border: 1px solid #303030;
 }

 .kv-select.legend-property-select .select__control,
  .kv-select.legend-property-select .select__control.select__control--menu-is-open {
   background-color: rgba(0,0,0,.9);
 }

 .kv-select .select__menu {
   box-shadow: none;
   border: 1px solid gray;
 }

 .kv-select .select__menu-list {
   background-color: transparent;
   border-color: gray;
   color: #DDD;
 }

 .kv-select .select__option.select__option--is-focused {
   color: #222 !important;
   background-color: rgba(255, 255, 255, 0.6) !important;
 }

 .kv-select .select__multi-value {
   background-color: gray;
   padding: 0 2px;
 }

 .kv-select .select__single-value,
 .kv-select .select__multi-value__label,
 .kv-select .select__multi-value__remove,
 .kv-select .select__input {
   color: #ACACAC;
   font-size: 14px;
 }

 .legend-property-select .select__single-value.css-1uccc91-singleValue {
  font-size: 18px;
 }

 .kv-select .select__multi-value__remove:hover,
 .kv-select .select__clear-indicator:hover,
 .kv-select .select__dropdown-indicator:hover {
   background-color: transparent;
   color: #DBDBDB;
 }

 .kv-select .select__menu {
   background-color: rgb(44, 44, 44);
 }


 .input-range {
   margin-top: 6px;
   display: flex;
   flex-direction: row;
   justify-content: space-between;
 }

 .input-range .input-group {
   display: flex;
   flex-direction: row;
   width: 264px;
 }

 .input-range .input-group.short-group {
   width: auto;
 }

 .input-range .input-group:first-child .rdt input {
   border-top-left-radius: 0;
   border-bottom-left-radius: 0;
 }

 .input-range .input-group:last-child .rdt input {
   border-top-right-radius: 0;
   border-bottom-right-radius: 0;
 }

 .input-range .input-group.short-group.fixed-range {
   line-height: 31px;
   width: 130px;
 }

 .input-range .input-group.short-group.fixed-range .fa {
   font-size: 18px;
   margin-right: 10px;
 }

 .input-range .input-group.small-group {
   /* width: 164px; */
   margin-left: 4px;
   margin-right: 4px;
 }

 .input-range .input-group .input-group-text {
   background-color: inherit;
   color: #ddd;
   cursor: pointer;
   border-color: #303030;
 }

 .input-range .input-group .form-control {
   min-width: 10px;
   width: 100px;
   padding: 6px 8px;
   color: #7D7D7D;
   background-color: #1D1D1D;
 }


 .input-range .rdtPicker {
   background-color: rgb(44, 44, 44);
   border-color: gray;
   border-radius: 4px;
 }

 /* .input-range .input-group:last-child .rdtPicker {
   margin-left: -106px;
 } */

 .input-range .rdtPicker tfoot,
 .input-range .rdtPicker th {
   border-color: gray;
 }

 .input-range .rdt .form-control {
   width: 152px;
 }

 .input-range .rdtPicker .rdtCounter .rdtBtn:hover,
 .input-range .rdtPicker td:hover,
 .input-range .rdtPicker button:hover,
 .input-range .rdtPicker th:hover,
 .input-range .rdtPicker .rdtTimeToggle:hover {
   background-color: #428bca !important;
   color: #fff;
   text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
 }


 .header-fixed>thead {
   font-weight: 600px;
 }

 .header-fixed>thead,
 .header-fixed>tbody,
 .header-fixed>thead>tr,
 .header-fixed>tbody>tr,
 .header-fixed>thead>tr>th,
 .header-fixed>tbody>tr>td {
   display: block;
 }

 .header-fixed>tbody>tr:after,
 .header-fixed>thead>tr:after {
   content: ' ';
   display: block;
   visibility: hidden;
   clear: both;
 }

 .header-fixed>tbody {
   overflow-y: auto;
   max-height: 450px;
   background-color: #303030;
 }

 .header-fixed tr {
   border-bottom: 1px solid #1D1D1D;
 }

 .header-fixed>tbody>tr>td:first-child,
 .header-fixed>thead>tr>td:first-child {
   width: 40%;
   float: left;
   text-align: left;
   word-break: break-all;
 }

 .header-fixed>tbody>tr>td:nth-child(n+2),
 .header-fixed>thead>tr>td:nth-child(n+2) {
   width: 15%;
   float: left;
   text-align: center;
 }

 .nav-tabs {
   background-color: transparent;
   pointer-events: visible;
   border-bottom: 1px solid #7D7D7D;
 }

 .nav-tabs>li>a {
   border: none;
   border-radius: 0px;
   color: #ACACAC;
   transition: all .15s ease;
 }

 .nav-tabs>li>a:hover,
 .nav-tabs>li>a:focus {
  color: #DBDBDB !important;
  border: 1px solid transparent !important;
 }

 .nav-tabs>li>a.active:hover,
 .nav-tabs>li>a.active,
 .nav-tabs>li>a.active:focus {
   border: 1px solid #7D7D7D !important;
   color: #DBDBDB !important;
   background-color: #434343 !important;
   border-bottom-color: transparent !important;
   border-radius: 5px 5px 0 0 !important;
 }

 .nav-tabs>li>a.active:focus,
 .nav-tabs>li>a.active:hover {
   cursor: default;
 }

 .nav-tabs>li:first-child {
   padding-left: 10px;
 }

 .custom-drags .actions {
   position: absolute;
   right: 0;
   top: 0;
   padding: 0 5px;
 }

 .custom-drags .actions .drag {
   cursor: move;
 }

 .custom-drags .actions .close,
 .custom-drags .actions .drag {
   font-weight: 700;
   line-height: 1;
   color: #000;
   text-shadow: 0 1px 0 #fff;
   opacity: 0.8;
   font-size: 20px;
   width: 20px;
   height: 20px;
   color: white;
   float: right;
   margin-top: 6px;
   margin-right: 6px;
   padding: 0;
   background-color: transparent;
   border: 0;
 }

 /*detail-table*/
 .detail-table{
  background-color: rgba(48, 48, 48, .85);
}

 /*----start---graphxr-modal*/

 #graphxr-modal {
   background-color: rgb(0, 0, 0, 0.6);
 }

 #graphxr-modal .modal-dialog {
   max-width: 600px;
   height: 100%;
   top: 80px;
 }

 #graphxr-modal .modal-content {
  color: #ACACAC;
  height: auto;
  background: rgba(48, 48, 48, 0.9);
  border: 1px solid #303030;
  border-radius: 5px;
 }

 #graphxr-modal .modal-content.webIframe {
   height: 454px;
 }

 #graphxr-modal .modal-content.nodeInfo .modal-header {
   border-bottom: none;
 }

 #graphxr-modal .modal-header {
   padding: 10px;
   color: #DDD;
   border-bottom: 1px solid #7D7D7D;
 }

 #graphxr-modal .modal-header .close {
   color: #DDD;
   opacity: 0.5;
   margin-right: 0px;
 }

 #graphxr-modal .modal-header .close:focus,
 #graphxr-modal .modal-header .close:hover {
   opacity: 1.0;
 }

 #graphxr-modal .modal-body {
   padding: 0px;
 }

 /*----end---graphxr-modal*/

 /*----start---left-toolbar*/

 .left-toolbar {
   /* width: 124px; */
   width: 40px;
   height: 100%;
   text-align: center;
   background: #141414;
   pointer-events: visible;
   border-right: 1px rgb(41, 39, 39, 0.8) solid;
   position: relative;
   z-index: 1390;
 }

 .left-toolbar.show-panel-info {
   width: 124px;
   background: rgb(67, 67, 67, .85);
 }

 .left-toolbar .show-info,
 .left-toolbar .top,
 .left-toolbar .bottom {
   margin: 0 5px;
   position: absolute;
 }
 .left-toolbar .show-info{
   top: 20px;
 }
 .left-toolbar .top {
   top: 70px;
 }

 .left-toolbar .bottom {
   bottom: 20px;
 }

 .left-toolbar span {
   color: #ACACAC;
   padding: 5px;
   font-size: 20px;
   text-align: center;
   transition: color .15s ease;
 }

 .left-toolbar span:hover,
 .left-toolbar .tool-item.active {
  color: #DBDBDB;
 }

 .left-toolbar .tool-item {
   position: relative;
   /* border-bottom: 1px dotted black; */
   text-align: center;
   margin-top: 15px;
   cursor: pointer;
 }

 /* .left-toolbar .tool-item.show-info {
   color: grey;
   font-size: 20px;
   margin-left: 10px;
   float: left;
 }

 .left-toolbar .tool-item.show-info:hover {
   color: #fff;
 } */

 .left-toolbar .tool-item .fa.active {
   color: #fff;
 }

 .left-toolbar .tool-item .iconfont {
   font-size: 20px;
 }

 .left-toolbar .tool-item:first-child {
   margin-top: 0;
 }

 .left-toolbar .tool-item .tooltiptext {
   /* visibility: hidden; */
   display: none;
   color: #fff;
   float: right;
   top: 0;
   left: 28px;
   text-align: left;
   padding: 5px 6px;
   font-size: 12px;
   background: rgb(41, 39, 39, 0.5);
   min-width: 40px;
   margin-left: 6px;
   margin-right: -40px;
   width: max-content;
   position: absolute;
   z-index: 99;
 }

 .left-toolbar.show-panel-info .tool-item .tooltiptext {
   display: block;
   background: rgb(41, 39, 39, 0);
 }

 .left-toolbar.show-panel-info .tool-item .tooltiptext.faster.animated {
   -webkit-animation-duration: 0ms !important;
   animation-duration: 0ms !important;
 }


 .left-toolbar .tool-item:hover .tooltiptext {
   /* visibility: visible; */
   display: block;
 }

 .left-toolbar .tool-item .tooltipdetail {
   visibility: hidden;
   color: #fff;
   text-align: left;
   padding: 5px 6px;
   font-size: 12px;
   background: rgb(41, 39, 39, 1);
   min-width: 120px;
   margin-left: 6px;
   position: absolute;
   z-index: 12;
 }

 .left-toolbar .tool-item:hover .tooltipdetail {
   transition-delay: 2s;
   visibility: visible;
 }

 .left-group.vr-only{
   height: 40px;
   bottom: 0;
 }

 .left-group.vr-only .left-toolbar{
  background: none;
  border: none;
 }


 .in-neo4j-desktop .left-toolbar .item-home {
   display: none;
 }
 
@media (max-height: 500px) {
  .left-toolbar .top{
    top: 0;
  }

  .left-toolbar  .show-info {
    display: none;
  }
}

 @media (max-height: 540px) {
  
  .left-toolbar #vr-button {
    display: none;
  }

  .left-group.vr-only .left-toolbar #vr-button {
    display: block;
  }
}

 @media (max-height: 580px) {
   .left-toolbar .item-home {
     display: none;
   }
 }

 @media (max-height: 620px) {
  .left-toolbar .item-logout {
    display: none;
  }
}

 @media (max-height: 660px) {
   .left-toolbar .item-shortcut {
     display: none;
   }
 }



 /*----end---left-toolbar*/

 .search-group {
   align-self: center;
   position: relative;
 }

 .search-box {
   height: 32px;
   align-self: center;
   margin-right: 10px;
   min-width: 420px;
   border-radius: 50px;
   padding-left: 10px;
   padding-right: 10px;
   border: 1px solid;
   background: rgb(44, 44, 44);
   border-color: rgb(222, 222, 222, 0.1);
 }

 .search-result {
   width: 30px;
   height: 10px;
   background: #00B7FF;
 }

 /**----start---create-tag-panel  */

 .create-tag-panel {
   display: flex;
   flex-direction: column;
 }

 .create-tag-panel .tags {
   margin: 20px 10px 10px 10px;
 }

 .create-tag-panel .tags button,
 .create-tag-panel .tags input {
   float: left;
   align-self: center;
   margin-right: 5px;
   border-radius: 40px;
   border: 1px solid gray;
   margin: 5px;
   background: gray;
   padding-left: 15px;
   padding-right: 15px;
   background: transparent;
   height: 32px;
 }

 .create-tag-panel .tags input {
   color: #444;
   background: #DDD;
 }

 .create-tag-panel .tags button{
   cursor: default;
 }
 .create-tag-panel .tags button .fa{
  cursor: pointer;
 }
 .create-tag-panel .tags button span {
   margin-left: 5px;
   margin-right: 5px;
 }

 .create-tag-panel  .add-new-tag {
   border: 1px solid gray;
   color: #444;
   background: #DDD;
   margin: 10px 15px 20px 15px;
   height: 32px;
   border-radius: 32px;
   padding: 0 15px;
 }

 .create-tag-panel  .add-new-tag .input-group{
  height: 32px;
  line-height: 32px;
 }
 .create-tag-panel .add-new-tag {
  background-color: #1d1d1d;
  border: none;
 }
 .create-tag-panel .add-new-tag  input{
  color: #ACACAC;
  border: none;
  padding:  0;
  height: 100%;
  border-radius: 0;
  background-color: transparent;
 }
 .create-tag-panel .add-new-tag  input:placeholder{
   color: #5A5A5A;
 }
 .create-tag-panel .add-new-tag .input-group-append{
  height: 32px;
  border-radius: 0;
  background: #434343;
  color: #ACACAC;
  padding: 0 15px;
  margin-right: -15px;
  border-top-right-radius: 32px;
  border-bottom-right-radius: 32px;
  line-height: 30px;
  cursor: pointer;
 }

 .create-tag-panel .actions {
   border-top: 1px solid #7D7D7D;
   padding: 10px;
   text-align: right;
 }

 .create-tag-panel .actions .kv-button {
   /*border-color: gray;*/
   margin-left: 10px;
   font-size: 16px;
   padding: 4px 20px;
   border-radius: 34px;
 }


 /**----end---create-tag-panel  */

 /*---start---left-group */

 .left-group {
   height: 100%;
   position: fixed;
   z-index: 1490;
 }

 .logo-color {
   color: #00B7FF;
 }

 .info-color {
   color: rgb(6, 214, 160);
 }

 .left-slider-panel {
   padding: 0;
   background: rgb(48, 48, 48, .9);
   min-width: 680px;
   max-width: 65%;
   width: 680px;
   height: 100%;
	 display: block;
   pointer-events: visible;
   transform: translateX('-100%');
   transition: all 0.3s ease 0s;
   position: fixed;
   top: 0;
   left: 40px;
 }

 .left-slider-panel.query input {
    color: #7D7D7D !important;
    background-color: #1d1d1d;
    margin-left: 0;
    padding: 5px 10px;
    border-radius: 5px;
    border: none;
 }

 .left-slider-panel.query input[type=file] {
   position: relative;
   z-index: 0;
 }

 .left-slider-panel.show-panel-info {
   left: 124px;
 }

 .left-slider-panel.transform {
   width: 620px;
 }

 .left-slider-panel .slider-title {
   display: flex;
   flex: 0 1 auto;
   color: #ACACAC;
   justify-content: flex-end;
   padding-bottom: 20px;
   padding-top: 21px;
 }

 .left-slider-panel .slider-title.no-search {
   padding-bottom: 0;
   padding-top: 10px;
 }

 .left-slider-panel .slider-title span {
   margin-right: 10px;
   pointer-events: visible;
   cursor: pointer;
 }

 .left-slider-panel > .content {
   flex: 1 1 auto;
   width: 100%;
	 height: 100%;
   position: relative;
 }

 .left-slider-panel > .content .empty {
   margin: 10px;
   padding-top: 30px;
   text-align: center;
   font-size: 14px;
   color: #7D7D7D;
 }

 .left-slider-panel .layout-panel {
   height: 100%;
 }

 .left-slider-panel .layout-panel-hidden {
    display: none;
 }

 .layout-panel .nav-tabs {
   height: 38px;
   z-index: 1090;
 }

 .layout-panel .tab-content {
   padding: 30px 10px 30px 10px;
   /*height: calc(100% - 60px - 38px);*/
   height: calc(100%);
   z-index: 1080;
 }

 .layout-panel .tab-content > .tab-pane.active {
   padding-bottom: 0px;
   display: flex;
   flex-flow: column;
   height: 100%;
   color: #ACACAC;
 }

 .layout-panel .tab-content > .tab-pane.active.extensions {
  height:calc(100% + 30px);
  }


 .left-slider-panel.layout .layout-panel .tab-content>.tab-pane.active > div,
 .left-slider-panel.algorithm .layout-panel .tab-content>.tab-pane.active > div,
 .left-slider-panel.query .layout-panel .tab-content>.tab-pane.active > div{
  overflow-y: auto;
  height: 100%;
  padding-top: 10px;
}

 .layout-panel .tab-content .expand-group {
   border: 1px solid gray;
   border-radius: 4px;
   margin-bottom: 10px;
 }

 .layout-panel .tab-content .expand-group.hide .form-group {
   display: none;
 }

 .layout-panel .tab-content .expand-group .form-group {
   margin-top: 10px;
 }


 .layout-panel .tab-content .expand-group:nth-child(n+1) {
   margin-top: 20px;
 }


 .layout-panel .tab-content .expand-group .expand-group-title {
   padding: 5.5px;
 }

 .layout-panel .tab-content>.tab-pane.active select {
  font-family: "Lato", "Helvetica", sans-serif;
  font-size: 14px;
  padding: 0 10px;
  border-radius: 5px;
  color: #ACACAC;
  border: 1px solid #303030;
  background-color: #434343;
  background-image: url(../assets/texture/ui/item/down.svg), linear-gradient(to right, #ccc, #ccc);
  height: 40px;
  box-sizing: border-box;
  -webkit-appearance: none;
  background-repeat: no-repeat;
  background-position: calc(100% - 10px), calc(100% - 32px) 8px;
  background-size: 12px, 1px 22px;
 }


  .layout-panel .tab-content .extension-items-div{
    width: 70%;
  }

  .layout-panel .tab-content .extension-items{
    margin-left: 10px;
    margin-right: 10px;
    height: 38px;
    overflow-y: auto;
    border-style: solid;
    border-width: 1px;
    border-color: lightgray;
  }

  .layout-panel .tab-content .extension-iframe{
    width: 100%;
    position: relative;
    padding-bottom: 0px;
    flex: 1 1 auto;
  }

  .extension-title {
    flex: 0 1 auto;
  }

  .td0 {
      position: absolute;
      z-index: 1000;
      margin-left: 470px;
      margin-top: 10px;
  }

  .bjqTTM,
  .gCgJEY {
    color: #ACACAC !important;
  }

  .cm-s-mbo.CodeMirror {
    background: #1d1d1d !important;
    color: #ACACAC;
    margin-right: 70px;
  }

  .css-1hwfws3 {
    position: initial !important;
  }

  /*CSV pagination*/
  .Table__pageButton {
    color: #7D7D7D !important;
    font-size: 14px !important;
  }
  .rt-thead.-header {
    color: #DBDBDB;
  }
 /*---end-layout-panel--*/

 /*---start-save-to-neo4j ---*/
 .left-slider-panel #save-to-neo4j {
   padding: 15px 0;
   position: fixed;
   width: 100%;
   top: 66px;
   left: 240px;
   background-color: rgb(44, 44, 44);
   border: solid 1px gray;
   border-radius: 4px;
   z-index: 1390;
 }

 #save-to-neo4j input {
   color: #DDDDDD;
 }

 #save-to-neo4j .form-control {
   min-width: 100px;
 }

 #save-to-neo4j .actions {
   padding-right: 15px;
 }

 #save-to-neo4j .action-close {
   margin-top: -10px;
   margin-right: 10px;
   cursor: pointer;
 }

 #save-to-neo4j .schema-config {
   max-height: 160px;
   overflow-y: auto;
   margin: 6px auto 20px auto;
   background-color: rgba(16, 16, 16, 0.8);
   border-radius: 4px;
   color: #DDD;
 }

 #save-to-neo4j .schema-config .label-item {
   width: 30%;
   float: left;
   font-size: 16px;
   margin-top: 10px;
   margin-bottom: 10px;
   padding: 0 10px;
 }

 #save-to-neo4j .schema-config .label-item:nth-child(3n+2) {
   margin-left: 5%;
   margin-right: 5%;
 }

 #save-to-neo4j .schema-config .label-item .props-list .list {
   max-height: 100px;
   overflow-y: auto;
 }

 #save-to-neo4j .schema-config .label-item .label-name {
   font-weight: 600;
   white-space: nowrap;
   overflow: hidden;
   text-overflow: ellipsis;
   height: 32px;
 }

 #save-to-neo4j .schema-config .label-item .label-name .name {
   padding: 0px 10px;
   border: 1px solid gray;
   line-height: 24px;
   border-radius: 14px;
   max-width: 100%;
   white-space: nowrap;
   overflow: hidden;
   text-overflow: ellipsis;
   user-select: none !important;
   display: inline-block;
 }

 #save-to-neo4j .schema-config .label-item .label-name .name.active {
   background-color: rgba(0, 255, 0, 0.1);
   border-color: green !important;
 }

 #save-to-neo4j .schema-config .label-item .prop-title {
   border-bottom: 1px dashed gray;
 }

 #save-to-neo4j .schema-config .label-item .prop-item {
   cursor: pointer;
 }

 #save-to-neo4j .schema-config .label-item .prop-item>div {
   float: left;
 }

 #save-to-neo4j .schema-config .label-item .prop-item .prop-action {
   width: 16px;
 }

 #save-to-neo4j .schema-config .label-item .prop-item .prop-name {
   width: 100%;
   white-space: nowrap;
   overflow: hidden;
   text-overflow: ellipsis;
   padding-left: 18px;
   margin-left: -16px;
 }

 /*---end-save-to-neo4j ---*/

 /*--start-connector--*/
 .formula-help-popover {
   top: -288px !important;
 }

 .formula-help-popover .popover-body{
  width: 420px;
  overflow-y: auto;
  max-height: 280px;
  color: #DDD;
}

input[type=checkbox] { 
  display:none; 
}
input[type=checkbox] + label:before,
input[type=checkbox] + span:before {
  font-family: FontAwesome;
  display: inline-block;
}
input[type=checkbox] + label:before,
input[type=checkbox] + span:before { 
  content: "\f096"; 
}
input[type=checkbox] + label:before,
input[type=checkbox] + span:before { 
  letter-spacing: 10px; 
}
input[type=checkbox]:checked + label:before,
input[type=checkbox]:checked + span:before { 
  content: "\f046" !important; 
} 
input[type=checkbox]:checked + label:before,
input[type=checkbox]:checked + span:before { 
  letter-spacing: 8px; 
}

input[type='checkbox']:after{
  line-height: 1.5em;
  content: '';
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-top: -4px;
  margin-left: -4px;
  border: 1px solid rgb(192,192,192);
  border-radius: 0.25em;
  background: rgb(224,224,224);
}

input[type='checkbox']:checked:after {
 width: 15px;
  height: 15px;
  border: 3px solid #00ff00;
}

 .left-slider-panel.transform .layout-panel .tab-content>.tab-pane>div {
   height: 100%;
   overflow-y: auto;
 }

 .left-slider-panel.transform .tab-content>.tab-pane table.table {
   color: #DBDBDB;
   background-color: rgb(222, 222, 222, 0);
   width: 100%;
   margin: 0;
   padding: 0;
 }

 .left-slider-panel.transform .tab-content>.tab-pane table.table td {
   overflow: hidden;
   text-overflow: ellipsis;
   max-width: 200px;
   min-width: 120px;
   border: 1px solid #262626;
   border-top: none;
   border-left: none;
   word-break: break-all;
 }

 .left-slider-panel.transform .table-extract>table td {
   max-width: 300px !important;
 }

 .left-slider-panel.transform .table-extract>table td.key {
   max-width: 48px !important;
   min-width: 48px !important;
 }

 .left-slider-panel.transform .tab-content>.tab-pane table.table td:last-child {
   border-right: none;
 }

 .left-slider-panel.transform .tab-content>.tab-pane table.table thead td {
   border-top: 1px solid rgb(222, 222, 222, 0.1);
 }

 .left-slider-panel.transform .tab-content>.tab-pane .table-scroll {
   overflow: auto;
   background-color: rgba(48, 48, 48, .85);border-left: none;
   border-right: none;
   margin: 15px;

 }

 .left-slider-panel.transform td .title {
   line-height: 38px;
   color: #ACACAC;
   white-space: nowrap;
   overflow: hidden;
   text-overflow: ellipsis;
 }

 .left-slider-panel.transform td .title.inline {
   display: flex;
   justify-content: space-between;
 }

 .left-slider-panel.transform td .sub-title {
   color: #888;
 }

 .left-slider-panel.transform .tab-content>.tab-pane .table-samples {
   max-height: 140px;
 }

 .left-slider-panel.transform .tab-content>.tab-pane .table-samples td {
   word-break: break-all;
 }

 .left-slider-panel.transform .tab-content>.tab-pane .table-samples tbody {
   color: #888;
 }

 .left-slider-panel.transform .tab-content>.tab-pane table.table td.td-center {
   text-align: left;
 }

 .left-slider-panel.transform .tab-content>.tab-pane table.table td .wrap-inline input {
   height: auto;
   padding: 0;
 }

 .left-slider-panel.transform .form-control {
   min-width: 100px;
   width: 100%;
 }

 .left-slider-panel.transform .aggregate-prop {
   margin-bottom: 15px;
 }

 .left-slider-panel.transform .aggregate-prop .kv-switch {
   margin: 5px 0 10px 0;
 }

 .left-slider-panel.transform .aggregate-title {
   align-self: baseline;
 }

 .left-slider-panel.transform .aggregate-title .sub-title {
   color: #888;
   margin-top: 10px;
   word-break: break-all;
   max-height: 60px;
   overflow: hidden;
 }

 .left-slider-panel.transform .token-show {
   height: 36px;
   width: 32px;
   text-align: center;
   line-height: 36px;
   font-size: 20px;
   cursor: pointer;
   position: absolute;
   right: 16px;
   bottom: 1px;
   background: rgba(0, 0, 0, 0.6);
   border-radius: 4px;
 }

 .left-slider-panel.transform .token-show~input {
   padding-right: 38px !important;
 }


 .left-slider-panel.transform .form-inline {
   margin-bottom: 10px;
 }

 .left-slider-panel.transform input,
 .left-slider-panel.transform select,
 .left-slider-panel.transform textarea {
   width: 100%;
   padding: 0 10px;
   height: 38px;
   border-radius: 4px;
   color: #DDD;
   border: none;
   background-color: #1d1d1d;
 }

 .left-slider-panel.transform input[type=checkbox] {
   height: auto;
 }

 .left-slider-panel.transform textarea {
   margin-top: .5rem;
   padding: .5rem 1rem;
   height: 100px;
   font-size: 14px;
   line-height: 22px;
 }

 .left-slider-panel.transform .form-inline label {
   justify-content: left;
 }

 .left-slider-panel.transform .form-inline label a.powered-by {
   margin-left: 4px;
   font-size: 600;
 }

 .left-slider-panel.transform .bottom-acitons {
   margin: 0 10px;
 }

 .left-slider-panel.transform .logs {
   margin: 10px 0;
   max-height: 80px;
   overflow-y: scroll;
 }

 .left-slider-panel.transform .logs .log-item {
   word-break: break-all;
   white-space: nowrap;
   text-overflow: ellipsis;
 }

 .left-slider-panel.transform .kv-select {
   z-index: auto;
   width: 100%;
 }

 .left-slider-panel.transform .param-table .table_header{
    display: flex;
    align-items: center;
 }

 left-slider-panel.transform .param-table th{
   vertical-align: middle;
 }

 /*--end-connector--*/
 .query-group {
   display: flex;
   flex-direction: column;
   padding: 0 10px 10px 10px;
   height: 100%;
 }

 .query-group hr {
   display: block;
   height: 1px;
   border: 0;
   border-top: 1px solid gray;
   margin: 1em 0;
   padding: 0;
 }

 .query-group .query-header {
   display: flex;
   color: #ACACAC;
   justify-content: space-between;
 }

 .query-group .query-header>div {
   margin-bottom: 0;
 }

 .query-group .query-header .title {
   margin-left: 0;
   padding-left: 0;
 }

 .query-group .query-header .max-results {
   padding-right: 56px;
   margin-right: 0;
   pointer-events: visible;
   width: 460px;
 }

 .query-group .query-header .max-results input {
   height: 26px;
 }

 .query-group .node input {
   height: 30px;
 }

 .query-command-group {
   position: relative;
   color: grey;
   padding: 0 5px 35px 0px;
   width: 100%;
   z-index: 1000;
 }

 .query-command-group .dropdown {
  width: 100%;
  max-height: 200px;
  margin: 0;
  background: rgb(44, 44, 44);
  overflow-y: scroll;
 }

 .query-command-group .dropdown ul {
   font-size: 12px;
   list-style: none;
   cursor: pointer;
   border: 1px gray solid;
   padding: 0px;
   margin: 0px;
   height: 100%;
   overflow-y: auto;
 }

 .query-command-group .dropdown li {
   border-top: gray 1px dotted;
   padding: 5px;
 }

 .query-command-group .dropdown li>div {
   display: flex;
 }

 .query-command-group .dropdown .content {
   width: 100%
 }

 .query-command-group .dropdown .content:hover {
   color: white;
 }

 .query-command-group .delete-button {
   align-self: center;
   padding: 5px
 }

 .query-command-group .delete-button:hover {
   color: white;
 }

 .query-command-group .icon {
   padding: 5px;
 }

 .query-command-group .icon:hover {
   color: white;
 }

 .query-command-group .top {
   width: 100%;
   display: flex;
   justify-content: flex-start;
   align-items: center;
   margin-bottom: 10px;
 }

 .query-command-group>div {
   z-index: 1100;
 }

 .query-command-group .result-table {
    position: absolute;
    top: 140px;
    margin: 0;
    width: 100%;
    height: 100%;
    margin-top: 10px;
    z-index: 1000;
 }

 .query-command-group .result-table .actions {
   position: absolute;
   margin-top: -46px;
   display: flex;
   right: 0;
 }

 .query-command-group .result-table .actions .kv-button {
   margin-right: 10px;
 }

 .query-command-group .result-table .detail-table {
   height: 100%;
   max-height: 100%;
 }

 .common-bar {
   display: flex;
   align-items: center;
   justify-content: flex-start;
 }

 .common-bar button {
   margin: 10px;
   background: transparent;
   color: grey;
   border: 0px;
 }

 .common-bar button:hover {
   color: white;
 }

 .right-panel {
   display: flex;
   flex-direction: column;
   flex: 1 0 auto;
 }

 .right-panel .content {
   position: absolute;
   right: 0px;
   top: 20px;
   width: 600px;
   height: 500px;
   transition: all 1s ease;
   overflow: hidden;
   pointer-events: none;
 }

 .right-panel .content-extra {
   transition: all 1s ease;
   overflow: hidden;
   pointer-events: none;
 }

 .right-panel .content::-webkit-scrollbar {
   width: 0px;
   display: none;
 }

 .right-panel .menu {
   pointer-events: visible;
   cursor: pointer;
 }

 .right-panel .menu ul {
   float: right;
 }

 .right-panel .menu li {
   float: left;
   list-style: none;
 }

 .parallelogram {
   width: 100px;
   height: 32px;
   cursor: pointer;
   border-left: 1px solid #303030;
   background-color: rgba(48, 48, 48, .9);
   display: flex;
    align-items: center;
  justify-content: center;
 }

 .right-panel .menu li:first-child .parallelogram {
   border-top-left-radius: 50px;
   border-bottom-left-radius: 50px;
   border: none;
 }

 .right-panel .menu li:last-child .parallelogram {
   border-top-right-radius: 50px;
   border-bottom-right-radius: 50px;
 }
  .right-panel .button-group .kv-select .css-yk16xz-control {
    background-color: rgba(0,0,0,.9);
    border: 1px solid #5A5A5A;
}

 /* .parallelogram-label {} */

 /* .parallelogram-node {} */
 .parallelogram {
  transition: all .2s ease;
 }

 .parallelogram span {
  color: #ACACAC;
  font-size: 14px;
  line-height: 28px;
  transition: all .2s ease;
 }

 .parallelogram-select,
 .parallelogram-select:hover{
   background-color: #8DCFF8 !important;
 }

 .parallelogram-select span,
 .parallelogram-select:hover span{
  color: #000 !important;
}

 .parallelogram:hover {
   background-color: #434343;
   /* box-shadow: inset 0 0 100px 20px greenyellow */
 }

 .parallelogram:hover span {
  color: #DBDBDB;
 }

 .node-info-group {
   bottom: 60px;
   right: 20px;
   background: rgba(48, 48, 48, 0.9);
   border: solid 1px #303030;
   position: fixed;
   z-index: 1390;
   width: 100%;
   min-height: 480px;
   /* max-height: 480px; */
   height: 480px;
   width: 400px;
   overflow: hidden;
   border-radius: 4px;
 }

 .node-info-group .nav-tabs li:first-child {
   padding-left: 0;
 }

 .node-info-group .tab-content {
   height: 100%;
   width: 100%;
 }

 .node-info-group .tab-content .tab-pane,
 .node-info-group .node-info {
   height: 100%;
   width: 100%;
 }
 .node-info-group .nav-tabs {
   margin-top: -1px;
   margin-left:-1px;
 }
 
 .node-info .node-labes {
   margin: 10px 0 10px 10px;
 }

 .node-info .label {
   border-radius: 30px;
   height: 30px;
   background: #00B7FF;
 }

 .node-info .node-info-table {
   height: 100%;
   width: 100%;
   padding-top: 160px;
   margin-top: -160px;
 }

 .node-info .node-info-table .content {
   width: 100%;
   height: 100%;
   overflow-y: auto;
 }

 .node-info .node-info-table .kv-table tr:nth-child(2n+1) {
   /*background-color: rgba(100, 100, 100, 0.2);*/
 }

 .node-info .node-info-table .kv-table tr td input {
   height: 100%;
   width: 100%;
   border: none;
   border-bottom: 1px solid #7D7D7D;
   background-color: inherit;
   color: #ddd;
   height: 32px;
 }
 .node-info .node-info-table .kv-table tr td textarea {
    border: 0;
    border-bottom: 1px solid var(--main-border-color);
    color: var(--main-text-color);
    background-color: inherit;
    height: 32px;
    box-shadow: inset 0px 2px 5px -5px grey;
    padding: 4px 10px;
}

 .node-info table {
   max-height: 100%;
   /* min-height: 320px; */
   margin-bottom: 0;
   overflow-y: auto;
   border-left: none;
   border-right: none;
   background-color: #303030;
 }

 .node-info thead>div {
   margin: 10px;
 }

 .node-info tbody {
   border: none;
   overflow: auto;
 }

 .node-info td {
   color: #ddd;
   padding: 10px 5px;
   border-left: none;
 }

 .node-info .table>thead>tr>td {
   border-top: none;
 }

 .node-info .table tr>td:first-child {
   font-weight: 600;
   padding-left: 16px;
 }

 .node-info .btn-group {
   margin: 10px;
 }

 .node-info .btn-group .kv-button {
   margin-right: 10px;
 }

 /*--start lengend--*/

 .current-legend {
   position: fixed;
   right: 20px;
   top: 20px;
   z-index: 1000;
   height: calc(100% - 225px);
   display: flex;
   flex: 1 0 auto;
 }

 .current-legend .legend-content {
   transition: all 1s ease;
   pointer-events: none;
   max-width: 600px;
   min-height: 0; /* https://stackoverflow.com/a/66689926/541101 */
 }

 .current-legend .legend-content-half {
   max-height: 50%; /* needed when there are two legend-contents (i.e. category + relationship). Otherwise they will distribute the height inequally and sometimes conten will be cut off */
 }

 .current-legend .content-group {
   position: relative;
   display: flex;
   flex-direction: column;
   max-height: 90%;
   flex-grow: 1;
 }

 .current-legend .legend-shown {
  transform: translateX(0%);
  padding-bottom: calc(var(--spacing-medium));
  margin-bottom: 0.5rem;
 }

 .current-legend .no-shrink {
   flex-shrink: 0; /* if Use Scale Color is on, avoid shrinking the property legend, because it is a fixed height */
   z-index: 1000000; /* needed to make sure the scale color chooser appears on top of the other legend contents */
 }

 .current-legend .no-shrink .button-group {
   overflow: visible !important; /* needed to make sure the scale color chooser appears on top of the other legend contents */
 }

 .current-legend .legend-hidden {
  transform: translateX(110%); 
  height: 0;
  /* display: none; */
 }

 .current-legend .button-group {
   max-width: 600px;
   max-height: 100%;
   overflow-y: auto;
   overflow-x: hidden;
   pointer-events: visible;
 }

 .current-legend .button-group-container {
   display: flex;
   flex-direction: row;
   justify-content: flex-end;
 }

 .current-legend .legend-property-select {
   margin-bottom: 15px;
   margin-bottom: 15px;
   transform: scale(0.74);
   transform-origin: right center;
 }

 .current-legend .scale-sequential .legend-property-select {
   z-index: 1080;
 }

 .current-legend .scale-color-text {
   color: #dddddd;
   font-size: 12px;
   display: flex;
   justify-content: space-between;
   float: right;
   padding: 0 10px;
 }

 .current-legend .legend-property-select .select__control {
   border-radius: 32px;
   padding: 0 10px;
 }

 .current-legend .legend-checkbox {
   color: #7D7D7D;
   font-size: 14px;
   justify-content: flex-end;

 }

 .current-legend .legend-scale-color {
   margin-top: 10px;
 }

 .current-legend .legend-scale-color canvas {
   background-color: red;
   border-radius: 32px;
 }

 .current-legend .scale-sequential .kv-range {
   z-index: 1080;
 }

 .current-legend .legend-scale-color .scale-color-range {
   text-align: right;
 }

 .current-legend .button-group-container span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60ch;
  float: left;
  line-height: 24px;
 }

 .current-legend .button-group-container .legend-icon {
   cursor: pointer;
   text-align: center;
 }

 .current-legend .button-group-container .legend-icon i {
  position: absolute;
  top: 50%;
  color: white;
  font-size: 14px;
  left: 50%;
  transform: translate(-50%, -50%);
}

.current-legend .button-group-container .legend-text{
  float: left;
  align-self: center;
  border-radius: 50px;
  margin: 5px 10px;
  padding: 0px 15px;
  cursor: pointer;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

.legend-text.clicker {
 background: rgba(0,0,0,.7);
 border-radius: 50px;
}

.legend-caption {

}

.legend-caption-undefined {
  font-style: italic;
}

 .current-legend>.button-group>button {
   display: flex;
   font-family: Raleway-SemiBold;
   font-size: 13px;
   color: rgba(108, 88, 179, 0.75);
   letter-spacing: 1px;
   line-height: 15px;
   border: 1px solid rgba(108, 89, 179, 0.75);
   border-radius: 40px;
   margin: 5px;
   min-width: 120px;
   background: transparent;
   transition: all 0.3s ease 0s;
 }

 .current-legend>.button-group>button:hover {
   color: #FFF;
   background: rgba(189, 33, 27, 0.6);
   border: 2px solid rgba(189, 33, 27, 0.6);
 }

 .current-legend .legend-line {
   height: 22px;
   align-self: center;
   cursor: pointer;
 }

 .current-legend .line {
   color: #f00;
   background: #f00;
   width: 18px;
   height: 5px;
   margin-top: 7px;
 }

 /*--end lengend--*/

 #graph-info {
   display: none;
   position: absolute;
   top: 0px;
   left: 40%;
   margin: 10px;
   background-color: #ffffe0;
   color: #333;
   padding: 5px 10px;
 }

 .graph-2d {
   position: relative;
 }

 .graph-2d .header {
   position: absolute;
   margin: 0px 88px 0 0;
   padding: 4px 0 0 10px;
 }

 .graph-2d .header span {
   font-size: 12px;
   display: block;
   color: white;
   margin: 6px 6px 0px 0px;
   float: left;
 }

 .graph-2d .count {
   position: absolute;
   right: 6px;
   margin: 6px;
   color: white;
 }

 #graph-div {
   background: transparent;
   position: absolute;
 }

 #options {
   position: absolute;
   top: 0;
   right: 0;
   z-index: 10;
   background: #ffffff;
   padding: 25px;
   display: none;
 }



 .form-control {
   min-width: 500px;
   background-color: transparent;
   border-color: #303030;
 }

 .form-inline .form-control {
   min-width: fit-content;
   width: 100%;
 }

 .form-control:focus {
   background-color: transparent;
   border-color: gray;
 }

 .form-control:focus {
   border-color: gray;
   -webkit-box-shadow: none;
   box-shadow: none;
 }

 #popover.popover {
   /* color: green; */
   border: 1px solid gray;
   background-color: #000000;
 }

 /* 
#popover.popover>.arrow::after {
    border-bottom-color: green;
} */

 #popover.popover>.popover-content {
   padding: 0px;
   background-color: transparent;
 }

 #popover.popover>.popover-title {
   background-color: transparent;
   border-bottom: 1px solid gray;
 }

 .popover.bottom>.arrow:after {
   border-bottom-color: gray !important;
 }

 .popover.right>.arrow:after {
   border-right-color: gray !important;
 }

 /* #node-info-panel {
     position: fixed;
     width: 100px;
     height: 100px;
     z-index: 1;
     background: red;
     left: -10000px;
     top: -10000px;
 } */

 .list-group {
   margin-bottom: 0px;
   border: 0px;
 }

 .list-group>.a-group>a.list-group-item {
   color: gray;
   background-color: transparent;
   padding: 5px;
   border: 0px;
 }

 .divider {
   height: 1px;
   width: 100%;
   display: block;
   overflow: hidden;
   background-color: green;
 }

 /*----start project ---- */

 .panel-content {
   bottom: 0px;
   height: 100%;
   overflow-y: auto;
 }

 .panel-content .list {
   height: 100%;
   overflow-y: auto;
 }

 .panel-content .list .title {
   cursor: pointer;
 }

 .panel-content.show-config .list {
   max-height: 160px;
   height: auto;
   overflow-y: auto;
 }

 .panel-content.show-config .config {
   /* height: 100%;
   padding-bottom: 150px; */
 }

 .panel-content.show-config .config .header {
   display: flex;
   flex-direction: row;
   text-align: center;
   margin: 5px;
   align-items: center;
   color: #ACACAC;
   min-height: 70px;
   z-index: 1090;
 }

 .panel-content.show-config .config .property-group {
   height: 100%;
   padding-bottom: 40px;
   z-index: 1080;
 }

 .panel-content.show-config .config .property-group tbody {
   max-height: 200px;
   overflow-y: scroll;
 }

 .panel-content.show-config .config .visible-trigger {
   text-align: center;
   margin: auto 20px;
 }

 .panel-content.show-config .config .visible-trigger input {
   margin-right: 5px;
   margin-left: 10px;
 }

 .panel-content.show-config .config .label-name {
   margin-top: auto;
   margin-bottom: auto;
   color: #DBDBDB;
   font-size: 18px;
 }

 .panel-content.show-config .config .label-icon {
   display: flex;
   justify-content: center;
   align-items: center;
   width: 32px;
   height: 32px;
   cursor: pointer;
   border-radius: 50%;
   cursor: pointer;
   margin-left: 5px;
   border: none;
 }

 .panel-content.show-config .config .label-icon  i{
  font-size: 24px;
  color: white;
}

 .panel-content.show-config .config .relationship-icon {
   width: 32px;
   height: 32px;
   cursor: pointer;
   background: rebeccapurple;
   cursor: pointer;
   border: none;
   border-radius: 2px;
 }


 .panel-content.show-config .config table {
   width: 100%;
   height: 100%;
 }

 .panel-content.show-config .config .header-fixed>tbody {
   /*height: 100%;*/
 }

 @-moz-document url-prefix() {
   .panel-content.show-config .config .header-fixed>tbody {
     max-height: 360px;
   }
 }
 .panel-content.show-config .config .property-group thead tr {
  color: #DBDBDB;
 }
 .panel-content.show-config .config .property-group tr {
   color: #ACACAC;
   height: 40px;
   display: flex;
   align-items: center;
 }

 .panel-content.show-config .config .property-group td {
   padding: 5px;
 }



 .property-panel {
   overflow-y: auto;
   display: block;
   height: 100%;
 }

 .property-panel .label-button-group {
   border-color: gray;
 }

 .property-panel .props-group .title {
   font-size: 16px;
   margin-top: 10px;
   padding: 0 6px;
 }

 .property-panel .label-button-group.exist {
   border-color: #DDD;
   cursor: pointer;
 }

 .property-panel .label-button-group.exist.active {
   border-color: #00B7FF;
   color: #00B7FF;
   cursor: pointer;
 }

 .panel-content.kv-table-group {
   height: 100%;
 }

 .panel-content.kv-table-group .tab {
   max-height: 150px;
   min-width: 80px;
   overflow-y: auto;
   z-index: 1090;
   margin-bottom: 15px;
 }

 .panel-content.kv-table-group .search {
   height: 26px;
   margin: 10px 6px;
   width: 100%;
   text-align: right;
   z-index: 1090;
 }

 .panel-content.kv-table-group .content {
   z-index: 1080;
   height: 100%;
   max-height: 300px;
 }

 .panel-content.kv-table-group .content .detail-table {
   max-height: 100%;
 }

 .panel-content.kv-table-group .content .button-group {
   height: 40px;
 }

 .icon-popover-container {
   display: grid;
   grid-template-columns: auto auto auto auto;
   grid-column-gap: 5px;
   grid-row-gap: 5px;
   margin: 5px;
 }

 .custom-HDLayout .axis-item {
   margin: 3px 6px;
 }

 .remark-window {
   margin: 0;
   padding: 0;
 }

 .remark-window table {
   border: none;
   /* width: 100%; */
 }

 .remark-window table thead td {
   color: white !important;
   padding: 5px;
   font-size: 14px;
 }

 .remark-window table tr {
   margin: 0;
 }

 .remark-window table td {
   color: white;
   font-size: 14px;
   padding: 0;
   margin: 0;
   border: 1px solid gray;
 }

 .remark-window table td input {
   background-color: rgb(44, 44, 44, 0.5);
   width: 100%;
   padding: 5px;
   border: none;
   color: #DDD;
   border-radius: 0;
   min-width: 80px;
 }

 .remark-window table td input:focus {
   color: #DDD;
 }

 .remark-window .button-group {
   padding: 10px;
   text-align: right;
 }

 .remark-window table tr td:nth-child(2) {
   border-right: none;
 }

 .remark-window table tr td:nth-child(1) {
   border-left: none;
   border-right: none;
 }

 #left-slider-panel.timeline .slider-title {
   z-index: 1090;
 }

 #left-slider-panel.timeline .content {
   z-index: 1080;
 }

 .time-line-window {
   padding-top: 64px;
   height: 100%;
   width: 100%;
   text-align: center;
 }

 .time-line-window .time-line-list {
   margin-top: -64px;
   height: 100%;
   width: 100%;
   padding-left: 15px;
   padding-right: 15px;
   pointer-events: visible;
   overflow-y: auto;
 }

 .time-line-window .time-item {
   width: 100%;
   margin: 5px 0;
   padding-bottom: 6px;
 }

 .time-line-window .time-item:first-child {
   margin-top: 20px;
 }

 .time-line-window .time-item:last-child {
   margin-bottom: 10px;
 }

 .time-line-window .time-line {
   margin: 5px;
   height: 16px;
   z-index: 9999;
   display: flex;
   flex-direction: row;
   justify-content: flex-end;
 }

 .time-line-window .time-line span.left-title {
   text-align: left;
 }

 .time-line-window .time-line span.right-title {
   text-align: right;
 }

 .time-line-window .time-line span.col-md-3 {
   padding: 0;
   /* background-color: red; */
   /* border: 1px solid red; */
 }

 .time-line-label {
   text-align: center;
   margin: 5px;
 }

 .time-line-label span {
   color: #dddddd;
   font-size: 14px;
 }

 .time-line-label input {
   margin-left: 5px;
   margin-right: 5px;
 }

 .time-line span {
   color: white;
 }

 .timeline-range {
   width: 60%;
 }

 .fulltext-search {
   /* display: flex;
     flex-direction: column;
     margin-top: 20px;
     margin-left: 20px; */
   pointer-events: visible;
   position: absolute;
   left: 50px;
   top: 20px;
 }

 .fulltext-search.show-panel-info {
   left: 136px;
 }

 .fulltext-search.panel-active {
   /* background-color: rgb(44, 44, 44, 0.9); */
   border-radius: 12px;
   padding-right: 12px;
 }

 .fulltext-search .no-result {
   /* height: 60px; */
   color: gainsboro;
   font-size: .8rem;
   width: 420px;
   background-color: rgba(44, 44, 44, 0.5);
   border-radius: 10px;
   margin-top: -18px;
   padding: 10px 8px;
 }

 .fulltext-search .no-result span.title {
   display: block;
   margin-bottom: 0.5rem;
 }

 .fulltext-search .no-result span.title strong {
   color: #dc3545;
 }

 .fulltext-search input {
   width: 90%;
   color: white;
   border: none;
   border-radius: 2px;
   padding-right: 40px;
   background-color: transparent;
   padding-bottom: 6px;
   /* background: rgb(44, 44, 44, 0.5); */
 }

 .fulltext-search .search-box-group {
   display: flex;
   align-self: flex-start;
 }

 .fulltext-search .search-box-group .search-box .kinput {
    display: flex;
    align-items: center;
    justify-content: space-between;
 }

 .fulltext-search .search-box-group>.button-item:nth-child(n+1) {
   margin: auto 5px;
 }

 .fulltext-search .search-box-group>.button-item.config-index i {
   font-size: 18px;
   line-height: 26px;
 }

 .fulltext-search .search-tips-list {
   color: #DDD;
 }

 .fulltext-search .search-tips-item:first-child {
   padding-top: 10px;
 }

 .fulltext-search .search-tips-item:last-child {
   padding-top: 10px;
 }

 .fulltext-search .switch-button {
   color: #888;
   cursor: pointer;
 }

 .fulltext-search .switch-button:hover {
   color: #DDD;
 }


 .fulltext-search .search-box-group span,
 .fulltext-search .ul span,
 .fulltext-search .no-result span
  {
   cursor: pointer;
   font-size: 14px;
 }

 .fulltext-search .switch-button+.tooltiptext {
   display: none;
   float: left;
   top: 0;
   right: 0;
   color: #DDD;
   margin-left: 24px;
   margin-right: -200px;
   padding-left: 4px;
   padding-right: 4px;
   background-color: rgba(41, 39, 39, 0.5);
   border-radius: 2px;
 }

 .fulltext-search .switch-button:hover+.tooltiptext {
   display: block;
 }

 .fulltext-search-input {
   display: flex;
   flex-direction: row
 }

 .fulltext-search ul {
   color: #333333;
   padding: 10px;
 }

 .fulltext-search ul li {
   color: gainsboro;
   list-style: none;
   position: relative;
 }

 .fulltext-search ul li.main-label {
   margin-top: 10px;
   margin-bottom: 10px;
 }

 .fulltext-search ul li.main-label a {
   color: gainsboro;
 }

 .fulltext-search ul li.main-label a.label-title {
   cursor: pointer;
   text-decoration: none;
 }

 .fulltext-search ul li.main-label span.title-sign {
   color: gainsboro;
   cursor: pointer;
   margin-left: 10px;
 }

 /* .fulltext-search ul li.main-label ul.label-list {} */

 .fulltext-search ul li.main-label ul.label-list .label-body {
   cursor: pointer;
   margin-top: 4px;
   margin-bottom: 14px;
   max-height: 62px;
   display: block;
   width: 320px;
   padding-left: 16px;
   word-break: break-all;
 }

 .fulltext-search ul li.main-label ul.label-list .label-body:hover {
   border: 1px solid gray;
   border-radius: 32px;
 }

 .fulltext-search ul li.main-label ul.label-list .label-body .label-sign {
   margin-left: -17px;
   margin-right: 4px;
 }

 .fulltext-search ul li.main-label ul.label-list .label-body:hover {
   text-decoration: none;
 }

 .fulltext-search ul li .search-node-tips {
   position: absolute;
   z-index: 1090;
   top: 30px;
   left: 120px;
 }

 /* .fulltext-search .paging {} */

 .fulltext-search .paging .page-index,
 .fulltext-search .paging .next-page,
 .fulltext-search .paging .last-page {
   cursor: pointer;
   float: right;
   margin-right: 8px;
   margin-bottom: 4px;
 }

 .fulltext-search .paging .page-index {
   cursor: Default
 }

 .fulltext-search #apoc-index-panel-bg{
    position: fixed;
    height: 100%;
    width: 100%;
    background-color:var(--main-bg-color);
    top: 0;
    opacity: 0.8;
    left: 0;
    z-index: 1390;
 }
 .fulltext-search #apoc-index-panel{
    position: fixed;
    left: 10%;
    top: 10%;
    max-height: 70%;
    overflow-y: auto;
    padding: 10px 20px 20px 20px;
    font-size: 16px;
    z-index: 1390;
    background-color: var(--main-bg-color) !important;
    color: var(--main-text-color) !important;
    margin-top: 50px;
    border: 1px solid var(--main-border-color);
    border-top: none;
    border-radius: 0 0 4px 4px;
    width: 80%;
  
 }

 .fulltext-search #apoc-index-panel > .index-header-title{
  left: 10%;
  top: 10%;
  display: block;
  position: fixed;
  width: 80%;
  height: 50px;
  padding: 10px 20px 0 20px;
  background-color: var(--main-bg-color) !important;
  color: var(--main-text-color) !important;
  border: 1px solid var(--main-border-color);
  border-radius: 4px 4px 0 0;
 }

 .search-heighlight {
   font-weight: bold;
   font-style: normal;
   color: red;
   background: transparent;
   padding-right: 1px;
 }

 .hidden {
   display: none;
 }


 .force-layout {
   padding: 10px;
 }

 .force-layout .check-group {
   color: #ACACAC;
   height: 40px;
 }

 .force-layout .check-group .checkbox-title {
   line-height: 20px;
   margin-right: 10px;
 }


 .force-layout .check-group input {
   margin-left: 10px;
   vertical-align: middle;
 }

 .force-layout .button-group {
   display: flex;
   justify-content: space-between;
 }

 .force-layout button {
   color: #ACACAC;
   border-radius: 50px;
   height: 32px;
   padding: 0 40px;
 }

 .force-layout .default-button {
   margin-top: 10px;
 }

 .force-layout .button-group button {
   width: 100%;
   margin: 10px;
 }

 .force-layout .sliders {
   margin: 0 0 20px 0;
 }

 .force-layout .sliders label {
   color: #ACACAC;
   margin-top: 10px;
   margin-bottom: 0;
   width: 100%;
 }

 .force-layout .sliders label.both .right {
   float: right;
 }

 .rc-slider-handle{
   background-color: #8DCFF8 !important;
   border: none !important;
   width: 10px !important;
   height: 10px !important;
   margin-top: -4px !important;
 }
 
 .rc-slider-track{
  background-color: #8DCFF8 !important;
  height: 2px !important;
 }

 .rc-slider-rail {
   background-color: #7D7D7D !important;
   height: 2px !important;
 }

 .tree-layout {
   padding: 10px;
 }

 .tree-layout .button-group {
   display: flex;
   justify-content: space-between;
 }

 .tree-layout .button-group button {
   margin: 10px;
   color: #ACACAC;
   border-radius: 50px;
   padding: 0 40px;
 }

 .parametric-layout {
   display: flex;
   flex-direction: column;
 }

 .parametric-layout .advanced-range {
   padding: 0 10px;
   line-height: 20px;
   display: flex;
   justify-content: flex-end;
 }

 .parametric-layout .advanced-range .check-box {
   font-size: 20px;
   cursor: pointer;
   margin-left: 10px;
 }

 .parametric-layout .item {
   display: flex;
   flex-direction: column;
 }

 .parametric-layout .item .item-axis {
   display: flex;
   flex-direction: row;
   justify-content: center;
   margin: 10px;
 }

 .parametric-layout .item .max-min {
   display: flex;
   justify-content: space-between;
   padding: 0 20px 0 30px;
   font-style: italic;
 }

 .parametric-layout .item .input-range {
   margin: 0 20px 0 38px;
 }

 .parametric-layout .item span.title {
   color: white;
   margin-right: 10px;
   align-self: center;
 }

 .parametric-layout .item select {
   width: 100%;
   /* -webkit-appearance: none;
     -webkit-border-radius: 0px; */
 }

 .parametric-layout .button-group {
   display: flex;
   justify-content: space-between;
   margin: 20px 0;
 }

 .parametric-layout .button-group button {
  padding: 0 40px;
   margin: 10px;
   color: #ACACAC;
   border-radius: 50px;
 }

 .parametric-layout .kv-check-box {
   margin: 0 10px;
 }

 .parametric-layout .kv-check-box .checkbox-title {
   margin-right: 10px;
 }

 .parametric-history {
   margin: 10px;
   max-height: 250px;
   min-height: 150px;
   height: 150px;
   overflow-y: auto;
 }

 .parametric-history-hidden {
   display: none;
 }

 .parametric-history table {
   width: 100%;
   background-color: #303030;
 }

 .parametric-history .table-bordered td, .table-bordered th {
   border: 1px solid #1D1D1D;
 }

 .parametric-history td {
   padding: 5px;
   word-break: break-all;
   min-width: 130px;
 }

 .parametric-history td.actions {
   min-width: 95px;
 }

 .parametric-history td .prop .prop-name,
 .parametric-history td .prop .prop-range {
   display: flex;
   color: #aaaaaa;
 }

 .parametric-history td .parametric-history thead td {
   border-width: 1px;
   border-color: gray;
   text-align: center;
 }

 .distribution-layout {
   display: flex;
   flex-direction: column;
   margin-left: 10px;
 }

 .distribution-layout .button-group {
   display: flex;
   margin-top: 10px;
 }

 .distribution-layout .layout {
   display: flex;
   flex-direction: column;
 }

 .distribution-layout .rotate {
   margin-top: 10px;
   display: flex;
   flex-direction: column;
 }

 .distribution-layout .rotate input {
   margin-left: 10px;
   background: transparent;
   border-width: 0 0 1px;
   border-color: gray;
   color: gray;
 }

 .distribution-layout .scale {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
}

.distribution-layout .scale input {
  margin-left: 10px;
  background: transparent;
  border-width: 0 0 1px;
  border-color: gray;
  color: gray;
}

.distribution-layout .shift {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
}

.distribution-layout .shift input {
  margin-left: 10px;
  background: transparent;
  border-width: 0 0 1px;
  border-color: gray;
  color: gray;
}

.distribution-layout .align {
   margin-top: 10px;
   display: flex;
   flex-direction: column;
 }

 .distribution-layout .distribution {
   margin-top: 10px;
   display: flex;
   flex-direction: column;
 }


 .distribution-layout .order {
   margin-top: 10px;
   display: flex;
   flex-direction: column;
 }

 .distribution-layout .order .option {
   display: flex;
   flex-direction: column;
   margin-top: 10px;
   background-color: rgba(48, 48, 48, .85);
 }

 .distribution-layout .order .option .label {
   display: flex;
   flex-direction: row;
 }

 .distribution-layout .order .option .label>span {
   min-width: 80px;
 }

 .distribution-layout .order .option .property {
   display: flex;
   flex-direction: row;
   margin-top: 10px;
 }

 .distribution-layout .order .option .property>span {
   min-width: 80px;
 }

 .distribution-layout .order .option .order-group {
   display: flex;
   flex-direction: row;
   margin-top: 10px;
 }

 .distribution-layout .order .option .order-group .descend {
   margin-left: 10px;
 }


 .distribution-layout .order .select {
   width: 100%;
   margin-left: 10px;
   color: black
 }

 .distribution-layout hr {
   border: 0;
   clear: both;
   display: block;
   width: 100%;
   background-color: rgba(255, 255, 255, .3);
   height: 1px;
 }

 .popover-body .styling-settings {
   min-width: 320px;
 }

 .popover-body .styling-settings .settings-header h4 {
   font-size: 12px;
   color: var(--main-text-color) !important;
 }
 .popover-body .styling-settings .settings-header button {
   display: flex;
 }
 .popover-body .styling-settings .settings-header button span {
   font-size: 32px;
   line-height: 16px;
   color: #ACACAC;
   font-weight: 200;
 }

 .popover-body .styling-settings .property-row{
  color: var(--main-text-color);
  padding: 5px;
  border: 1px solid #5A5A5A; 
  margin: 0 0 -1px -1px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
 }

 .popover-body .styling-settings .property-row .avatar{
  background: var(--main-text-active);
  width: 18px;
  height: 18px;
  text-align: center;
 }


 .popover-body .styling-settings .property-row .caption{
  background: var(--main-bg-color);
  margin-left: 5px;
  filter: invert(1);
  width: 18px;
  height: 18px;
  text-align: center;
 }

 .popover-body .styling-settings .property-row .icon-group{
   display: flex;
 }

 .ToggleSwitch {
  background: #1D1D1D;
  visibility: hidden;
  box-shadow: inset 0px 4px 4px rgba(0, 0, 0, 0.25);
  height: 24px;
  width: 50px;
  border-radius: 1em;
}

.ToggleSwitch .knob {
  position: relative;
  width: 20px;
  height: 20px;
  background: #65B7F3;
  border-radius: 50%;
  left: 0em;
  transition: left 0.3s ease-out;
  margin: 1.5px;
}

.ToggleSwitch .knob.active {
  left: 26px;
}

 .label-color-picker {
    display: grid;
    /*grid-template-columns: auto auto auto auto auto auto auto auto;
    grid-column-gap: 7px;*/
    grid-row-gap: 10px;
    min-width: 290px;
    margin: 5px;
    background-color: rgba(29,29,29, .7);
    grid-template-columns: 14.7% 14.7% 14.7% 14.7% 14.7% 14.7% 14.7%;
    border-radius: 5px;
 }

 .label-color-picker .circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
 }

 .label-color-picker .circle:hover {
   cursor: pointer;
 }

 .icon-picker{
  display: flex;
  width: 100%;
  position: relative;
  flex-direction: column;
  align-items: center;
  margin-bottom: 7px;
  max-height: 300px;
 }

 .icon-picker .form-group{
    background-color: #1D1D1D;
    width: 100%;
   margin-bottom: 0px;
   border-radius: 5px;
 }

 .icon-picker .dropdown{
  position: absolute;
  right: 5px;
  width: 80px;
  height: auto;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(133,134,156,.3);
  border: none;
  border-radius: 5px;
 }
 .icon-picker .dropdown select{
   width: 80px;
   display: inline-block;
   padding: 4px 3px 3px 5px;
   margin: 0;
   font: inherit;
   outline: none;
   /* remove focus ring from Webkit */
   line-height: 1.2;
   background: rgb(48,48,48);
   color: #ACACAC;
   border: 0;
   border-radius: 5px;
 }

 .icon-picker .dropdown select::after{
    content: "▼";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    font-size: 60%;
    line-height: 30px;
    padding: 0 7px;
    background: #000;
    color: white;
    pointer-events: none;
 }

 .icon-picker .icon-group{
  background-color: rgba(29, 29, 29, .7);
  overflow: hidden;
  overflow-y: scroll;
  width: 100%;
  border-radius: 5px;
 }

 .icon-picker .form-control {
  padding-left: 2.375rem;
  min-width: 100%;
  padding-right: 80px;
  margin-bottom:0px;
  color: #ACACAC
}

.icon-picker .form-control-search {
  position: absolute;
  z-index: 2;
  display: block;
  width: 2.375rem;
  height: 2.375rem;
  line-height: 2.375rem;
  text-align: center;
  pointer-events: none;
  color: #aaa;
} 

.icon-picker .row .col{
  width: 50px;
  padding: 0% 3.5%;
}

.icon-picker .row span{
  color: gray;
  font-size: 24px;
}

 .help-panel {
   margin: 10px;
 }

 .help-panel li {
   color: gray;
 }

 .axis-slider-panel {
   padding: 10px;
   display: flex;
 }

 .axis-slider-panel label {
   margin-right: 10px;
   color: #ACACAC;
 }


 .setting-panel .checkboxes div div {
  height: 32px;
 }

 .setting-panel .theme > span {
  cursor: pointer;
}

 /* .setting-panel .edge-setting {} */

 /* .setting-panel .icon-select {} */

 .setting-panel .icon-select .form-control {
   min-width: 200px;
   color: #DDD;
   height: 32px;
 }

 .setting-panel label {
    color: #ACACAC;
 }

 /*import export*/

 .import-export-panel .button-group {
   display: flex;
   justify-content: space-between;
 }

 .import-export-panel .button-group button {
   width: 100%;
   border-radius: 50px;
 }

 .import-export-panel .import-input[type=file] {
   display: none;
 }

 .import-export-panel .perspectives {
   /* height: 100%; */
   color: #ACACAC;
 }

 .import-export-panel .perspectives .content {
   padding-top: 10px;
   height: 100%;
   border-radius: 4px;
 }

 .import-export-panel .perspectives .kinput {
    background-color: #1D1D1D;
    border: none;
    border-radius: 5px;
    padding: 2px 10px;
    height: 32px;
    margin: 10px;
    color: #ACACAC;
}

.import-export-panel .perspectives  .header  .kinput{
  width: 68%;
}
 .import-export-panel .perspectives .header {
   display: flex;
 }



 .import-export-panel .perspectives .header .kv-button {
   width: 32%;
   /*border-color: #7D7D7D;*/
   /*color: #ACACAC;*/
 }

 .import-export-panel .perspectives .perspective-list {
   display: flex;
   flex-direction: column;
   height: 100%;
   max-height: 300px;
   overflow-y: scroll;
 }

 .import-export-panel .perspective-item.active {
    background-color: #8dcff8;
    transition: all .2s all;
 }

 .import-export-panel .perspective-item.active td,
 .import-export-panel .perspective-item.active a,
 .import-export-panel .perspective-item.active i {
    color:  #000000; 
 }

 .import-export-panel .perspective-item.active .btn {
    border: 1px solid #000;
 }

 .import-export-panel .perspective-item a {
   color: #DDDDDD;

 }

 .import-export-panel .perspective-item .item-title {
   width: 35%;
 }

 .import-export-panel .perspective-item .item-update-time {
   width: 20%;
   text-align: center;
   padding-left: 3px;
   padding-right: 3px;
 }

 .import-export-panel .perspective-item .item-update,
 .import-export-panel .perspective-item .item-load,
 .import-export-panel .perspective-item .item-remove {
   width: 15%;
   text-align: center;
 }

 .import-export-panel .perspective-item.in-neo4j-desktop.no-current-project-view,
 .import-export-panel .perspective-item.in-neo4j-desktop.no-current-project-view .item-update .btn,
 .import-export-panel .perspective-item.in-neo4j-desktop.no-current-project-view .item-load .btn {
   color: #888;
 }

 .import-export-panel .perspective-item .item-remove.no-own,
 .import-export-panel .perspective-item .item-remove.no-own .btn {
   color: #888;
   border-color: #888;
   cursor: default;
 }

 .import-export-panel .perspective-item .btn {
   padding: 2px 6px;
   color: #DDD;
   border-color: #DDD;
 }

 /*Map*/

 #map {
   position: absolute;
   top: 0;
   bottom: 0;
   width: 100%;
   z-index: -9;
 }

 .map-search {
   padding: 10px;
 }

 .map-search .search-box {
   margin-right: 0;
 }


 .map-search .results {
   position: absolute;
   width: 100%;
   margin-top: 3px;
   padding: 10px 20px 0 0;
 }

 .map-search .sugesstion {
   border-radius: 4px;
   list-style: none;
   margin: 0;
   padding: 0;
   width: 100%;
   color: #ACACAC;
   z-index: 1000;
   overflow: hidden;
   font-size: 14px;
   background-color: rgba(44, 44, 44, 0.5);
 }

 .map-search .sugesstion li {
   height: 38px;
   line-height: 38px;
   overflow: hidden;
   transition: all .2s ease;
 }

 .map-search .sugesstion a {
   text-decoration: none;
   cursor: pointer;
   padding: 10px;
   border-radius: 4px;
 }

 .map-search .sugesstion a strong {
   font-weight: 800;
 }

 .map-search .sugesstion li:hover {
   color: #DBDBDB;
   background-color: rgba(48, 48, 48, .85);
   cursor: pointer;
 }

 #map-attribution {
   position: fixed;
   bottom: 5px;
   left: 42px;
   border: none;
   z-index: 1099;
   height: 20px;
   opacity: 0.6;
 }

 #map-attribution .mapbox-attribution-container {
   float: left;
   padding-left: 10px;
   font-size: 10px;
   line-height: 20px;
 }

 #map-attribution .mapbox-wordmark {
   float: left;
   height: 20px;
   width: 65px;
   bottom: 10px;
   text-indent: -9999px;
   overflow: hidden;
   background-image: url(data:image/svg+xml;base64,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);
   background-repeat: no-repeat;
   background-position: 0 0;
   background-size: 65px 20px;
 }

 #map.debug {
   /* transform: scale(0.1);
    transform-origin: right bottom; */
   display: block;
   z-index: 99;
   background-color: red;
   border: solid 1px red;
   /* right: 10px; */
 }

 #map.debug+#react-dom {
   z-index: 999;
 }

 .map.left-slider-panel {
   height: auto;
   background-color: transparent;
   border: none;
 }

 .map.left-slider-panel .fa-window-minimize,
 .map.left-slider-panel .fa-window-maximize {
   display: none;
 }

 .map .button-group {
   display: flex;
   padding: 0 6px;
 }

 .map .button-group button {
   width: 100%;
 }

 .node-edge-props-filters {
   padding: 0 10px;
   height: 100%;
   padding-bottom: 20px;
 }
 .node-edge-props-filters .filters .props-select-item .props-multi-select,
 .node-edge-props-filters .filters .props-select-item .props-multi-select .select__control {
  height: 100%;
  min-height: 100%;
  padding: 0;
 }


 .filters {
   color: #ACACAC;
   height: 100%;
   overflow-y: auto;
   padding-right: 20px;
 }

 .filters .props-select-item,
 .filters .props-select-item .props-multi-select {
   z-index: 1390;
 }

 .filters .props-select-item:first-child,
 .filters .props-select-item:first-child .props-multi-select {
   z-index: 1490;
 }

 .filters .header {
   z-index: 1090;
   width: 100%;
   margin-top: 10px;
 }

 .filters .header .props-select-item {
   margin-bottom: 10px;
 }

 .filters .header .title {
   font-size: 14px;
 }

 .filters .main {
   z-index: 1080;
   width: 100%;
   margin-top: -10px;
 }

 .filters .header .filters-list{
  height: 76px;
  overflow-y: hidden;
 }

 .filters .header .filters-list .filter-item{
   background-color: unset;
 }

 .filters .main .filters-list {
  width: 100%;
  overflow-y: auto;
}

.filters .input-range .input-group.short-group {
  width: auto;
}

 .filters .filter-item {
  background-color: rgba(48, 48, 48, .85);
  width: 100%;
  z-index: 1080;
 }


 .filters .main .filter-item{
    padding: 0px 20px;
 }

 .filters  .filter-item.active {
   z-index: 1090;
   
 }

 .filters .filter-item:first-child {
   margin-top: 10px;
 }

 .filters .filter-item-node:first-child {
   margin-top: 100px;
 }

 .filters .filter-item.bottom-split {
   margin-bottom: 10px;
 }

 .filters .filter-item.actions {
   margin-bottom: 10px;
   display: flex;
   justify-content: space-between;
 }

 .filters .filter-item .title,
 .filters .filter-item .body {
   width: 100%;
 }

 .filters .filter-item .title {
   padding: 0;
   margin: 0 -5px ;
   cursor: pointer;
 }
 

 .filters .filter-item .body .kv-range {
   z-index: 1080;
   width: 100%;
   padding-top: 0;
 }

 .filters .filter-item .body .kv-button .fa {
   margin-top: 3px;
   margin-right: 3px;
 }

 .filters .filter-item .action-delete:focus,
 .filters .filter-item .action-delete:hover{
   color: red !important;
 }


 .filters .filter-item-split {
   margin: 10px 5px;
   font-size: 14px;
 }

 .filters-list .check-group .checkbox-title {
   line-height: 20px;
   margin-right: 10px;
 }

 /**
 Navgation Tool
 */

 .nav-tool {
   border-radius: 10px;
   bottom: 20px;
   right: 20px;
   z-index: 1290;
   position: fixed;
   color: #bbb;
   text-align: center;
   margin-right: -10px;
 }

 .nav-tool.not-pc span {
   -webkit-touch-callout: none !important;
   -webkit-user-select: none !important;
   -khtml-user-select: none !important;
   -moz-user-select: none !important;
   -ms-user-select: none !important;
   -moz-user-select: none !important;
   user-select: none !important;
 }

 .nav-tool .clearfix .title-text {
   position: absolute;
   left: -95px;
   width: 95px;
   height: 20px;
   text-align: right;
   visibility: hidden;
   display: none;
   color: #FFF
 }

 .nav-tool .clearfix:hover .title-text {
   visibility: visible;
   display: block;
 }

 .nav-tool .clearfix .title-text span {
   background: rgb(41, 39, 39, 0.5);
   padding: 3px 5px;
   border-radius: 2px;
 }

 .nav-tool .clearfix .title-text.title-move {
   top: 29px;
 }

 .nav-tool .clearfix .title-text.title-rotate {
   top: 100px;
 }

 .nav-tool .clearfix .title-text.title-translate {
   top: 146px;
 }

 .nav-tool .item {
   cursor: pointer;
   margin-top: 8px;
 }

 .nav-tool .item span:before {
   font-size: 26px;
 }

 .nav-tool .nav-btns {
   margin-bottom: 0px;
 }

 .nav-tool .nav-btns .item {
   margin-top: 0px;
 }

 .nav-tool .nav-btns .item span:before {
   font-size: 20px;
 }

 .nav-tool .layer {
   width: 100%;
   float: left;
 }

 .nav-tool .layer.layer-center {
   float: left;
   margin: 0;
   padding: 0;
 }

 .nav-tool .layer.layer-center .item {
   width: 30%;
   float: left;
   line-height: 18px;
 }

 .nav-tool .layer.layer-center .item.item-center span:before {
   font-size: 15px;
 }

 .nav-tool .layer.layer-center .item:first-child {
   text-align: right;
   margin-left: 5%;
 }

 .nav-tool .layer.layer-center .item:last-child {
   text-align: left;
 }

 .nav-tool .two-btns .item {
   float: left;
   width: 50%;
 }

 .nav-tool .two-btns .item:first-child {
   text-align: right;
   padding-right: 3px;
 }

 .nav-tool .two-btns .item:last-child {
   text-align: left;
   padding-left: 10px;
 }

 /*
 selected-nodes-tools
 */

 #selected-nodes-tools {
   position: fixed;
   bottom: 20px;
   right: 140px;
   z-index: 1000;
   color: #bbb;
   border-radius: 2px;
 }

 #selected-nodes-tools.menu-open{
  bottom: 20px;
}

 #selected-nodes-tools .item {
   float: right;
   padding: 0 10px;
   cursor: pointer;
   /* border: 1px solid red; */
 }

 #selected-nodes-tools .item .node-num {
   display: none;
 }

 #selected-nodes-tools .item .node-num.node-inverse,
 #selected-nodes-tools .item .node-num.node-un-pin,
 #selected-nodes-tools .item .node-num.node-pin {
   height: 14px;
   min-width: 14px;
   border-radius: 7px;
   float: right;
   background-color: #00B7FF;
   display: inherit;
   text-align: center;
   line-height: 10px;
   padding: 2px 4px;
   font-size: 10px;
   color: #000;
 }

 #selected-nodes-tools .item .pin-table {
   height: 100px;
   width: 110px;
   border-radius: 10px;
   position: absolute;
   top: 0;
   left: 0;
   border: 1px solid #ddd;
   background-color: rgba(0, 0, 0, .4);
   padding: 16px;
   display: flex;
   flex-direction: row;
   flex-wrap: wrap;
   justify-content: space-around;
   cursor: default;
 }

 #selected-nodes-tools .item .pin-table-arrow {
   height: 10px;
   width: 10px;
   position: absolute;
   bottom: -4.5px;
   left: 42px;
   transform: rotate(45deg);
   background-color: #000;
   border: solid #ddd 1px;
   border-top: none;
   border-left: none;
   z-index: 2;
   margin-left: 4px;
 }

 #selected-nodes-tools i.close-icon {
   color: #fff;
   float: right;
   margin-right: -6px;
   font-size: 12px;
 }

 #selected-nodes-tools .item .show-pin-table {
   height: 100px;
   width: 100px;
   position: absolute;
   bottom: 45px;
   margin-left: -40px;
   background-color: rgba(0, 0, 0, .4);
 }

 #selected-nodes-tools .item .show-pin-table.mini-point {
   height: 10px;
   width: 10px;
   border-radius: 5px;
   border: 1px #fff solid;
   position: absolute;
   bottom: 25px;
   margin-left: 14px;
 }

 #selected-nodes-tools .item .show-pin-table .close-icon {
   cursor: pointer;
   z-index: 1290;
   position: absolute;
   right: -2px;
   top: -2px;
   color: #ddd;
 }

 #selected-nodes-tools .item .show-pin-table .close-icon:hover {
   color: #fff;
 }

 #selected-nodes-tools .item .pin-table div {
   width: 26px;
   height: 26px;
   border-radius: 14px;
   border: 1px solid #000;
   cursor: pointer;
 }

 #selected-nodes-tools .item .pin-table div:hover {
   border: 1px solid #fff;
 }

 #selected-nodes-tools .item .pin-table div.default {
   background-color: #000;
   border: 1px solid #ddd;
 }

 #selected-nodes-tools .item .pin-table div.red {
   background-color: red;
 }

 #selected-nodes-tools .item .pin-table div.green {
   background-color: green;
 }

 #selected-nodes-tools .item .pin-table div.blue {
   background-color: blue;
 }

 #selected-nodes-tools .item i.title-text {
   position: absolute;
   bottom: 45px;
   width: 90px;
   margin-left: -35px;
   text-align: center;
   padding: 4px;
   display: none;
 }

 #selected-nodes-tools .item i.title-text .title-detail {
   background-color: rgb(41, 39, 39, 0.5);
   padding: 5px;
   border-radius: 2px;
 }

 #selected-nodes-tools .item:hover i.title-text {
   /* visibility: visible; */
   display: block;
   color: #fff;
   white-space: nowrap;
 }

 #selected-nodes-tools .item .iconfont:before {
   font-size: 22px;
 }

 #selected-nodes-tools .item .iconfont.icon-split {
   margin: 0 2px;
 }

 #dropdown-panel {
   pointer-events: visible;
 }

 #dropdown-panel .tour-dropdown-toggle {
   background-color: #06d6a0;
   border: 1px solid rgb(6, 214, 160, 0.5);
 }

 #dropdown-panel.dropdown-main {
   margin: 41px;
   width: 240px;
   height: 300px;
   padding: 10px;
   background-color: rgb(41, 39, 39, 0.5);
   border-radius: 10px;
   position: fixed;
   bottom: -40px;
   border: 1px rgb(41, 39, 39, 0.8) solid;
 }

 #dropdown-panel.dropdown-main .close-tour {
   float: right;
   color: #ddd;
   margin-top: -6px;
   cursor: pointer;
 }

 #dropdown-panel.dropdown-main .close-tour:hover {
   color: #fff;
 }

 #dropdown-panel.dropdown-main .dropdown-title {
   display: flex;
   direction: row;
   justify-content: space-around;
   color: #ddd;
   margin-bottom: 10px;
   font-size: 16px;
   font-weight: bolder;
 }

 #dropdown-panel.dropdown-main .dropdown-title i {
   cursor: pointer;
 }

 #dropdown-panel.dropdown-main .dropdown-title i:hover {
   color: #fff;
 }

 #dropdown-panel.dropdown-main .dropdown .item-name {
   width: 200px;
   margin-top: 2px;
 }

 #dropdown-panel.dropdown-main .dropright .dropdown-toggle::after {
   display: none;
 }

 /* ponter-item */

 #hotkey-list {
   font-size: 14px;
 }

 #hotkey-list table td {
   vertical-align: middle;
   line-height: 24px;
 }

 #hotkey-list .modal-content {
   width: 920px;
 }

 #hotkey-list .modal-backdrop {
   display: none;
 }

 #hotkey-list .modal-dialog {
   position: fixed;
   left: 50%;
   transform: translate(-50%);
   max-width: 100%;
 }

 #hotkey-list .modal.fade.show {
   display: block;
   pointer-events: none;
 }

 #hotkey-list .modal-content {
   height: 680px;
   width: 968px;
   overflow: auto;
 }

 #hotkey-list .modal-body {
   padding-bottom: 0;
   /* height: 548px; */
 }

 #hotkey-list .modal-body td.left {
   min-width: 120px
 }

 #hotkey-list .modal-body td.middle {
   min-width: 200px;
 }


 /*snapshot-panel*/
 .snapshot-panel {
   position: fixed;
   pointer-events: visible;
   top: 0px;
   right: 410px;
   z-index: 1390;
   padding: 10px;
   color: #383d41;
   background: rgba(16, 16, 16, 0.7);
   border: solid 1px gray;
   margin: 20px;
   border-radius: 4px;
   color: #ddd;
   width: 480px;
   max-height: 90%;
 }

 .snapshot-panel.none-expand {
   width: 280px;
 }

 .snapshot-panel.none-expand .header {
   border: none;
 }

 .snapshot-panel.none-expand .header>.title {
   padding-bottom: 0;
 }

 .snapshot-panel.none-expand .content {
   display: none !important;
 }

 .snapshot-panel.is-expand .header .min-show {
   display: none;
 }

 .snapshot-panel .btn,
 .snapshot-panel button {
   color: white;
 }

 .snapshot-panel .header {
   display: flex;
   justify-content: space-between;
   border-bottom: 1px solid rgba(222, 222, 222, 0.2);
 }


 .snapshot-panel .header>.title {
   font-size: 16px;
   font-weight: 600;
   padding-bottom: 6px;
 }

 .expander {
   cursor: pointer
 }

 .force-layout-toggle {
  cursor: pointer
}

 .snapshot-panel .header .actions {
   display: flex;
   position: relative;
 }

 .snapshot-panel .header .actions button {
   border: none;
   padding: 0;
   margin: 0 6px;
   font-size: 20px;
   width: 20px;
   height: 20px;
   line-height: 20px;
   color: #DDDDDD;
   background-color: transparent !important;
 }

 .snapshot-panel .content {
   height: 100%;
   /* padding-bottom: 30px; */
 }

 .snapshot-panel .content-header {
   display: inline-flex;
   justify-content: space-between;
   margin: 20px 0 10px 0;
   width: 100%;
 }

 .snapshot-panel .content-header>* {
   height: 32px;
   margin: 0;
   border-radius: 32px;
   padding: 3px 12px;
 }


 .snapshot-panel .perspective-list {
   margin: 0 -10px;
   height: 100%;
   /* padding-bottom: 60px; */
 }

 .snapshot-panel .perspective-list .detail-table {
   height: 100%;
   overflow-y: auto;
   margin-bottom: -10px;
 }

 .snapshot-panel .perspective-list tr:first-child {
   text-align: center;
 }

 .snapshot-panel .perspective-list tr:nth-child(n+2) {
   height: 150px;
 }

 .snapshot-panel .kv-table{
   border: none;
 }

 .snapshot-panel .kv-table tr{
   background-color: var(--main-bg-color);
   color: var(--main-text-color);
 }
 .snapshot-panel .kv-table tr:hover,
 .snapshot-panel .kv-table tr.active
  {
    background-color: var(--main-hover-bg-color);
    color: var(--main-hover-text-color);
  }

 .snapshot-panel .perspective-list tr td {
   padding: 0px;
   border-top: 1px solid rgba(222, 222, 222, 0.6);
 }

 .snapshot-panel .perspective-list td button {
   padding: 0px;
   margin: 0;
   width: 100%;
   text-align: center;
 }



 .snapshot-panel .perspective-list td.item-thumb {
   width: 200px;
   border-right: 1px solid rgba(222, 222, 222, 0.2);
   position: relative;
 }

.snapshot-panel .perspective-list td.item-thumb img {
  height: 108px;
  width: 180px;
  margin: 10px;
  border-radius: 4px;
  background-color: black;
  object-position: center;
  object-fit: contain;
}

 .snapshot-panel .perspective-list td.item-thumb .item-title,
 .snapshot-panel .perspective-list td.item-thumb .item-time {
   left: 0px;
   position: absolute;
   width: 100%;
 }

 .snapshot-panel .perspective-list td.item-thumb .item-title {
   top: 6px;
   left: 6px;
   padding: 10px;
  }

 .snapshot-panel .perspective-list td.item-thumb .item-time {
   bottom: 0px;
   text-align: center;
   opacity: 0.6;
   height: 26px;
 }

 .snapshot-panel .perspective-list td.item-thumb .item-title span {
   display: inline-block;
   padding: 0;
   font-size: 16px;
   border: 2px solid gray;
   width: 32px;
   height: 32px;
   text-align: center;
   line-height: 30px;
   border-radius: 16px;
 }

 .snapshot-panel .perspective-list td.item-note {
   width: 276px;
   max-width: 276px !important;
   color: #DDDDDD;
 }

 .snapshot-panel .perspective-list td.item-note .item-note-content {
   height: 120px;
   text-align: center;
   padding: 10px;
   overflow-y: hidden;
 }

 .snapshot-panel .perspective-list td.item-note .item-actions {
   display: flex;
 }

 .snapshot-panel .perspective-list td.item-note textarea {
   width: 100%;
   min-width: 100%;
   color: #DDDDDD;
   border: 1px solid gray;
   text-align: left;
   background-color: var(--main-bg-color);
 }

 .pop-panel .panel-foot .checkbox-group {
   height: 30px;
   line-height: 30px;
   margin-right: 14px;
   cursor: pointer;
 }

 .pop-panel .panel-foot .checkbox-group i.iconfont {
   padding-right: 4px;
 }
 
 .advance-group .checkbox-group i.icon-check-all span,
 .advance-group .checkbox-group i.icon-check-none span{
 font-family: "Lato", "Helvetica", sans-serif;
 }

 #expand-panel .content {
  max-height: 220px;
  overflow-y: auto;
  padding: 0 0px 10px 0;
  margin: 0px 0px -10px 0;
}

#expand-panel .content .prop-item .prop-key,
#expand-panel .content .prop-item .prop-value {
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -khtml-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

 #expand-panel .panel-foot{
  padding: 10px 12px 10px 12px;
 }  


 #expand-panel .panel-foot .input-group-prepend{
   height: 32px;
   line-height: 32px;
 }

 #expand-panel .panel-foot  .input-group-text{
  color: var(--main-text-color);
  background-color: var(--main-bg-color);
  border: none;
 }

 #expand-panel .panel-foot .form-control-expand {
   padding:   0 10px 0 10px;
   line-height: 30px;
   border-radius: 4px;
   border: none;
   width: 160px;
   height: 32px;
   color: var(--second-text-color);
   background-color: var(--main-bg-color);
 }

 #expand-panel .panel-foot .form-control-expand.limit{
  border-top-left-radius: 0;
  border-bottom-left-radius: 0 ;
 }

 #expand-panel .panel-foot .advance-group {
   background-color: rgba(0, 0, 0, .05);
 }
 #expand-panel .panel-foot .advance-group .iconfont {
  padding: 5px 10px;
  width: 100%;
 }
 #expand-panel .panel-foot .advance-group .icon-check-all:before{
  color: #000;
  background-color: #8DCFF8;
 }
 #expand-panel .panel-foot .advance-group .icon-check-none{
  background-color: rgba(0, 0, 0, .1);
 }
 #expand-panel .panel-foot .advance-group .icon-check-all {
  background-color: #8DCFF8;
  color: #000;
 }

 #tour-info-panel {
   height: 100%;
   width: 100%;
   background-color: rgba(0, 0, 0, 0.6) !important;
   position: absolute;
   left: 0;
   top: 0;
   z-index: 1390;
 }

 #tour-info-panel .main-panel {
   position: relative;
   color: rgb(6, 214, 160);
   height: 250px;
   width: 400px;
   z-index: 1390;
   margin-left: -200px;
   left: 50%;
   top: 20%;
   border: 1px solid rgb(6, 214, 160);
   border-radius: 6px;
   padding: 20px 16px;
   text-align: center;
   display: flex;
   flex-direction: column;
   justify-content: space-between;
 }

 #tour-info-panel .detail {
   margin-top: 20px;
   margin-bottom: 55px;
 }

 #tour-tips {
   z-index: 1098;
   width: 350px;
   height: 210px;
   border: rgb(6, 214, 160) 1px solid;
   border-radius: 8px;
   color: rgb(6, 214, 160);
   position: absolute;
   bottom: 4px;
   left: 44px;
 }

 #tour-tips #panel-arrow {
   height: 10px;
   width: 10px;
   border: 1px solid rgb(6, 214, 160);
   position: relative;
   top: 172px;
   left: -6px;
   border-top: none;
   border-right: none;
   z-index: 999;
   background-color: rgb(0, 0, 0);
   transform: rotate(45deg);
   -ms-transform: rotate(45deg);
   -moz-transform: rotate(45deg);
   -webkit-transform: rotate(45deg);
   -o-transform: rotate(45deg);
 }

 #tour-tips span.close-icon {
   float: right;
   padding-top: 5px;
   padding-right: 8px;
   font-weight: bolder;
   cursor: pointer;
 }

 #tour-tips .tour-body {
   height: 100%;
   padding: 28px 18px;
   text-align: center;
 }

 #tour-tips .tour-body .tour-btn-group {
   margin-top: 10px;
 }

 #tour-tips .tour-body .tour-btn-group button {
   margin-top: 35px;
 }

 #tour-tips .tour-body .tour-detail {
   padding-bottom: 6px;
 }

 #tour-tips .tour-body .tips-btn {
   height: 26px;
   line-height: 26px;
   padding: 0;
   padding-left: 12px;
   padding-right: 12px;
   font-size: .8rem;
   border-radius: 14px;
 }

 .animated .animation-count-4 {
   animation-iteration-count: 4;
 }

 #tip-reset {
   height: 0px;
   width: 0px;
   cursor: pointer;
   z-index: 1390;
   position: absolute;
   top: 146px;
   left: 40px;
   transform: scale(0.5)
 }

 #tip-reset .arrow {
   position: relative;
   top: 30px;
   left: 12px;
   height: 40px;
   width: 40px;
   border: 1px solid rgb(6, 214, 160);
   border-top: none;
   border-right: none;
   z-index: 999;
   transform: rotate(45deg);
   background-color: rgb(6, 214, 160);
 }

 #tip-reset .circle-btn {
   z-index: 999;
   position: relative;
   top: -30px;
   left: 18px;
   height: 80px;
   width: 80px;
   border: 1px solid rgb(6, 214, 160);
   border-radius: 40px;
   background-color: rgb(6, 214, 160);
   color: #000;
   line-height: 80px;
   text-align: center;
   font-weight: 800;
   font-size: 22px;
 }

 #flight-panel {
   position: fixed;
   top: 100px;
   left: 100px;
   width: 200px;
   height: 80px;
   background-color: #666;
   color: whitesmoke;
   z-index: 1390;
 }

 #flight-panel.timer .header {
   width: 100%;
   height: 28px;
   background-color: #00B7FF;
   border-radius: 6px 6px 0 0;
   display: flex;
   flex-direction: row;
   justify-content: center;
 }

 #flight-panel.timer .header span {
   height: 28px;
   line-height: 28px;
 }

 #flight-panel.timer .header span.title {
   font-weight: bold;
 }

 #flight-panel.timer .btn-start {
   width: 50%;
   height: 52px;
   float: left;
   text-align: center;
   line-height: 53px;
   border-right: solid #ddd 1px;
   cursor: pointer;
 }

 #flight-panel.timer .btn-restart {
   width: 50%;
   height: 52px;
   float: right;
   text-align: center;
   line-height: 53px;
   cursor: pointer;
 }

 #pan-border {
   position: absolute;
   top: 0;
   left: 0;
   width: 100%;
   height: 100%;
   pointer-events: none;
   /* border: 2px solid red; */
   z-index: 1390;
 }

 #pan-border .left-border {
   /* pointer-events: auto; */
   position: absolute;
   top: 0;
   left: 0;
   height: 100%;
   width: 40px;
   background: -webkit-linear-gradient(right, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
   background: -o-linear-gradient(left, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
   background: -moz-linear-gradient(left, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
   background: linear-gradient(to left, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
 }

 #pan-border .top-border {
   /* pointer-events: auto; */
   position: absolute;
   top: 0;
   left: 0;
   height: 40px;
   width: 100%;
   background: -webkit-linear-gradient(bottom, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
   background: -o-linear-gradient(top, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
   background: -moz-linear-gradient(top, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
   background: linear-gradient(to top, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
 }

 #pan-border .right-border {
   /* pointer-events: auto; */
   position: absolute;
   top: 0;
   right: 0;
   height: 100%;
   width: 40px;
   background: -webkit-linear-gradient(left, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
   background: -o-linear-gradient(right, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
   background: -moz-linear-gradient(right, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
   background: linear-gradient(to right, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
 }

 #pan-border .bottom-border {
   /* pointer-events: auto; */
   position: absolute;
   bottom: 0;
   left: 0;
   height: 40px;
   width: 100%;
   background: -webkit-linear-gradient(top, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
   background: -o-linear-gradient(bottom, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
   background: -moz-linear-gradient(bottom, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
   background: linear-gradient(to bottom, rgb(6, 214, 160, 0), rgb(6, 214, 160, .5));
 }

 /* mouse right panel */
 #mouse-right {
   position: absolute;
   top: 0;
   left: 0;
   width: 100%;
   height: 100%;
   z-index: 1380;
 }

 #mouse-panel-detail {
   pointer-events: auto;
   position: absolute;
   top: 0;
   left: 0;
   /* max-width: 220px; */
   min-width: 180px;
   height: 420px;
   z-index: 1390;
   cursor: pointer;
 }

 /* #mouse-right #mouse-panel-detail.mouse-right-panel ::-webkit-scrollbar  
{  
    background-color: #F5F5F5;  
} */

 #mouse-right #mouse-panel-detail.mouse-right-panel ::-webkit-scrollbar-thumb {
   /* border-radius: 0px;   */
   -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
 }

 #mouse-right #mouse-panel-detail.mouse-right-panel ::-webkit-scrollbar-track {
   background-color: #F5F5F5;
   border-top-right-radius: 4px;
   border-bottom-right-radius: 4px;
 }

 #mouse-right #mouse-panel-detail.mouse-right-panel ul {
   padding: 0px;
   overflow-y: auto;
   border-radius: 4px;
 }

 #mouse-right #mouse-panel-detail.mouse-right-panel ul.has-scroll,
 #mouse-right #mouse-panel-detail.mouse-right-panel ul.has-scroll li:first-child,
 #mouse-right #mouse-panel-detail.mouse-right-panel ul.has-scroll li:last-child {
   border-top-right-radius: 0;
   border-bottom-right-radius: 0;
 }

 #mouse-right #mouse-panel-detail.mouse-right-panel li.list-group-item:hover {
   background-color: #ddd;
 }

 #mouse-right #mouse-panel-detail.mouse-right-panel li i {
   margin-right: 10px;
 }


 #trace-neighbors-slider {
   z-index: 1090;
   position: fixed;
   width: 300px;
   bottom: 60px;
   right: 400px;
 }


 #trace-neighbors-slider .header .close {
   color: white;
   opacity: 0.8;
   position: absolute;
   right: -32px;
   top: -6px;
 }

 #trace-neighbors-slider .max-min {
   color: #DDDDDD;
 }


 .pop-center-panel {
   z-index: 1490;
   position: fixed;
  color: #ACACAC;
  width: 480px;
  top: 20px;
  right: 430px;
  background: rgba(48, 48, 48, 0.9);
  border: 1px solid #303030;
  border-radius: 5px;
 }

 .pop-center-panel .select__control {
  background-color: #434343;
  border: none;
 }

 .pop-center-panel .select__control .css-1uccc91-singleValue {
  color: #DBDBDB;
 }

 .pop-center-panel .content {
   margin: 0;
   padding: 10px;
 }

 .pop-center-panel .header .title {
   line-height: 32px;
   font-size: 18px;
   padding: 0 10px;
   border-bottom: 1px solid rgba(222, 222, 222, 0.2);
 }

 .pop-center-panel .create-lable-select {
   width: 100%;
   color: black;
 }

 .pop-center-panel .table-wrapper{
   max-height: 400px;
   overflow: auto;
 }

 .pop-center-panel .content .table {
   background-color: #303030;
   margin: 10px 0 0 0;
 }

 .pop-center-panel .content .table input {
   height: 100%;
   width: 100%;
   border: none;
   background-color: inherit;
   border-radius: 5px;
   color: #ACACAC;
   font-size: 14px;
   float: left;
   height: 32px;
   border-bottom: 1px solid #7D7D7D;
   border-radius: 0;
 }

 .pop-center-panel .content .table textarea {
    width: calc(100% - 35px);
    border: 0;
    border-bottom: 1px solid var(--main-border-color);
    color: var(--main-text-color);
    background-color: inherit;
    height: 32px;
    box-shadow: inset 0px 2px 5px -5px grey;
    padding: 4px 10px;
 }
 

 .pop-center-panel .content .table input:placeholder,
 .pop-center-panel .content .table textarea:placeholder {
  color: #5A5A5A;
 }

 .pop-center-panel .content .table .key {
   width: 40%;
 }

 .pop-center-panel .content .table .value {
   width: 60%;
 }

 .pop-center-panel .content .table .value .remove {
   height: 32px;
   width: 32px;
   float: right;
   text-align: center;
   font-size: 14px;
   z-index: 1080;
   cursor: pointer;
 }

 .pop-center-panel .content .table.kv-table tr:nth-child(n+2):hover {
   background-color: transparent;
 }


 .pop-center-panel .footer .actions {
   position: relative;
   display: flex;
   justify-content: flex-end;
   margin-bottom: 10px;
 }

 .pop-center-panel .footer .actions .btn {
   height: 32px;
   font-size: 16px;
   border-radius: 16px;
   padding: 0 20px;
 }

 .pop-center-panel .edge-source-target {
   display: flex;
   flex-direction: column;
   margin: 10px 0;
 }

 .pop-center-panel .edge-source-target .kv-button {
   /*border-color: #ddd;*/
   margin: 5px 0;
 }

 .pop-center-panel .edge-source-target>div {
   display: flex;
   flex-direction: row;
   font-size: 14px;
 }

 .pop-center-panel .edge-source-target .source-target-actions {
   justify-content: space-between;
   margin-bottom: 10px;
 }

 #plugins-container {
   position: absolute;
   z-index: 1490;
   /* pointer-events: none; */
 }

 /*drag-selection*/
 .drag-selection {
   position: fixed;
   top: 0;
   left: 0;
   z-index: 9999;
 }

 .drag-selection>div {
   width: 100%;
   background-color: rgba(0, 0, 0, 0.6);
 }

 .drag-selection>div.middle {
   background-color: rgba(0, 0, 0, 0);
   height: 100%;
 }

 .drag-selection>div.middle>div {
   background-color: rgba(0, 0, 0, 0.6);
   float: left;
 }

 .drag-selection>div.middle>div.center {
   background-color: rgba(0, 0, 0, 0);
   border: 1px dashed gray;
   width: 0;
   height: 0;
   position: relative;
 }

 .drag-selection>div.middle>div.center .tools {
   position: absolute;
   width: auto;
   background-color: rgb(41, 39, 39, 0.5);
   right: 0;
   height: 30px;
   bottom: -30px;
   margin-left: -320px;
 }

 .drag-selection>div.middle>div.center .tools.up {
   bottom: 0px;
 }

 .drag-selection .tools>div.tool-item .tips {
   display: none;
 }

 .drag-selection .tools>div.tool-item:hover .tips {
   display: inline-block;
 }

 .drag-selection .tools>div.tool-item {
   float: left;
   padding: 3px 15px;
   cursor: pointer;
   color: #DDDDDD;
 }

 .drag-selection .tools>div.tool-item.active {
   background-color: rgba(189, 33, 27, 0.6);
 }

 .drag-selection .tools>div.tool-item .icon {
   font-size: 20px;
   line-height: 26px;
 }

 .drag-selection .tools>div.tool-item .tips {
   display: none;
   position: absolute;
   bottom: 45px;
   margin-left: -110px;
   text-align: center;
   width: 200px;
 }

 .drag-selection .tools>div.tool-item .tips span {
   background-color: rgb(41, 39, 39, 0.5);
   padding: 4px;
   border-radius: 2px;
   width: fit-content;
   font-style: italic;
 }

 /* algorithm path-finding*/
 .path-finding .form-inline label {
   justify-content: left;
   word-wrap: break-word;
   width: 100%;
 }
 .path-finding .form-inline label span {
  padding-left: 15px !important;
  width: calc(100% - 45px);
 }

 .path-finding .form-inline.actions {
   margin-top: 20px;
 }

 .label-window  .kv-button.active {
  cursor: pointer;
  color: #000;
}

.css-1pahdxg-control {
  box-shadow: none !important;
}

@media print {
  #map,
  #tip-container,
  #confirm-modal,
  #react-dom{
    display: none; 
  }
}

.loading-spinner {
  text-align: center;
}

.loading-spinner > .spinner-border {
  margin-right: 0.5rem;
}

.loading-spinner > .loading-message {
  display: inline-block;
  vertical-align: middle;
}