package schema.execution.operations;

import schema.execution.ExecutionContext;
import schema.execution.Operation;

/**
 * 更新结果操作，将当前位的计算结果添加到总结果中
 */
public class UpdateResultOperation implements Operation {
    @Override
    public Object execute(ExecutionContext context) {
        String result = (String) context.getVariable("结果");
        int currentDigitResult = (int) context.getVariable("当前位结果");
        
        context.setVariable("结果", currentDigitResult + result);
        
        return null;
    }
}
