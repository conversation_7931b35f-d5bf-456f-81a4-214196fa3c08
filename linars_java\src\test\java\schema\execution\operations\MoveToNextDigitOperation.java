package schema.execution.operations;

import schema.execution.ExecutionContext;
import schema.execution.Operation;

/**
 * 移动到下一位操作，更新当前位索引
 */
public class MoveToNextDigitOperation implements Operation {
    @Override
    public Object execute(ExecutionContext context) {
        int currentIndex = (int) context.getVariable("当前位索引");
        context.setVariable("当前位索引", currentIndex - 1);
        
        return null;
    }
}
