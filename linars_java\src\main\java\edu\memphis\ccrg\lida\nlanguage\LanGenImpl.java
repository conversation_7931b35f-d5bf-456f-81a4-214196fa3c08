/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.nlanguage;

import edu.memphis.ccrg.lida.framework.*;
import edu.memphis.ccrg.lida.framework.initialization.Initializable;
import edu.memphis.ccrg.lida.framework.shared.*;
import edu.memphis.ccrg.lida.framework.strategies.DecayStrategy;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.globalworkspace.BroadcastListener;
import edu.memphis.ccrg.lida.globalworkspace.Coalition;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamListener;
import edu.memphis.ccrg.lida.proceduralmemory.*;
import edu.memphis.ccrg.lida.workspace.Workspace;
import edu.memphis.ccrg.lida.workspace.WorkspaceContent;
import edu.memphis.ccrg.lida.workspace.WorkspaceListener;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Default implementation of {@link ProceduralMemory}. Indexes scheme by context
 * elements for quick access. Assumes that the {@link Condition} of {@link Scheme} are {@link Node} only.
 * 默认实现。通过上下文*元素建立索引方案，以便快速访问。假设{@link Scheme}的{@link Condition}仅是{@link Node}
 * <AUTHOR> J. McCall
 * <AUTHOR> Snaider
 */
public class LanGenImpl extends FrameworkModuleImpl implements LanGen,
		BroadcastListener, WorkspaceListener {

	private static final Logger logger = Logger.getLogger(LanGenImpl.class.getCanonicalName());
	private static final ElementFactory factory = ElementFactory.getInstance();

	@Override
	public void receiveWorkspaceContent(ModuleName originatingBuffer, WorkspaceContent content) {

	}

	/**
	 * The possible type of usage for a condition inside a {@link Scheme}
	 *{@link Scheme}中某个条件可能的用法类型
	 */
	public enum ConditionType{
		/**
		 * A {@link Condition} that is part of a scheme's context.
		 */
		CONTEXT, 
		/**
		 * A {@link Condition} that is part of a scheme's adding list.
		 */
		ADDINGLIST,
		/**
		 * A {@link Condition} that is part of a scheme's deleting list.
		 * Not yet supported.
		 */
		DELETINGLIST,
		/**
		 * A {@link Condition} that is part of a scheme's negated context.
		 * Not yet supported.
		 */
		NEGATEDCONTEXT
	};
	/*节点在其上下文中索引的方案
	 * Schemes indexed by Nodes in their context.
	 */
	protected Map<Object, Set<Scheme>> contextSchemeMap = new ConcurrentHashMap<Object, Set<Scheme>>();

	/*
	 * Schemes indexed by Nodes in their adding list.
	 * 节点在其添加列表中索引的方案
	 */
	protected Map<Object, Set<Scheme>> addingSchemeMap = new ConcurrentHashMap<Object, Set<Scheme>>();

	/*
	 * Set of all schemes current in the module. Convenient for decaying the schemes' base-level activation.
	 * 模块中当前所有方案的集合。方便衰减方案的基础级别激活
	 */
	private Set<Scheme> schemeSet = Collections.newSetFromMap(new ConcurrentHashMap<Scheme,Boolean>());

	/**
	 * A pool of all conditions (context and adding) in all schemes in the
	 * procedural memory
	 * 程序存储器中所有方案中所有条件（上下文和相加）的池
	 */
	protected Map<Object, Condition> conditionPool = new HashMap<Object, Condition>();

	/**
	 * Recent contents of consciousness that have not yet decayed away.
	 * 意识的最新内容尚未消失
	 */
	protected InternalNodeStructure broadcastBuffer = new InternalNodeStructure("PamNodeImpl","PamLinkImpl");

//	protected InternalNodeStructure goalBuffer = new InternalNodeStructure("PamNodeImpl","PamLinkImpl");
//
//	protected InternalNodeStructure actBuffer = new InternalNodeStructure("PamNodeImpl","PamLinkImpl");
//
//	protected InternalNodeStructure planBuffer = new InternalNodeStructure("PamNodeImpl","PamLinkImpl");

	/**
	 * Allows Nodes to be added without copying. 
	 * Warning: doing so allows the same java object of Node to exist in multiple places.
	 * 允许添加节点而不进行复制。 *警告：这样做允许Node的相同java对象在多个位置存在
	 * <AUTHOR> J. McCall
	 * @see NodeStructureImpl
	 */
	protected class InternalNodeStructure extends NodeStructureImpl {

		public InternalNodeStructure(String nodeType, String linkType) {
			super(nodeType, linkType);
		}

		@Override
		public Node addNode(Node n, boolean copy) {
			return super.addNode(n, copy);
		}
	}

	/*
	 * Listeners of this Procedural Memory
	 */
	private List<PamListener> lanListeners = new ArrayList<PamListener>();

	private static final double DEFAULT_SCHEME_SELECTION_THRESHOLD = 0.1;	
	/*
	 * Determines how much activation a scheme should have to be instantiated
	 * 确定方案必须实例化多少激活
	 */
	private double schemeSelectionThreshold = DEFAULT_SCHEME_SELECTION_THRESHOLD;
	
	private static final double DEFAULT_CONDITION_WEIGHT = 1.0;//for Javier
	
	private static final String DEFAULT_SCHEME_CLASS = "edu.memphis.ccrg.lida.proceduralmemory.SchemeImpl";
	/**
	 * Qualified name of the {@link Scheme} class used by this module 
	 */
	public static String schemeClass = DEFAULT_SCHEME_CLASS;
	/**
	 * DecayStrategy used by all conditions in the condition pool (and broadcast buffer).
	 * 条件池（和广播缓冲区）中所有条件使用的DecayStrategy
	 */
	private DecayStrategy conditionDecay;
	
	/**
	 * This module can initialize the following parameters:<br><br/>
	 * 
	 * <b>proceduralMemory.schemeSelectionThreshold type=double</b> amount of activation schemes must have to be instantiated, default is 0.0<br/>
	 * <b>proceduralMemory.contextWeight type=double</b> The weight of context conditions for the calculation of scheme activation. Should be positive<br/>
	 * <b>proceduralMemory.addingListWeight type=double</b> The weight of adding list conditions for the calculation of scheme activation. Should be positive<br/>
	 * <b>proceduralMemory.conditionDecayStrategy type=string</b> The DecayStrategy used by all conditions in the condition pool (and broadcast buffer).<br/> 
	 * <b>proceduralMemory.schemeClass type=string</b> qualified name of the {@link Scheme} class used by this module <br/>
	 *
	 * 该模块可以初始化以下参数：proceduralMemory.schemeSelectionThreshold type = double
	 * 必须实例化激活方案的数量，默认值为0.0
	 * proceduralMemory.contextWeight type = double
	 * 用于方案激活计算的上下文条件权重。应该为正数<br/>
	 * proceduralMemory.addingListWeight type = double
	 * 为计划激活计算添加列表条件的权重。应该为正数。
	 * proceduralMemory.conditionDecayStrategy type = string
	 * 条件池（和广播缓冲区）中所有条件使用的DecayStrategy。
	 * proceduralMemory.schemeClass type =字符串
	 * 此模块使用的{@link Scheme}类的限定名称<br/>
	 * 
	 * @see Initializable
	 */
	@Override
	public void init() {
		schemeSelectionThreshold = getParam(
				"proceduralMemory.schemeSelectionThreshold",
				DEFAULT_SCHEME_SELECTION_THRESHOLD);
		String decayName = getParam("proceduralMemory.conditionDecayStrategy",
				factory.getDefaultDecayType());
		conditionDecay = factory.getDecayStrategy(decayName);
		schemeClass = getParam("proceduralMemory.schemeClass",
				DEFAULT_SCHEME_CLASS);
	}

	public WorkspaceBuffer sceneGraph;
	public WorkspaceBuffer csm;
	public WorkspaceBuffer seqGraph;
	public WorkspaceBuffer goalGraph;
	public WorkspaceBuffer concentGraph;
	public WorkspaceBuffer grammarGraph;

	public NodeStructure csmNs;
	public NodeStructure nonNs;
	public NodeStructure feelNs;
	public NodeStructure conNs;
	public NodeStructure goalNs;
	public NodeStructure seqNs;
	public NodeStructure sceneNs;
	public NodeStructure yufaNs;

	public Workspace workspace;
	private PAMemory pam;

	private Map<Integer, String> words;
	private int wordnum;
	private boolean isdone;
	private Node node;
	private int level;

	Map<String,Collection<Link>> scenemap;
	Map<String,List<Link>> yufamap;

	@Override
	public void setAssociatedModule(FrameworkModule m, String usage) {
		if(m instanceof Workspace){
			csm = (WorkspaceBuffer) m.getSubmodule(ModuleName.CurrentSM);
			seqGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.SeqGraph);
			goalGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.GoalGraph);

			concentGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.ConcentGraph);
			sceneGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.SceneGraph);
			grammarGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.GrammarGraph);

			csmNs = csm.getBufferContent(null);
//			nonNs = nonGraph.getBufferContent(null);

//			feelNs = feelGraph.getBufferContent(null);
//			goalNs = goalGraph.getBufferContent(null);

			conNs = concentGraph.getBufferContent(null);
			seqNs = seqGraph.getBufferContent(null);

			sceneNs = sceneGraph.getBufferContent(null);
			yufaNs = grammarGraph.getBufferContent(null);
		}else if(m instanceof PAMemory){
			pam = (PAMemory) m;
		}else{
			logger.log(Level.WARNING, "Cannot add module {1}",
					new Object[]{TaskManager.getCurrentTick(),m});
		}
	}

	@Override
	public void addListener(ModuleListener l) {
		if (l instanceof PamListener) {
			lanListeners.add((PamListener) l);
		}else{
			logger.log(Level.WARNING, "Requires lanListeners but received {1}",
					new Object[]{TaskManager.getCurrentTick(), l});
		}
	}

	@Override
	public void receiveBroadcast(Coalition coalition) throws Exception {
		scenemap = sceneNs.getMainMap();
		yufamap = yufaNs.getMainMap();
		words = new HashMap<>();
		// 等语法完善才生成
		if (yufamap != null && yufamap.size() > 0 && scenemap != null && scenemap.size() > 0){
//			wordnum = 0;
//
//			// 取到第一层父语法
//			List<Link> mainlinsks = yufamap.get("0_" + sceneNs.getMainNodeId());
//			// 同层的都顺次解决，子层的继续深挖
//			for (int i = 0; i < mainlinsks.size(); i++) {
//				node = mainlinsks.get(i).getSource();
//				invoke(1,"0_" + sceneNs.getMainNodeId());
//			}
//
//			if(AgentStarter.scenelist.size() > 0){
//				sceneNs.setMainNodeId(Integer.valueOf(AgentStarter.scenelist.get(0)));
//				AgentStarter.scenelist.remove(0);
//			}else {
//				sceneNs.setMainNodeId(0);
//			}
//
//			yufaNs.setMainMap(null);
//			sceneNs.setMainMap(null);
		}

		// 注意 coalition 与 condition
		learn(coalition);

		// Spawn a new task to activate and instantiate relevant schemes.
		// This task runs only once in the next tick
		// todo 将节点与相关信息拆分的好处：先判断节点重要性？但判断需要背后的信息
		// 不判断buffer 是确信不为空？
//		taskSpawner.addTask(new FrameworkTaskImpl() {
//			@Override
//			protected void runThisFrameworkTask() {
//				cancel();
//			}
//		});
	}

	public void invoke(int level0, String upmapkey) {
		String name = "";
		String mapkey = level0 + "_" + node.getTNname();
		List<Link> mainlinsks = yufamap.get(mapkey);
		isdone = false;
		if (mainlinsks == null) {
			// 不是子场景，则直接在前一层查场景元素，匹配则替换，scenename序号和点都是前一层
			for (Link scenelink:scenemap.get(upmapkey)){
				if(scenelink.getTNname().equals(node.getTNname())){
					lanListeners.get(0).receivePercept(scenelink.getSource(), ModuleName.WordGraph);
					name = scenelink.getSource().getTNname();
					words.put(wordnum,name);
					isdone = true;
				}
			}
			// 没有场景元素匹配，直接拼接当前词
			if (!isdone) {
				name = node.getTNname();
				words.put(wordnum,name);
				lanListeners.get(0).receivePercept(node, ModuleName.WordGraph);
			}
			// todo 内部语言重理解，重新输入pam走激活过程，主要是知道自己说了啥和正确与否
			System.out.println("新词-----------" + name);
			wordnum++;
		}else {
			for (int i = 0; i < mainlinsks.size(); i++) {
				node = mainlinsks.get(i).getSource();
				// 只能一个地方加加，深度担当，其他都按同层处理
				invoke(level0 + 1, mapkey );
			}
		}
	}


	@Override
	public void learn(Coalition coalition) {
	}

	@Override
	public void decayModule(long ticks){
		broadcastBuffer.decayNodeStructure(ticks);
	}

	@Override
	public Object getModuleContent(Object... params) {
		if("schemes".equals(params[0])){
			return Collections.unmodifiableCollection(schemeSet);
		}
		return null;
	}
}