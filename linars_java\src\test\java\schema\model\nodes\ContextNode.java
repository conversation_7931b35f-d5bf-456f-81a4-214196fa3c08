package schema.model.nodes;

import schema.model.Node;

/**
 * 上下文节点，表示执行上下文，如加法计算的整体环境
 */
public class ContextNode extends Node {
    private String scope; // 作用域：global, local

    public ContextNode(String id, String name, String scope) {
        super(id, name, "ContextNode");
        this.scope = scope;
        setProperty("scope", scope);
    }

    public String getScope() {
        return scope;
    }
}
