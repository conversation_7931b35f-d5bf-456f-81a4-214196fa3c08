package org.opennars.operator.misc;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
import org.neo4j.graphdb.Transaction;
import org.opennars.entity.Task;
import org.opennars.interfaces.Timable;
import org.opennars.io.Parser;
import org.opennars.language.Product;
import org.opennars.language.Similarity;
import org.opennars.language.Variable;
import org.opennars.operator.Operation;
import org.opennars.operator.Operator;
import org.opennars.main.Debug;

import java.util.*;
import java.util.logging.Logger;
import java.util.logging.Level;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.narsese;

/**
 * 搜索单条，输出具体内容，基于多个条件综合
 * 案例：苹果+去哪了，输出场景：(*,苹果,$状态)
 *
 * 该操作符实现了基于Neo4j图数据库的智能搜索功能，
 * 能够根据输入的复合词项构建Cypher查询语句，
 * 并返回匹配的结果作为NARS任务。
 */
public class SearchSOM extends Operator {

    private static final Logger logger = Logger.getLogger(SearchSOM.class.getName());

    public SearchSOM() {
        super("^SearchSOM");
    }

    public SearchSOM(final String name) {
        super(name);
    }

    /**
     * 执行搜索操作
     *
     * @param operation 操作对象
     * @param args 参数数组
     * @param memory 内存对象
     * @param time 时间接口
     * @return 任务列表
     */
    @Override
    public List<Task> execute(Operation operation, Term[] args, Memory memory, Timable time) {
        List<Task> tasks = new ArrayList<>();

        if (args == null || args.length == 0) {
            logger.warning("SearchSOM: 参数为空");
            return tasks;
        }

        if (!(args[0] instanceof CompoundTerm)) {
            logger.warning("SearchSOM: 第一个参数不是复合词项");
            return tasks;
        }

        try {
            CompoundTerm ctt = (CompoundTerm) args[0];
            Term[] terms = ctt.term;

            if (Debug.DETAILED) {
                logger.info("SearchSOM: 开始搜索，词项: " + Arrays.toString(terms));
            }

            String result = getAnswer(terms);

            if (result != null && !result.trim().isEmpty()) {
                Task task = createTaskFromResult(result);
                if (task != null) {
                    tasks.add(task);
                }
            } else {
                logger.info("SearchSOM: 未找到匹配结果");
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "SearchSOM执行过程中发生错误", e);
            if (Debug.SHOW_EXECUTION_ERRORS) {
                memory.event.emit(org.opennars.io.events.OutputHandler.ERR.class, e);
            }
        }

        return tasks;
    }

    /**
     * 从搜索结果创建任务
     *
     * @param result 搜索结果字符串
     * @return 创建的任务对象
     */
    private Task createTaskFromResult(String result) {
        try {
            return narsese.parseTask(result + ".");
        } catch (Parser.InvalidInputException e) {
            logger.log(Level.WARNING, "解析任务时发生错误: " + result, e);
            return null;
        }
    }

    private static String getAnswer(Term[] terms) {
        String sname = "";
        String cypher = "";

        // 苹果+去哪了，输出场景：(*,苹果,$状态)
        // (^搜索匹配,(*,(*,苹果,$状态),<$状态<->去哪了>)）
        // 搜索匹配条件集，直接用cypher语句，查包含苹果和一个其他元素a的场景，并且元素a与【去哪了】相似

//        cypher = "MATCH (c{name:'苹果'})-[r:arg0]->(a)<-[r1:arg1]-(b)-[r2:相似]->(d{name:'去哪了'}) return a";

        cypher = assembleCypherQuery3(terms);

        // todo 拓展为与内容无关，适配各种搜索场景和条件集，组装查询语句
        //      考虑pam的扩散，顺序扩散，逆向扩散，双向扩散。非传递性的也扩散了？
        // 案例，输入两条语句：[(*,苹果,$状态), <$状态 <-> 去哪了>]
        // 如果term为Product等【格式如（*，x，y）】，则每个单词项转为一条语句，即类似(c)-[r:arg0]->(a)，arg的标号按顺序
        // 如果term为Similarity等【格式如：除圆括号外的括号，如<>】，则括号内内容作为一个整体，类似(b)-[r2:相似]->(d)，中间的双箭头为“相似”，其他同理
        // 如果term的某组件为Variable【格式有$符号】，则代表其关联的是查询目标，以此为输出。非$的词项，作为查询条件，作为name属性
        // 可能有多个变量，还包括自变量$和因变量#，需要用变量名来区分，可直接用变量名做节点
        // 语句term类型有多种可能，如Product和Similarity，先判断类型，
        cypher = "MATCH (苹果)-[r:arg0]->(a), (a)<-[r1:arg1]-(状态), (状态)-[r2:相似]->(d)\n" +
                 "WHERE 苹果.name = '苹果' AND d.name = '去哪了'\n" +
                 "RETURN a";


        cypher = "MATCH (苹果)-[r0:arg0]->(a0), (状态)-[r1:arg1]->(a0), (状态)-[r_sim:相似]->(去哪了)\n" +
                 "WHERE 苹果.name = '苹果' AND 去哪了.name = '去哪了'\n" +
                 "RETURN a0";

        try (Transaction tx = graphDb.beginTx()) {
            if (Debug.DETAILED) {
                logger.info("执行Cypher查询: " + cypher);
            }

            List<Map<String, Object>> result = NeoUtil.getByCypher(cypher, tx);

            if (result != null && !result.isEmpty()) {
                sname = extractResultFromQuery(result);
            }

            tx.commit();
        } catch (Exception e) {
            logger.log(Level.WARNING, "执行Cypher查询时发生错误: " + cypher, e);
        }

        return sname;
    }

    /**
     * 从查询结果中提取答案
     *
     * @param result 查询结果列表
     * @return 提取的结果字符串
     */
    private static String extractResultFromQuery(List<Map<String, Object>> result) {
        for (Map<String, Object> map : result) {
            // 尝试多种可能的返回键名
            for (String key : Arrays.asList("a0", "a", "result", "n")) {
                Object value = map.get(key);
                if (value instanceof org.neo4j.graphdb.Node) {
                    org.neo4j.graphdb.Node node = (org.neo4j.graphdb.Node) value;
                    Object nameProperty = node.getProperty("name", null);
                    if (nameProperty != null) {
                        return nameProperty.toString();
                    }
                }
            }
        }
        return null;
    }


    /**
     * 构建Cypher查询语句的核心方法
     * 支持Product和Similarity类型的词项
     *
     * @param terms 词项数组
     * @return 构建的Cypher查询字符串
     */
    private static String assembleCypherQuery3(Term[] terms) {
        if (terms == null || terms.length == 0) {
            return "";
        }

        StringBuilder cypher = new StringBuilder("MATCH ");
        List<String> whereClauses = new ArrayList<>();
        Set<String> vars = new HashSet<>();
        int index = 0;

        try {
            for (Term term : terms) {
                if (!(term instanceof CompoundTerm)) {
                    continue;
                }

                CompoundTerm compoundTerm = (CompoundTerm) term;
                Term[] subTerms = compoundTerm.term;

                if (compoundTerm instanceof Product) {
                    processProductTerm(compoundTerm, subTerms, cypher, whereClauses, vars, index);
                } else if (compoundTerm instanceof Similarity) {
                    processSimilarityTerm(compoundTerm, subTerms, cypher, whereClauses);
                }

                index++;
            }
        } catch (Exception e) {
            Logger.getLogger(SearchSOM.class.getName()).log(Level.WARNING, "构建Cypher查询时发生错误", e);
            return "";
        }
        // 去除最后的逗号和空格
        if (cypher.length() > 0) {
            cypher.setLength(cypher.length() - 2);
        }
        // 添加WHERE子句
        if (!whereClauses.isEmpty()) {
            cypher.append("\nWHERE ");
            cypher.append(String.join(" AND ", whereClauses));
        }
        // 添加RETURN子句，这里简单返回所有变量节点，可以根据需要调整
        cypher.append("\nRETURN ");

//        for (String clause : whereClauses) {
//            if (clause.contains(".name = ")) {
//                String varName = clause.substring(0, clause.indexOf(".name = "));
//                if (varName.startsWith("a")) { // 是我们生成的变量名
//                    vars.add(varName);
//                }
//            }
//        }
//        // 假设 vars 是已经填充的变量名集合
//        boolean isFirst = true; // 标记是否为第一个元素
//        for (String var : vars) {
//            if (!isFirst) {
//                cypher.append(", "); // 如果不是第一个元素，则添加逗号和空格
//            }
//            cypher.append(var); // 添加变量名
//            isFirst = false; // 设置标记为 false，表示下一个元素不是第一个了
//        }

        // 假设 vars 是已经填充的变量名集合
        StringJoiner sj = new StringJoiner(", "); // 创建一个使用逗号和空格作为分隔符的StringJoiner
        for (String var : vars) {
            sj.add(var); // 添加变量名到StringJoiner
        }
        cypher.append(sj); // 将StringJoiner转换为字符串并添加到cypher中

        if (cypher.length() > 0 && cypher.charAt(cypher.length() - 1) == ',') {
            cypher.deleteCharAt(cypher.length() - 1); // 去除最后的逗号
            cypher.append(" "); // 添加空格以保持格式整洁
        }

        return cypher.toString();
    }

    /**
     * 处理Product类型的词项
     */
    private static void processProductTerm(CompoundTerm compoundTerm, Term[] subTerms,
                                         StringBuilder cypher, List<String> whereClauses,
                                         Set<String> vars, int index) {
        String varName = "a" + index;
        vars.add(varName);

        for (int i = 0; i < subTerms.length; i++) {
            String termName = getCleanVarName(subTerms[i].toString());
            cypher.append("(").append(termName).append(")-[r").append(i)
                  .append(":arg").append(i).append("]->(").append(varName).append("), ");

            // 如果不是变量，添加到WHERE子句
            if (!(subTerms[i] instanceof Variable)) {
                whereClauses.add(termName + ".name = '" + subTerms[i] + "'");
            }
        }
    }

    /**
     * 处理Similarity类型的词项
     */
    private static void processSimilarityTerm(CompoundTerm compoundTerm, Term[] subTerms,
                                            StringBuilder cypher, List<String> whereClauses) {
        if (subTerms.length >= 2) {
            String varName1 = getCleanVarName(subTerms[0].toString());
            String varName2 = getCleanVarName(subTerms[1].toString());

            cypher.append("(").append(varName1).append(")-[r_sim:相似]->(")
                  .append(varName2).append("), ");

            // 添加WHERE条件
            if (!(subTerms[0] instanceof Variable)) {
                whereClauses.add(varName1 + ".name = '" + subTerms[0] + "'");
            }
            if (!(subTerms[1] instanceof Variable)) {
                whereClauses.add(varName2 + ".name = '" + subTerms[1] + "'");
            }
        }
    }

    /**
     * 辅助方法，用于清理变量名，去除$符号等特殊字符
     *
     * @param varName 原始变量名
     * @return 清理后的变量名
     */
    private static String getCleanVarName(String varName) {
        if (varName == null) {
            return "";
        }

        // 去掉$符号和其他可能影响Cypher查询的特殊字符
        String cleaned = varName.replaceAll("[\\$#]", "");

        // 如果清理后为空，使用默认名称
        if (cleaned.trim().isEmpty()) {
            return "unknown";
        }

        return cleaned;
    }


}