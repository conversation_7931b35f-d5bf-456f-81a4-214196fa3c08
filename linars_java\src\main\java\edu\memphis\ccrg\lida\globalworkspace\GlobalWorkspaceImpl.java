/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
/**
 * 
 */
package edu.memphis.ccrg.lida.globalworkspace;

import edu.memphis.ccrg.lida.framework.FrameworkModuleImpl;
import edu.memphis.ccrg.lida.framework.ModuleListener;
import edu.memphis.ccrg.lida.framework.initialization.Initializable;
import edu.memphis.ccrg.lida.framework.shared.ElementFactory;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.strategies.DecayStrategy;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTask;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.globalworkspace.triggers.BroadcastTrigger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * The default implementation of {@link GlobalWorkspace} which maintains the collection of
 * {@link Coalition} objects. It supports {@link BroadcastTrigger} tasks that are in charge
 * of triggering the new broadcast. This class maintains a list of
 * {@link BroadcastListener} which are the modules that are registered to receive winning coalitions.
 *
 * 用于维护* {@link Coalition}对象的集合。它支持{@link BroadcastTrigger}任务，这些任务负责触发新的广播。
 * 此类维护* {@link BroadcastListener}的列表，这些列表已注册为接收获胜联盟的模块
 * <AUTHOR> Snaider
 * <AUTHOR> J. McCall
 */
public class GlobalWorkspaceImpl extends FrameworkModuleImpl implements GlobalWorkspace{

	private static final Logger logger = Logger
			.getLogger(GlobalWorkspaceImpl.class.getCanonicalName());
	private static final ElementFactory factory = ElementFactory.getInstance();
	private static final Integer DEFAULT_REFRACTORY_PERIOD = 10; // 不应期 原40
	private static final String DEFAULT_COALITION_DECAY = factory
			.getDefaultDecayType();
	private static final double DEFAULT_COALITION_REMOVAL_THRESHOLD = 0.0;

	private double coalitionRemovalThreshold;
	private DecayStrategy coalitionDecayStrategy;
	private int broadcastRefractoryPeriod;
	private int broadcastsSentCount;
	private long tickAtLastBroadcast;
	private BroadcastTrigger lastBroadcastTrigger;
	private AtomicBoolean broadcastStarted = new AtomicBoolean(false);

	private List<BroadcastListener> broadcastListeners = new ArrayList<BroadcastListener>();
	private List<BroadcastTrigger> broadcastTriggers = new ArrayList<BroadcastTrigger>();
	private Queue<Coalition> coalitions = new ConcurrentLinkedQueue<Coalition>();

	/**
	 * Constructs a new instance with default values
	 */
	public GlobalWorkspaceImpl() {
		super();
	}

	/**
	 * Initializes parameters with the following names and types:<br/>
	 * <br/>
	 * 
	 * <b>globalWorkspace.coalitionRemovalThreshold type=double</b> amount of
	 * activation coalitions must have to remain in the GlobalWorkspace<br/>
	 * <b>globalWorkspace.coalitionDecayStrategy type=string</b> name of the
	 * decay strategy used by all coalitions in the GlobalWorkspace<br/>
	 * <b>globalWorkspace.refractoryPeriod type=int</b> minimum amount of time
	 * allowed between subsequent broadcasts<br/>
	 *
	 * 数量*激活联盟必须保留在GlobalWorkspace中<br/>
	 * **衰减策略的名称由GlobalWorkspace中的所有联盟使用<br/> *
	 * <b> globalWorkspace.refractoryPeriod type = int </ b>后续广播之间允许的最短时间* <br/>
	 * @see Initializable
	 */
	@Override
	public void init() {
		coalitionRemovalThreshold = (Double) getParam(
				"globalWorkspace.coalitionRemovalThreshold",
				DEFAULT_COALITION_REMOVAL_THRESHOLD);

		String coalitionDecayStrategyName = (String) getParam(
				"globalWorkspace.coalitionDecayStrategy",
				DEFAULT_COALITION_DECAY);
		coalitionDecayStrategy = factory
				.getDecayStrategy(coalitionDecayStrategyName);
		if (coalitionDecayStrategy == null) {
			coalitionDecayStrategy = factory.getDefaultDecayStrategy();
			logger.log(Level.WARNING,
					"failed to obtain decay strategy {0}, using default",
					coalitionDecayStrategyName);
		}

		int refractoryPeriod = (Integer) getParam(
				"globalWorkspace.refractoryPeriod", DEFAULT_REFRACTORY_PERIOD);
		setRefractoryPeriod(refractoryPeriod);

		taskSpawner.addTask(new StartTriggersTask());
	}

    private class StartTriggersTask extends FrameworkTaskImpl {
        public StartTriggersTask() {
            super(1);
        }

        @Override
        protected void runThisFrameworkTask() {
            for (BroadcastTrigger t : broadcastTriggers) {
                t.start();
            }
            cancel();
        }
    }

    @Override
    public void addListener(ModuleListener listener) {
        if (listener instanceof BroadcastListener) {
            addBroadcastListener((BroadcastListener) listener);
        } else {
            logger.log(Level.WARNING,
                    "Can only add listeners of type BroadcastListener. Tried to add {1}", new Object[]{TaskManager.getCurrentTick(), listener});
        }
    }
    
    @Override
    public void addBroadcastListener(BroadcastListener bl) {
        broadcastListeners.add(bl);
    }

    @Override
    public void addBroadcastTrigger(BroadcastTrigger t) {
        broadcastTriggers.add(t);
    }

	@Override
	public boolean addCoalition(Coalition coalition) {
		coalition.setDecayStrategy(coalitionDecayStrategy);
		coalition.setActivatibleRemovalThreshold(coalitionRemovalThreshold);
		// 多线程 交互汇总？
		if (coalitions.add(coalition)) {

//			Thread t = Thread.currentThread();
//			System.out.println("addCoalition 进入线程-------》" + t.getName());
//			System.out.println("coalitions.add(c)--------------;" + coalitions.size());

			logger.log(Level.FINEST, "New Coalition added with activation {1}",
					new Object[] { TaskManager.getCurrentTick(),
							coalition.getActivation() });
			newCoalitionEvent();
			return true;
		} else {
			System.out.println("add coalitions 出错-----" + coalition.getContent());
			return false;
		}
	}

    private void newCoalitionEvent() {
        for (BroadcastTrigger trigger : broadcastTriggers) {
            trigger.checkForTriggerCondition(coalitions);
        }
    }

    @Override
    public void triggerBroadcast(BroadcastTrigger trigger) {
//		System.out.println("triggerBroadcast开始------------------");
		// 确保原子性，只有一个进程运行，compareAndSet 保证原子性并发
		// 从始至终都是set（false），进行中的状态为true，true则不能让其他线程进入，直到再次设置false，非进行态
        if (broadcastStarted.compareAndSet(false, true)) {

//			System.out.println("triggerBroadcast 进入线程-------》" + Thread.currentThread().getName());

        	// 不应期，不广播。全局性，不是单个神经元
            if(TaskManager.getCurrentTick() - tickAtLastBroadcast < broadcastRefractoryPeriod){
                broadcastStarted.set(false);
//				System.out.println("triggerBroadcast 进入线程0000-------》" + Thread.currentThread().getName());
                return;
            }
//			System.out.println("triggerBroadcast 进入线程1111111-------》" + Thread.currentThread().getName());

            boolean broadcastWasSent = sendBroadcast();
            if (broadcastWasSent) {
                lastBroadcastTrigger = trigger;
            }
//			t = Thread.currentThread();
//			System.out.println("triggerBroadcast 退出线程222222-------》" + t.getName());
        }
    }

	/**
	 * This method realizes the broadcast. First it chooses the winner
	 * coalition. Then, all registered {@link BroadcastListener}s receive a
	 * reference to the winning Coalition. A new task is created to send the
	 * Coalition to each listener. The winning Coalition is removed from the
	 * pool. This method is supposed to be called from {@link
	 * BroadcastTrigger}s. The reset()method is invoked on each trigger at the
	 * end of this method.
	 *
	 * 该方法实现了广播。首先，它选择获胜者*联盟。然后，所有已注册的{@link BroadcastListener}都会收到*
	 * 对获胜联盟的参考。创建一个新任务以将*联盟发送给每个侦听器。获胜的联盟将从*池中删除。
	 * 应该从{@link * BroadcastTrigger} s中调用此方法。在此方法的*端，每个触发器都会调用reset（）方法
	 */
	//todo 内隐过程过长时，通达广播节律需优化，可能无需广播，工作记忆=意识通达，学习可能需要广播，20230406
	private boolean sendBroadcast() {
//		System.out.println("Triggering broadcast-----开始");
		logger.log(Level.FINEST, "Triggering broadcast", TaskManager
				.getCurrentTick());
		boolean broadcastWasSent = false;
//		System.out.println("Triggering broadcast-----chooseCoalition开始");

		// 筛选应该是注意力阶段，筛选后整合包全部通过，这里暂时当做重复的精简，20201010
		Coalition winningCoalition = chooseCoalition(0);

//		System.out.println("Triggering broadcast-----chooseCoalition 完成");

		// 不应期的问题，广播不应期比探测报告不应期小，广播没有内容
		if (winningCoalition == null) {
			System.out.println("coal空，停止广播");
			broadcastStarted.set(false);
			return broadcastWasSent;
		}

//		System.out.println("broadcast----------------------------------------");

		NodeStructure ns = (NodeStructure) winningCoalition.getContent();
//		System.out.println("Triggering broadcast-----getContent 完成");
		if (winningCoalition != null) {

			coalitions.remove(winningCoalition);

			broadcastsSentCount ++;
			ns.setBroadSceneCount(broadcastsSentCount);

			for(Node node: ns.getNodes()){
				// 设置当前周期
				node.setBcastid(String.valueOf(broadcastsSentCount));
				// 更新虚实状态，留存而非激活的，遇实改虚，虚则不改
				if (node.getLastAct() != null && broadcastsSentCount > Integer.valueOf(node.getLastAct())){
					int nodetruth = node.getTruth();
					if(nodetruth == 1){
						node.setTruth(2);
					}else if(nodetruth == 3 || nodetruth == 5){
						node.setTruth(4);
					}
				}
			}

			// 需要一个个绑定多个线程，然后由多线程执行，多线程观察者模式？
			for (BroadcastListener bl : broadcastListeners) {
				FrameworkTask broadcastTask = new SendBroadcastTask(bl,
						winningCoalition);
				taskSpawner.addTask(broadcastTask);
			}

			logger.log(Level.FINEST, "Broadcast Performed at tick: {0}",
					TaskManager.getCurrentTick());

			tickAtLastBroadcast = TaskManager.getCurrentTick();
			broadcastWasSent = true;
//			System.out.println("Triggering broadcast-----完成");
		} else {
			logger.log(Level.FINEST,
					"Broadcast triggered but there are no Coalitions",
					TaskManager.getCurrentTick());
		}
		resetTriggers();

//		Thread t = Thread.currentThread();
//		System.out.println("sendBroadcast 进入线程-------》" + t.getName());
//		System.out.println("Triggering broadcast-----重置");

		broadcastStarted.set(false);

		return broadcastWasSent;

	}

	private class SendBroadcastTask extends FrameworkTaskImpl {
		private BroadcastListener listener;
		private Coalition coalition;

		public SendBroadcastTask(BroadcastListener bl, Coalition c) {
			listener = bl;
			coalition = c;
		}

		@Override
		protected void runThisFrameworkTask() {
			try {
				listener.receiveBroadcast(coalition);
//				System.out.println("-----------broadcastStartedt--------" + broadcastStarted);
			} catch (Exception e) {
				e.printStackTrace();
			}
			cancel();
		}
	}

	private int sum = 0;

	// 动作选择前，要精简场景，以场景预测动作
	// 联想过程中不好精简，可能错误放行或扼杀，整合成联盟后方便精简
	// 联盟精简=抑制非显著项+保留显著场景，单位点边数值运算筛选，两者并行？
	private Coalition chooseCoalition(int sum) {
        Coalition chosenCoal = null;

//		if (coalitions.size() == 0) {
////			chosenCoal = new CoalitionImpl();
//			System.out.println("coalitions空---0000");
//		}

//		Thread t = Thread.currentThread();
//		System.out.println("chooseCoalition 进入线程-------》" + t.getName());

		if (sum >= 20) {
			System.out.println("sum > 20 --------------------------------");
			return null;
		}

        for (Coalition c : coalitions) {

//			if (chosenCoal == null){
//				chosenCoal = c;
//				System.out.println("Triggering broadcast-----chooseCoalition空");
//			}else if(c.getActivation() > chosenCoal.getActivation()){
//				chosenCoal = c;
//				System.out.println("Triggering broadcast-----chooseCoalition大");
//			}else {
//				System.out.println("Triggering broadcast-----chooseCoalition非空");
//			}

            if (chosenCoal == null
					|| c.getActivation() > chosenCoal.getActivation()) {
//				System.out.println("Triggering broadcast-----chooseCoalition进行");
                chosenCoal = c;
            }

        }

		if (coalitions.size() == 0) {
			sum ++;
//			chosenCoal = new CoalitionImpl();
//			System.out.println("coalitions空---111111------" + sum);
			chosenCoal = chooseCoalition(sum);
		}else if(chosenCoal == null){
//			chosenCoal = new CoalitionImpl();
			System.out.println("chosenCoal空");
			chosenCoal = chooseCoalition(sum);
		}

		sum = 0;	// 成功则初始化

        return chosenCoal;
    }

    private void resetTriggers() {
        for (BroadcastTrigger t : broadcastTriggers) {
            t.reset();
        }
    }

    @Override
    public Object getModuleContent(Object... params) {
        if (params.length > 0) {
            if ("lastBroadcastTrigger".equals(params[0])) {
                return lastBroadcastTrigger;
            } else if ("coalitions".equals(params[0])) {
                return Collections.unmodifiableCollection(coalitions);
            }
        }
        return Collections.unmodifiableCollection(coalitions);
    }

    @Override
    public void decayModule(long ticks) {
        decay(ticks);
        logger.log(Level.FINEST, "Coalitions Decayed",
                TaskManager.getCurrentTick());
    }

    private void decay(long ticks) {
        for (Coalition c : coalitions) {
            c.decay(ticks);
            if (c.isRemovable()) {
                coalitions.remove(c);
//                System.out.println("coalitions.remove(c);" + coalitions.size());
                logger.log(Level.FINEST, "Coalition removed",
                        TaskManager.getCurrentTick());
            }
        }
    }

    @Override
    public long getBroadcastSentCount() {
        return broadcastsSentCount;
    }

    /**
     * Gets refractoryPeriod
     * @return number of ticks that must pass after a broadcast has been sent before
     * a new one can be sent.
     */
    @Override
    public int getRefractoryPeriod() {
        return broadcastRefractoryPeriod;
    }
    

	@Override
    public long getTickAtLastBroadcast() {
        return tickAtLastBroadcast;
    }

    /**
	 * Sets refractoryPeriod
	 * 
	 * @param period number of ticks that must pass after a broadcast has been
	 * sent before a new one can be sent.
	 */
    @Override
    public void setRefractoryPeriod(int period) {
    	if (period > 0) {
    		broadcastRefractoryPeriod = period;
    	}else{
    		broadcastRefractoryPeriod = DEFAULT_REFRACTORY_PERIOD;
    		logger.log(Level.WARNING,
    				"refractory period must be positive, using default value",
    				TaskManager.getCurrentTick());
    	}
    }

    @Override
	public double getCoalitionRemovalThreshold() {
		return coalitionRemovalThreshold;
	}

	@Override
	public void setCoalitionRemovalThreshold(double coalitionRemovalThreshold) {
		this.coalitionRemovalThreshold = coalitionRemovalThreshold;
	}

	@Override
	public DecayStrategy getCoalitionDecayStrategy() {
		return coalitionDecayStrategy;
	}

	@Override
	public void setCoalitionDecayStrategy(DecayStrategy coalitionDecayStrategy) {
		this.coalitionDecayStrategy = coalitionDecayStrategy;
	}

}
