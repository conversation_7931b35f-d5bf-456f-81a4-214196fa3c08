/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.control.concept;

import java.util.*;

import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.pam.PamNodeImpl;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
import org.opennars.control.DerivationContext;
import org.opennars.entity.BudgetValue;
import edu.memphis.ccrg.linars.Concept;
import org.opennars.entity.Sentence;
import org.opennars.entity.Stamp;
import org.opennars.entity.Task;
import org.opennars.entity.TaskLink;
import org.opennars.entity.TermLink;
import org.opennars.entity.TruthValue;
import org.opennars.inference.RuleTables;
import org.opennars.inference.TemporalRules;
import org.opennars.interfaces.Timable;
import org.opennars.io.Narsese;
import org.opennars.io.Parser;
import org.opennars.io.Symbols;
import org.opennars.io.events.OutputHandler;
import org.opennars.language.*;
import org.opennars.main.Nar;
import org.opennars.main.Parameters;
import org.opennars.operator.Operator;
import org.opennars.operator.mental.Anticipate;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.narsese;
import static org.opennars.inference.UtilityFunctions.c2w;
import static org.opennars.inference.UtilityFunctions.w2c;

/**
 *
 * <AUTHOR> Hammer
 */
public class ProcessAnticipation {
//    public static Narsese narsese = new Narsese(nar);
    public static void anticipate(final DerivationContext nal, final Sentence mainSentence, final BudgetValue budget, 
            final long mintime, final long maxtime, final float urgency, Map<Term,Term> substitution) {
        //derivation was successful and it was a judgment event
        final Stamp stamp = new Stamp(nal.time, nar.memory);
        stamp.setOccurrenceTime(Stamp.ETERNAL);
        float eternalized_induction_confidence = nar.narParameters.ANTICIPATION_CONFIDENCE;
        final Sentence s = new Sentence(
            mainSentence.term,
            mainSentence.punctuation,
            new TruthValue(0.0f, eternalized_induction_confidence, nar.narParameters),
            stamp);
        //Budget for one-time processing
        final Task t = new Task(s, new BudgetValue(0.99f,0.1f,0.1f, nar.narParameters), Task.EnumType.DERIVED);
        Term predicate = ((Statement) mainSentence.term).getPredicate();
        Term specificAnticipationTerm = null;
        if(predicate instanceof CompoundTerm) {
            specificAnticipationTerm = ((CompoundTerm)predicate).applySubstitute(substitution);
        } else if (predicate instanceof PamNodeImpl) {
            try {
                Term term = narsese.parseTerm(predicate.toString());
                specificAnticipationTerm = ((CompoundTerm)term).applySubstitute(substitution);
            } catch (Parser.InvalidInputException e) {
                throw new RuntimeException(e);
            }
        }else {
            specificAnticipationTerm = predicate;
        }
        //put into consequence concept
        final Concept c = nar.memory.concept(specificAnticipationTerm);
        if(c != null /*&& mintime > nar.memory.time()*/ && c.observable && (mainSentence.getTerm() instanceof Implication || mainSentence.getTerm() instanceof Equivalence) && 
                mainSentence.getTerm().getTemporalOrder() == TemporalRules.ORDER_FORWARD) {
            Concept.AnticipationEntry toDelete = null;
            Concept.AnticipationEntry toInsert = new Concept.AnticipationEntry(urgency, t, mintime, maxtime);
            boolean fullCapacity = c.anticipations.size() >= nar.narParameters.ANTICIPATIONS_PER_CONCEPT_MAX;
            // choose an element to replace with the new, in case that we are already at full capacity
            // 选择一个元素来替换新元素，以防我们已经达到最大容量
            if(fullCapacity) {
                for(Concept.AnticipationEntry entry : c.anticipations) {
                    if(urgency > entry.negConfirmationPriority /*|| t.getPriority() > c.negConfirmation.getPriority() */) {
                        // prefer to replace one that is more far in the future, takes longer to be disappointed about
                        // 更喜欢替换一个更远的未来，需要更长的时间才能失望
                        if(toDelete == null || entry.negConfirm_abort_maxtime > toDelete.negConfirm_abort_maxtime) {
                            toDelete = entry;
                        }
                    }
                }
            }
            // we were at full capacity but there was no item that can be replaced with the new one
            // 我们已经满了，但是没有项目可以用新的替换
            if(fullCapacity && toDelete == null) {
                return;
            }
            if(toDelete != null) {
                c.anticipations.remove(toDelete);
            }
            c.anticipations.add(toInsert);
            final Statement impOrEqu = (Statement) toInsert.negConfirmation.sentence.term;
            final Concept ctarget = nar.memory.concept(impOrEqu.getPredicate());
            if(ctarget != null) {
                Operator anticipate_op = ((Anticipate)c.memory.getOperator("^anticipate"));
                if(anticipate_op != null && anticipate_op instanceof Anticipate) {
                    ((Anticipate)anticipate_op).anticipationFeedback(impOrEqu.getPredicate(), null, c.memory, nal.time);
                }
            }
            //disappoint/confirm printed anyway
            nar.memory.emit(OutputHandler.ANTICIPATE.class, specificAnticipationTerm);
        }
    }

    /**
     * Process outdated anticipations within the concept,these which are outdated generate negative feedback
     * 处理概念中过时的预期，这些过时的预期会产生负反馈
     * @param narParameters The reasoner parameters
     * @param concept The concept which potentially outdated anticipations should be processed
     * @param nar the reasoner
     */
    public static void maintainDisappointedAnticipations(final Parameters narParameters, final Concept concept, final Nar nar, Memory memory) {
        // here we can check the expiration of the feedback:
        // 这里我们可以检查反馈的过期时间：
        List<Concept.AnticipationEntry> confirmed = new ArrayList<>();
        List<Concept.AnticipationEntry> disappointed = new ArrayList<>();
        for(Concept.AnticipationEntry entry : concept.anticipations) {
            if(entry.negConfirmation == null || nar.time() <= entry.negConfirm_abort_maxtime) {
                continue;
            }
            //at first search beliefs for input tasks:
            //首先搜索输入任务的信念：
            boolean gotConfirmed = false;
            if(narParameters.RETROSPECTIVE_ANTICIPATIONS) {
                //search for input in tasklinks (beliefs alone can not take temporality into account as the eternals will win)
                //在任务链接中搜索输入（仅信念无法考虑时间性，因为永恒的将获胜）
                for(final TaskLink tl : concept.taskLinks) {
                    final Task t = tl.targetTask;
                    if(t!= null && t.sentence.isJudgment() && /*t.isInput() &&*/ !t.sentence.isEternal() && t.sentence.truth.getExpectation() > AgentStarter.nar.narParameters.DEFAULT_CONFIRMATION_EXPECTATION &&
                            CompoundTerm.replaceIntervals(t.sentence.term).equals(CompoundTerm.replaceIntervals(concept.getTerm()))) {
                        if(t.sentence.getOccurenceTime() >= entry.negConfirm_abort_mintime && t.sentence.getOccurenceTime() <= entry.negConfirm_abort_maxtime) {
                            confirmed.add(entry);
                            gotConfirmed = true;
                            break;
                        }
                    }
                }
            }
            if(!gotConfirmed) {
                disappointed.add(entry);
            }
        }
        //confirmed by input, nothing to do
        if(confirmed.size() > 0) {
            concept.memory.emit(OutputHandler.CONFIRM.class,concept.getTerm());
        }
        concept.anticipations.removeAll(confirmed);
        //not confirmed and time is out, generate disappointment
        //没有确认，时间到了，产生失望
        if(disappointed.size() > 0) {
            concept.memory.emit(OutputHandler.DISAPPOINT.class,concept.getTerm());
        }
        for(Concept.AnticipationEntry entry : disappointed) {
            final Term term = entry.negConfirmation.getTerm();
            final Term termWithRplacedIntervals = CompoundTerm.replaceIntervals(term);

            { // revise with negative evidence
                TruthValue truthOfBeliefWithTerm = null;
                {
                    final Concept targetConcept = memory.concept(termWithRplacedIntervals);
                    if (targetConcept == null) { // target concept does not exist
                        continue;
                    }

                    synchronized (targetConcept) {
                        for( final Task iBeliefTask : targetConcept.beliefs ) {
                            Term iBeliefTerm = iBeliefTask.getTerm();

                            boolean found = iBeliefTerm.equals(term);
                            if (found) {
                                truthOfBeliefWithTerm = iBeliefTask.sentence.truth;
                                break;
                            }
                        }
                    }
                }

                if(truthOfBeliefWithTerm != null) {
                    // compute amount of negative evidence based on current evidence
                    // we just take the counter and don't add one because we want to compute a w "unit" which will be revised
                    // 基于当前证据计算负面证据的数量，我们只需要计数器，不添加一个，因为我们想计算一个w“单位”，它将被修订
                    long countWithNegativeEvidence = ((Implication)term).counter;
                    double negativeEvidenceRatio = 1.0 / (double) countWithNegativeEvidence;

                    // compute confidence by negative evidence
                    double w = c2w(truthOfBeliefWithTerm.getConfidence(), narParameters);
                    w *= negativeEvidenceRatio;
                    double c = w2c((float) w, narParameters);

                    final TruthValue truth = new TruthValue(0.0f, c, narParameters); // frequency of negative confirmation is 0.0

                    final Sentence sentenceForNewTask = new Sentence(
                        term,
                        Symbols.JUDGMENT_MARK,
                        truth,
                        new Stamp(nar, memory, Tense.Eternal));
                    final BudgetValue budget = new BudgetValue(0.99f, 0.1f, 0.1f, AgentStarter.nar.narParameters);
                    final Task t = new Task(sentenceForNewTask, budget, Task.EnumType.DERIVED);

                    concept.memory.inputTask(nar, t, false);
                }
            }

            concept.anticipations.remove(entry);
        }
    }
    
    /**
     * Whether a processed judgement task satisfies the anticipations within concept
     * 处理的判断任务是否满足概念内的预期
     * @param task The judgement task be checked
     * @param concept The concept that is processed
     * @param nal The derivation context
     */
    public static void confirmAnticipation(Task task, Concept concept, final DerivationContext nal) {
        final boolean satisfiesAnticipation = task.isInput() && !task.sentence.isEternal();
        final boolean isExpectationAboveThreshold = task.sentence.truth.getExpectation() > nar.narParameters.DEFAULT_CONFIRMATION_EXPECTATION;
        List<Concept.AnticipationEntry> confirmed = new ArrayList<>();
//        synchronized (concept.anticipations) {
            for (Concept.AnticipationEntry entry : concept.anticipations) {
                if (satisfiesAnticipation && isExpectationAboveThreshold && task.sentence.getOccurenceTime() >= entry.negConfirm_abort_mintime && task.sentence.getOccurenceTime() <= entry.negConfirm_abort_maxtime) {
                    confirmed.add(entry);
                }
            }
            if (confirmed.size() > 0) {
                nar.memory.emit(OutputHandler.CONFIRM.class, concept.getTerm());
            }
            concept.anticipations.removeAll(confirmed);
//        }
    }
    
    /**
     * Fire predictictive inference based on beliefs that are known to the concept's neighbours
     * 基于已知于概念邻居的信念，触发预测性推理
     * @param judgementTask judgement task
     * @param concept concept that is processed
     * @param nal derivation context
     * @param time used to retrieve current time
     * @param tasklink coresponding tasklink
     */
    public static void firePredictions(final Task judgementTask, final Concept concept, final DerivationContext nal, Timable time, TaskLink tasklink) {
        if(!judgementTask.sentence.isEternal() && judgementTask.isInput() && judgementTask.sentence.isJudgment()) {
//            synchronized (concept) {
                for (TermLink tl : concept.termLinks) {
                    Term term = tl.getTarget();
                    Concept tc = nar.memory.concept(term);
                    if (tc != null && !tc.beliefs.isEmpty() && term instanceof Implication) {
                        // 限定为正向时序的推理
                        Implication imp = (Implication) term;
                        if (imp.getTemporalOrder() == TemporalRules.ORDER_FORWARD) {
                            Term precon = imp.getSubject();
                            Term component = precon;
                            if (precon instanceof Conjunction) {
                                Conjunction conj = (Conjunction) imp.getSubject();
                                if (conj.getTemporalOrder() == TemporalRules.ORDER_FORWARD && conj.term.length == 2 && conj.term[1] instanceof Interval) {
                                    component = conj.term[0]; //(&/,a,+i), so use a
                                }
                            }
                            if (CompoundTerm.replaceIntervals(concept.getTerm()).equals(CompoundTerm.replaceIntervals(component))) {
                                //trigger inference of the task with the belief
                                //触发任务与信念的推理
                                DerivationContext cont = new DerivationContext(nar.memory, nar.narParameters, time);
                                cont.setCurrentTask(judgementTask);     //  a
                                cont.setCurrentBeliefLink(tl);          //  a =/> b
                                cont.setCurrentTaskLink(tasklink);      //  a
                                cont.setCurrentConcept(concept);        //  a
                                cont.setCurrentTerm(concept.getTerm()); //  a
                                RuleTables.reason(tasklink, tl, cont);  //  generate b
                            }
                        }
                    }
                }
//            }
        }
    }
}
