/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamLink;
import edu.memphis.ccrg.linars.Memory;
import org.opennars.control.DerivationContext;
import org.opennars.control.InferenceControl;
import org.opennars.entity.Task;
import org.opennars.entity.TermLink;
import org.opennars.inference.RuleTables;
import org.opennars.main.Nar;

/**
 *
 */
public class ReasonTask extends FrameworkTaskImpl {
	private PAMemory pam;
	private Link link;
	private Task task;
	private Memory mem;
	private DerivationContext nal;
	private TermLink termLink;

	public ReasonTask(TermLink termLink, final DerivationContext nal) {
		super(1, "tact");
		this.mem = mem;
		this.nal = nal;
		this.termLink = termLink;
	}

	public ReasonTask() {
		super(1, "tact");
	}

	@Override
	protected void runThisFrameworkTask() {
		RuleTables.reason(nal.currentTaskLink, termLink, nal);
		cancel();
	}
	
}

