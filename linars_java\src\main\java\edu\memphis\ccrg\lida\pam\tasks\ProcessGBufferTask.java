/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamLink;
import edu.memphis.ccrg.linars.Concept;
import edu.memphis.ccrg.linars.Memory;
import org.opennars.entity.Task;
import org.opennars.interfaces.Timable;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;

/**
 * A task to add a {@link PamLink} and its sink to the percept.
 * 
 * <AUTHOR> J. McCall
 * @see ExcitationTask creates this task
 * @see PropagationTask creates this task
 */
public class ProcessGBufferTask extends FrameworkTaskImpl {
	private PAMemory pam;
	private Link link;
	private Task task;
	private Memory mem;
	private Timable time;
	private String target;

	/**
	 * Default constructor
	 * param link {@link PamLink}
	 * param pam {@link PAMemory}
	 */
//	public TaskProcessTask(Task task, PAMemory pam, Memory mem, Timable time) {
//		super(1, "tact");
//		this.pam = pam;
////		this.link = link;
//		this.task = task;
//		this.mem = mem;
//		this.time = time;
//	}

//	public ProcessGBufferTask(PAMemory pam, Memory mem, Timable time) {
	public ProcessGBufferTask(PAMemory pam, Memory mem, String target) {
		super(1, "tact");
		this.pam = pam;
//		this.link = link;
//		this.task = task;
		this.mem = mem;
//		this.time = time;
		this.target = target;
	}

	public ProcessGBufferTask() {
		super(1, "tact");
	}

	/**
	 * Adds link's sink to the percept and tries to add the link as well then finishes.
	 */
	@Override
	protected void runThisFrameworkTask() {
		final Task task = mem.globalBuffer.takeOut();
		if (task != null) {
			if(!task.processed) {
				task.processed = true;
				switch (target){
					case "belief":
						// 目标相关的相似、蕴含等也会处理
						if (task.sentence.isGoal()) {
							break;
						}
					// 在动机线程处理，或者在这直接处理，20240527，改即时处理，有关键步骤：概念化
					case "goal":
//						if (!task.sentence.isGoal()) {
//							break;
//						}
					case "both":
					default:
//						if(task.sentence.term.toString().contains("$")){
//							System.out.println("----pgb----相似--变量-----：" + task.toString());
//						}
						// 20240528 只处理顺承
//						if (task.sentence.isGoal()){
//							break;
//						}
						mem.localInference(task, mem.narParameters, nar, mem);
						break;
				}

//				if(task.sentence.term.toString().contains("ft_不执行")){
//					System.out.println("----" + task.sentence.term.toString() + "----");
//				}
				// 统计执行时间
//				long startTime = System.currentTimeMillis();
//				System.out.println("执行任务：" + task.sentence.term.toString());

//				mem.localInference(task, mem.narParameters, time);

//				long endTime = System.currentTimeMillis();
//				System.out.println("执行任务时间：" + (endTime - startTime) + "ms" + " ---- 任务：" + task.toString());
			}
			// 20240528 注释掉
//			mem.globalBuffer.putBack(task, mem.narParameters.GLOBAL_BUFFER_FORGET_DURATIONS, mem);
		}
		cancel();
	}
	
}

