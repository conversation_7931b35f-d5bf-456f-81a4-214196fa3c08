package schema.model.edges;

import schema.model.Edge;
import schema.model.Node;

/**
 * 循环边，表示循环结构
 */
public class LoopEdge extends Edge {
    private String condition; // 循环条件

    public LoopEdge(String id, Node source, Node target, String condition) {
        super(id, source, target, "循环条件");
        this.condition = condition;
        setProperty("condition", condition);
    }

    public String getCondition() {
        return condition;
    }
}
