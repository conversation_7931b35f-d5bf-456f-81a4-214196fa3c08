/*
 * Created by JFormDesigner on Sat Dec 21 01:41:21 CST 2019
 */

package edu.memphis.ccrg.lida.framework.gui.panels;

import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.shared.activation.Learnable;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PAMemory;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartPanel;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.axis.NumberAxis;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.chart.plot.XYPlot;
import org.jfree.data.xy.XYSeries;
import org.jfree.data.xy.XYSeriesCollection;

import javax.swing.*;
import java.awt.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * A {@link GuiPanel} which displays a {@link Learnable} element's activation over time (in ticks).
 * <AUTHOR>
 */
public class ActivationChartPanel extends GuiPanelImpl {
    private static final Logger logger = Logger.getLogger(ActivationChartPanel.class.getCanonicalName());
    private JFreeChart chart;
    private XYSeriesCollection dataset = new XYSeriesCollection();
    private XYSeries series1 = new XYSeries("Base-level Activation");
    private XYSeries series2 = new XYSeries("Current Activation");
    private XYSeries series3 = new XYSeries("Total Activation");
    private int tickDisplayInterval = 100;
    private FrameworkModule selectedModule;
    private Learnable learnable;
    private String elementName = "";

    /** Creates new form JChartGuiPanel */
    public ActivationChartPanel() {
        chart = ChartFactory.createXYLineChart("", "Tick", "Activation",
                dataset, PlotOrientation.VERTICAL, true, true, false);
        chart.setBackgroundPaint(new Color(238,233,233));
        XYPlot plot = (XYPlot) chart.getPlot();
        plot.setBackgroundPaint(Color.WHITE);
        plot.setDomainGridlinePaint(Color.LIGHT_GRAY);
        plot.setRangeGridlinePaint(Color.LIGHT_GRAY);
        NumberAxis domainAxis = (NumberAxis) plot.getDomainAxis();
        domainAxis.setStandardTickUnits(NumberAxis.createIntegerTickUnits());

        initComponents();
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents
        toolBar = new JToolBar();
        jLabel1 = new JLabel();
        moduleComboBox = new JComboBox<>();
        jLabel2 = new JLabel();
        elementNameTextField = new JTextField();
        displayButton = new JButton();
        mainPanel = new ChartPanel(chart);

        //======== this ========
        setMinimumSize(new Dimension(250, 34));
        setPreferredSize(new Dimension(250, 200));

        //======== toolBar ========
        {
            toolBar.setRollover(true);

            //---- jLabel1 ----
            jLabel1.setText("Module ");
            toolBar.add(jLabel1);

            //---- moduleComboBox ----
            moduleComboBox.setModel(new DefaultComboBoxModel<>(new String[] {

            }));
            moduleComboBox.setPreferredSize(new Dimension(200, 22));
            moduleComboBox.addActionListener(e -> moduleComboBoxActionPerformed(e));
            toolBar.add(moduleComboBox);
            toolBar.addSeparator();

            //---- jLabel2 ----
            jLabel2.setText(" Element Name ");
            toolBar.add(jLabel2);

            //---- elementNameTextField ----
            elementNameTextField.setMaximumSize(new Dimension(100, 22));
            elementNameTextField.setPreferredSize(new Dimension(90, 22));
            toolBar.add(elementNameTextField);

            //---- displayButton ----
            displayButton.setFont(new Font("Tahoma", Font.BOLD, 11));
            displayButton.setText("Plot Activation");
            displayButton.setFocusable(false);
            displayButton.setHorizontalTextPosition(SwingConstants.CENTER);
            displayButton.setVerticalTextPosition(SwingConstants.BOTTOM);
            displayButton.addActionListener(e -> displayButtonActionPerformed(e));
            toolBar.add(displayButton);
        }

        //======== mainPanel ========
        {

            GroupLayout mainPanelLayout = new GroupLayout(mainPanel);
            mainPanel.setLayout(mainPanelLayout);
            mainPanelLayout.setHorizontalGroup(
                mainPanelLayout.createParallelGroup()
                    .addGap(0, 450, Short.MAX_VALUE)
            );
            mainPanelLayout.setVerticalGroup(
                mainPanelLayout.createParallelGroup()
                    .addGap(0, 267, Short.MAX_VALUE)
            );
        }

        GroupLayout layout = new GroupLayout(this);
        setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createParallelGroup()
                    .addComponent(mainPanel, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
                .addComponent(toolBar, GroupLayout.DEFAULT_SIZE, 450, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createParallelGroup()
                    .addGroup(layout.createSequentialGroup()
                        .addGap(33, 33, 33)
                        .addComponent(mainPanel, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)))
                .addGroup(layout.createSequentialGroup()
                    .addComponent(toolBar, GroupLayout.PREFERRED_SIZE, 28, GroupLayout.PREFERRED_SIZE)
                    .addContainerGap(272, Short.MAX_VALUE))
        );
        // JFormDesigner - End of component initialization  //GEN-END:initComponents
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables
    private JToolBar toolBar;
    private JLabel jLabel1;
    private JComboBox<String> moduleComboBox;
    private JLabel jLabel2;
    private JTextField elementNameTextField;
    private JButton displayButton;
    private JPanel mainPanel;
    // JFormDesigner - End of variables declaration  //GEN-END:variables
    private void moduleComboBoxActionPerformed(java.awt.event.ActionEvent evt) {
        String selection = moduleComboBox.getSelectedItem().toString();
        FrameworkModule newModuleSelection = agent.getSubmodule(selection);
        if(newModuleSelection != null){
            selectedModule = newModuleSelection;
            refresh();
        }else{
            logger.log(Level.WARNING, "cannot retriveve module with name {1}",
                    new Object[]{TaskManager.getCurrentTick(), selection});
        }

    }

    private void displayButtonActionPerformed(java.awt.event.ActionEvent evt) {
        elementName = elementNameTextField.getText().trim();
        refresh();
    }

    /**
     * first optional parameter sets tickDisplayInterval (int)
     *
     */
    @Override
    public void initPanel(String[] params){
        //TODO additional module support: ProceduralMemory, ActionSelection?, CodeletModules
        selectedModule = agent.getSubmodule(ModuleName.PerceptualAssociativeMemory);
        moduleComboBox.addItem(String.valueOf(selectedModule));

        //order affects color of series, TODO find out how to choose colors
        dataset.addSeries(series3);
        dataset.addSeries(series2);
        dataset.addSeries(series1);

        if(params!=null && params.length > 0){
            try{
                tickDisplayInterval = Integer.parseInt(params[0].trim());
            }catch(NumberFormatException e){
                logger.log(Level.WARNING, "unable to set tick display interval {0}", params[0].trim());
            }

            if(params.length > 1){
                elementName = params[1].trim();
                elementNameTextField.setText(elementName);
                refresh();
            }
        }

    }

    @Override
    public void refresh() {
        ModuleName selectedModuleName = selectedModule.getModuleName();
        if(selectedModuleName == ModuleName.PerceptualAssociativeMemory){
            learnable = (Learnable) ((PAMemory) selectedModule).getNode(elementName);
        }else{
            learnable = null;
        }
        updateWithLearnable(learnable);
    }

    private void updateWithLearnable(Learnable learnable){
        if(learnable != null){
            //Remove oldest if display interval is reached
            if(series2.getItemCount() >= tickDisplayInterval){
                series1.remove(0);
                series2.remove(0);
                series3.remove(0);
            }
            //add a new x,y entry to each of the 3 series
            long currentTick = TaskManager.getCurrentTick();
            series1.add(currentTick, learnable.getBaseLevelActivation());
            series2.add(currentTick, learnable.getActivation());
            series3.add(currentTick, learnable.getTotalActivation());
        }else{
            logger.log(Level.FINEST, "null learnable", TaskManager.getCurrentTick());
        }
    }
}
