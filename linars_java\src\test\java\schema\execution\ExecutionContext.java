package schema.execution;

import java.util.HashMap;
import java.util.Map;

/**
 * 执行上下文，维护执行过程中的变量和状态
 */
public class ExecutionContext {
    private Map<String, Object> variables = new HashMap<>();
    private Map<String, Object> globalVarCache = new HashMap<>();
    private Map<String, ExecutionMode> componentModes = new HashMap<>();
    private ExecutionMode defaultMode = ExecutionMode.PRECISE;

    public ExecutionContext() {
    }

    public void setVariable(String name, Object value) {
        variables.put(name, value);
    }

    public Object getVariable(String name) {
        if (variables.containsKey(name)) {
            return variables.get(name);
        }
        return null;
    }

    public Map<String, Object> getVariables() {
        return variables;
    }

    public void cacheVariable(String name, Object value) {
        globalVarCache.put(name, value);
    }

    public Object getCachedVariable(String name) {
        return globalVarCache.get(name);
    }

    public boolean hasCachedVariable(String name) {
        return globalVarCache.containsKey(name);
    }

    public void setComponentMode(String componentId, ExecutionMode mode) {
        componentModes.put(componentId, mode);
    }

    public ExecutionMode getModeForComponent(String componentId) {
        return componentModes.getOrDefault(componentId, defaultMode);
    }

    public void setDefaultMode(ExecutionMode mode) {
        this.defaultMode = mode;
    }

    public ExecutionMode getDefaultMode() {
        return defaultMode;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("ExecutionContext:\n");
        sb.append("Variables: ").append(variables).append("\n");
        sb.append("Global Variable Cache: ").append(globalVarCache).append("\n");
        sb.append("Default Mode: ").append(defaultMode).append("\n");
        return sb.toString();
    }
}
