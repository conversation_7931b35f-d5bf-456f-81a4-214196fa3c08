package schema.execution.operations;

import schema.execution.ExecutionContext;
import schema.execution.Operation;

/**
 * 计算当前位和进位操作，执行加法计算
 */
public class CalculateDigitAndCarryOperation implements Operation {
    @Override
    public Object execute(ExecutionContext context) {
        int digit1 = (int) context.getVariable("位1");
        int digit2 = (int) context.getVariable("位2");
        int carry = (int) context.getVariable("进位");
        
        int sum = digit1 + digit2 + carry;
        int currentDigitResult = sum % 10;
        int newCarry = sum / 10;
        
        context.setVariable("当前位结果", currentDigitResult);
        context.setVariable("进位", newCarry);
        
        return null;
    }
}
