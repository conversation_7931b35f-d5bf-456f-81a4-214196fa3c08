/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.data.TermUtil;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamNode;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Term;
import org.neo4j.graphdb.Relationship;
import org.neo4j.graphdb.Result;
import org.neo4j.graphdb.Transaction;
import org.opennars.io.Parser;

import java.util.Map;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.actmap;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.narsese;


/**
 */
public class CreateSimpleSceneTask extends FrameworkTaskImpl {
	private String sources;
	private String sinks;
	private Node sink;
	private Node source;
	private PAMemory pam;
	private NodeStructure sceneNs;
	private NodeStructure goalNs;

	private Link link;
	private String rootNode;
	private String mark;
	private Node sonHead;
	private String actStamp;
	/**
	 * @param link
	 */

	public CreateSimpleSceneTask(Link link, Node sonHead, String actStamp) {
		super(1, "tact");
		this.link = link;
		this.rootNode = rootNode;
//		this.mark = mark;
		this.sonHead = sonHead;
		this.actStamp = actStamp;
	}

	/**
	 */
	@Override
	protected void runThisFrameworkTask() {
		// 待生成父场景，根场景，单句
//		pam.setSceneMainNode(sink);

//		nar.addInputTo("(^say,{SELF}," + sink.getTNname() + ")! :|:", (Memory) goalNs);

		// 变量句，定义变量并赋值，用具体值实例化，提交到参数表中
//		varTask(sink, sink.getTNname());
		// 用于某些初始化
//		varTask(sink, null);

		// 直接将link的尾节点name改为sonhead，
		String query = "match (n) where n.name = \'" + link.getSink().getTNname() + "\' set n.name = \'" + sonHead.getTNname() + "\' return n";
		Node node = NeoUtil.getNodeCypher(query);

		// 然后新建结构边arg0123等，根据sonhead的name的嵌套结构，构建结构边，如（从，个位数，开始），分别是arg0，arg1，arg2，指向node
		CompoundTerm compoundTerm = null;
		try {
			compoundTerm = (CompoundTerm) narsese.parseTerm(sonHead.getTNname());
		} catch (Parser.InvalidInputException e) {
			throw new RuntimeException(e);
		}
		System.out.println("构建时序中------------SimpleSceneTask");
		createArgsWithNest(compoundTerm, node);

		CreateSuccTask createSuccTask = new CreateSuccTask(link,pam,60, actStamp);
		pam.getAssistingTaskSpawner().addTask(createSuccTask);

		System.out.println("目前任务总数-----------------》 " + pam.getAssistingTaskSpawner().getTasks().size());

		AgentStarter.isCreateVar = true;
		AgentStarter.createStartick = TaskManager.getCurrentTick();

		cancel();
	}

	// 有嵌套的构建，如果中间有term是复合结构，则递归调用，createArgsWithNest
	private static void createArgsWithNest(CompoundTerm compoundTerm, Node node) {
		Term[] terms = compoundTerm.getTerms();
		for (int i = 0; i < terms.length; i++) {
			String query1 = "merge (n{name: \'" + terms[i].toString() + "\'}) return n";
			Node node1 = NeoUtil.getNodeCypher(query1);
			// 新建结构边
			String query2 = "match (n),(m) where n.name = \'" + node.getNodeId() + "\' and m.name = \'" + terms[i].toString() + "\' merge (n)-[r:arg" + i + "]->(m) return r";
			Link link1 = NeoUtil.getLinkCypher(query2);

			// 如果是复合结构，则递归调用
			if (terms[i] instanceof CompoundTerm) {
				createArgsWithNest((CompoundTerm) terms[i], node1);
			}
		}
	}

	public static void varTask(Node sink, String change) {
		String query;
		if(change == null || change.equals("")) {
			// 初始化变量，没有参考句
			query = "match (n)-[r:初始句]->(m) where n.name = \'" + sink.getTNname()  + "\' return r";
		}else {
			query = "match (n)-[r:变量句]->(m) where n.name = \'" + sink.getTNname()  + "\' return r";
		}
		System.out.println("query = " + query);
		Link link1 = null;
		try (Transaction tx0 = graphDb.beginTx()) {
			try (Result result0 = tx0.execute(query, NeoUtil.parameters)) {
				Map<String, Object> row0;
				while (result0.hasNext()) {
					row0 = result0.next();
					Relationship actre;
					for (String key0 : result0.columns()) {
						actre = (Relationship) row0.get(key0);

						link1 = NeoUtil.CastNeoToLidaLink(actre, null);
						Node toNode = (Node) link1.getSink();

						if (change == null || change.equals("")) {
							// 初始化变量，没有参考句
							String[] gTerms = toNode.getTNname().split(",");
							if (gTerms[0].contains("$")) {
								PamNode type = TermUtil.getPamNode(gTerms[0], 20001 + 5);
								PamNode value = TermUtil.getPamNode(gTerms[1], 20001 + 5);
								IsaPamTask.makeNowisa(type, value);
							}
						}else {
							String[] pTerms = change.split(",");
							String[] gTerms = toNode.getTNname().split(",");

							if (pTerms.length != gTerms.length) {
								System.out.println("变量句中变量个数不匹配");
								return;
							} else {
								for (int i = 0; i < pTerms.length; i++) {
									if (gTerms[i].contains("$")) {
//									if(gTerms[i].contains("加数")) {
//										continue;
//									}
										// .replace("$","")
										PamNode type = TermUtil.getPamNode(gTerms[i].replace(")", ""), 20001 + i);
										PamNode value = TermUtil.getPamNode(pTerms[i].replace(")", ""), 20001 + i);
										IsaPamTask.makeNowisa(type, value);
										System.out.println("---------时序---变量定义----------" + type.toString() + " = " + value.toString());
									}
								}
							}
						}

//						System.out.println("---------时序---变量定义----------" + link1.toString());
					}
				}
			}
			tx0.commit();
		}
	}
}
