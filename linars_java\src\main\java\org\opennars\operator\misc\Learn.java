/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.operator.misc;

import edu.memphis.ccrg.lida.pam.tasks.LearnTask;
import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
import org.opennars.entity.Task;
import org.opennars.interfaces.Timable;
import org.opennars.operator.Operation;
import org.opennars.operator.Operator;

import java.util.List;

import static edu.memphis.ccrg.lida.framework.FrameworkModuleImpl.taskSpawner;

/**
 * 学习操作符，精确构建时序模式
 */
public class Learn extends Operator {
    public Learn() {
        super("^learn");
    }

    public Learn(final String name) {
        super(name);
    }

    @Override
    public List<Task> execute(final Operation operation, final Term[] args, final Memory memory, final Timable time) {
        // 这里是narsese操作，也就是图程执行，调用底层学习算法，等待输入=耗时操作，不确定因素多。挂起对输入的轮询动机
        // 不能用narsese操作符，死循环等待也不行，需要短时间结束narsese操作，释放线程。直接搜索或创建都是短时操作
        // 底层有底层的调用，图程有图程的调用，先后天区别。底层零散经验学习，精确构建=半底层
        // getActRoot是查既有数据，迅速一步执行，后面逐步调用对应时序线程。不太一样，这里从后天语句调用先天操作
        // 图程执行也可转（^do，getActRoot），do是指roottask。learntask类似动机构建link，调用时序的阶段，learnroot还需额外线程
        LearnTask learnTask = new LearnTask(args, null, null, null);
        taskSpawner.addTask(learnTask);
        System.out.println("LearnTask 加入 taskSpawner---------" + learnTask.toString());
        return null;
    }

}
