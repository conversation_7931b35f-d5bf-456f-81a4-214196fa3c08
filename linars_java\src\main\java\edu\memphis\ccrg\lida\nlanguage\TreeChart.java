package edu.memphis.ccrg.lida.nlanguage;

import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.pam.PamLinkImpl;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Term;
import org.opennars.entity.BudgetValue;
import org.opennars.io.Symbols;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

public class TreeChart extends CompoundTerm {
//    int start;
//    int end;

    // 只用string做key，term太笨重了，场景名和id不足以区分，需要整个term，哈希也可，后期再说
    // 需要次序与否，某些框架无需次序，语法语义需要次序，视听需要空间关系，框架元素间关系，输入信息间也有交互关系
    // 先不管元素间关系激活投票，排序时元素间关系越匹配，优先级越高，不排除输入语句本身有语义语法问题，有即时纠正效果
    // merge后保留原框架，不要修改，以便回溯？merge后框架优先度提高，即使不完整也比完整原构式高。要严格控制merge
    // 不回溯，以体量换时间，并行投票扩散，回溯需求低，优势是短时间得到大量结果，但也要有回溯选项，并做好剪枝和判别
    // 针对特定框架，从0到1匹配完整，不用构建新中间框架，merge需构建新框架
//    public String sceneStr;// 框架的key，也就是整个框架，一开始就要完整？

//    public Term sceneTerm;

    // 组装好的嵌套框架，一开始为完整的原始状态，即found和find的集合，不一定匹配完整，匹配完整=完整的嵌套状态
    // 嵌套和原始要区分，且同时存在，也可能只留嵌套，从嵌套能推出原始，但反过来不行，原始是嵌套的子集
//    public Term[] sceneTermList;

    public BudgetValue budget;// 框架的预算，包括优先级，优先级依据完整度、强度*激活度、权重等排序

    public Term sceneRoot;// 构式根节点，暂不分构式形状
    public Collection<Term> foundList; // 包括向下各层嵌套的元素，整合merge。不止当前层

    // 对比earley算法，区别是：多了嵌套、自底向上、多值排序。用来衡量匹配度和是否预测
    public Collection<Term> findingList;
    // todo 动机树也可以是一个大term？非结构树，而是竞争树，有多个竞争分支，竞争者之间有优先级，竞争者之间有冲突

    // todo 需大改概念mem的时候，替代概念，一个复合term包含所有，目前ns的bag只term，mem的bag用概念
    // 父节点集，向上关联，类似concept的termlink，注意树分支不同，关联同一父节点，不完全是同一个框架，向下关联亦是
    // 继承于复合term，后面全继承于此类，但nars原生term没那么多边和属性，生成或推理时，需要组装转换，不过这边过去的都有边
    // 先让product继承，试下水。嵌套都重在pro。sceneTerm是pro，无限套娃？
    public ArrayList<TreeChart> parentList;
    public int cpsize; // 已完成产生式右部长度，也就是语义树子节点个数，与词数相同即是整句
    int status;
    public void PrintChart() {
        System.out.println("["+ budget + sceneRoot +","+foundList+","+findingList+"]");
    }
    // 空构造
    public TreeChart() {
        super();
//        this.sceneStr = "";
        this.budget = null;
        this.sceneRoot = null;
        this.foundList = null;
        this.findingList = null;
        this.status = 0;
    }

    public TreeChart(Term... arg) {
        this(new ArrayList<>(), arg);
    }

    public TreeChart(Collection<Term> findingList0, Term... arg){
        // 第一句只能是this或super，解决super链的问题，否则会有很多父类属性需要初始化。具体属性后面再调整
        super(arg);
//        this("", new BudgetValue(0.99f, 0.1f, 0.1f,AgentStarter.nar.narParameters),
//                null, arg, null, findingList);
        this.findingList = findingList0;
//        this.budget = new BudgetValue(0.99f, 0.1f, 0.1f,AgentStarter.nar.narParameters);
//        init(arg);
        initParams(findingList0, arg);
    }

    private void initParams(Collection<Term> findingList0, Term[] arg) {
        // 遍历arg，找到各term的sink节点并统计，数量多的为根节点，并取得ordermap拼接sceneStr
        // arg排除finding即为found。可能有多种形状的框架，同进同出，或杠铃形，或多根，或多叶，或多根叶
        int lsize = arg.length;
        int[] sinkCount = new int[lsize];
        int maxSinkCount = 0;
        int maxSinkIndex = 0;
        int sinkIndex = 0;

        String rootName = "";
        Map<Node,Integer> sinkMap = new HashMap();
        String subSceneStr = "";
        Map<Integer,String> orderMap = new HashMap();

        for (int i = 0; i < lsize; i++) {
            if (arg[i] instanceof Link) {
                Link ll = (Link) arg[i];

                if (!findingList0.contains(ll)) {
                    this.foundList.add(arg[i]);
                }
//                if (ll.getSource() instanceof TreeChart){
//                    subSceneStr = ((TreeChart) ll.getSource()).sceneStr;
//                }else {
//                    subSceneStr = (String) ll.getSource().getProperty("name");
//                }
//                orderMap.put(Integer.valueOf((String) (ll.getProperty("order"))), subSceneStr);

                // 统计sink节点数量，并找到最多的（单层）或最深的（多层），作为根节点，取得rootname
                if (sinkMap.containsKey(ll.getSink())) {
                    sinkIndex = sinkMap.get(ll.getSink());
                    sinkCount[sinkIndex]++;
                }else {
                    sinkMap.put((Node) ll.getSink(), sinkIndex);
                    sinkCount[sinkIndex] = 1;
                }
                if (sinkCount[sinkIndex] > maxSinkCount) {
                    maxSinkCount = sinkCount[sinkIndex];
                    maxSinkIndex = sinkIndex;
                }
            }else {
                // todo 可能是单个term，需要构造一条边，默认各个term指向构造的根节点
                Link ll = new PamLinkImpl();
            }
        }
        StringBuffer buf = new StringBuffer();
        // 生成场景文本序列=产生式规则，场景为左部，构式其他成分为右部，要注意词语次序，有些框架本身带有嵌套
        // 两种方案，原始单产生式直存到场景节点；或实时生成。后者更好，因为存储费空间，而且不好改，再者嵌套需要实时生成
//        buf.append(rootName + "(");
//        for (int i = 0; i < lsize; i++) {
//            if(buf.length() > 8) buf.append(" , ");// 按字符数，非词数
//            buf.append(orderMap.get(i));
//        }
//        buf.append(")");

//        this.sceneStr = buf.toString();
        this.sceneRoot = arg[maxSinkIndex];

        AgentStarter.chartSize++;
        this.setNodeId(AgentStarter.chartSize);
    }

    public TreeChart(BudgetValue budget, Term sceneRoot, Term[] sceneTermList,
                     Collection<Term> foundList, Collection<Term> findingList) {
        super(sceneTermList);//((CompoundTerm)sceneTerm).term
        init(sceneTermList);
//        this.sceneStr = sceneStr;
//        this.sceneTerm = sceneTerm;
//        this.sceneTermList = sceneTermList;
        if (budget != null)
            this.budget = budget.clone(); // clone, not assignment
        else
            this.budget = null;
        this.sceneRoot = sceneRoot;
        this.foundList = foundList;
        this.findingList = findingList;
        this.status = 0;
//        setTermName(sceneStr);
//        setNodeName(sceneStr);
        AgentStarter.chartSize++;
        this.setNodeId(AgentStarter.chartSize);
    }

    private boolean Strcmp(String a, String b) {
        boolean result = (a == b);
        return result;
    }

    // 比较两个term是否相等
    private boolean Termcmp(Term a, Term b) {
        if(a instanceof CompoundTerm && b instanceof CompoundTerm) {
            CompoundTerm a1 = (CompoundTerm)a;
            CompoundTerm b1 = (CompoundTerm)b;
//            if(a1.getName().equals(b1.getName())) {
//                if(a1.getArgs().size() == b1.getArgs().size()) {
//                    for(int i = a1.getArgs().size()-1; i >= 0; i-- ) {
//                        if(!Termcmp(a1.getArgs().get(i), b1.getArgs().get(i)))
//                            return false;
//                    }
//                } else
//                    return false;
//            } else
//                return false;
        } else if(a instanceof Term && b instanceof Term) {
            if(a.toString().equals(b.toString())) {
                return true;
            } else
                return false;
        } else
            return false;
        return true;
    }

    private <E> boolean ArrayListCmp(ArrayList<E> a, ArrayList<E>b) {
        if(a.size() == b.size( )) {
            for(int i = a.size()-1; i >= 0; i-- ) {
                if(a.get(i) != b.get(i))
                    return false;
            }
        } else
            return false;
        return true;
    }
    public boolean Chartcmp(TreeChart a) {
//        if(this.start != a.start)
//            return false;
//        else if(this.end != a.end)
//            return false;
//        else
            if(!Termcmp(this.sceneRoot, a.sceneRoot))
            return false;
        else if(!ArrayListCmp(new ArrayList<>(this.foundList), new ArrayList<>(a.foundList)))
            return false;
        else if(!ArrayListCmp(new ArrayList<>(this.findingList), new ArrayList<>(a.findingList)))
            return false;
        else if(this.status != a.status)
            return false;
//        else if(!Termcmp(this.budget, a.budget))
//            return false;
        else if(!ArrayListCmp(this.parentList, a.parentList))
            return false;
//        else if(this.dotted_point != a.dotted_point)
//            return false;
        else if(this.cpsize != a.cpsize)
            return false;
//        else if(!Strcmp(this.sceneStr, a.sceneStr))
//            return false;
        else
            return true;
    }

    public float getPriority() {
        return budget.getPriority();
    }

    public void setPriority(final float v) {
        budget.setPriority(v);
    }

    //增加优先级
    public void incPriority(final float v) {
        budget.incPriority(v);
    }

    //减少优先级
    public void decPriority(final float v) {
        budget.decPriority(v);
    }

    //与具有相同键的另一个项目合并
    public TreeChart merge(TreeChart that) {
        budget.merge(that.budget);
        return this;
    }

    public BudgetValue getBudget() {
        return budget;
    }

    @Override
    public Symbols.NativeOperator operator() {
        return null;
    }

    @Override
    public CompoundTerm clone() {
        return null;
    }

    @Override
    public Term clone(Term[] replaced) {
        return null;
    }

//    @Override
//    public String name() {
////        return sceneStr;
//        // 和tostring一样，可能很庞大繁杂，要限制复杂度
//        return name();
//    }
}
