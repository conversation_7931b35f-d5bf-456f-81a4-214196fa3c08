package schema.execution.operations;

import schema.execution.ExecutionContext;
import schema.execution.Operation;

/**
 * 添加最终进位操作，处理计算完所有位后可能剩余的进位
 */
public class AddFinalCarryOperation implements Operation {
    @Override
    public Object execute(ExecutionContext context) {
        String result = (String) context.getVariable("结果");
        int carry = (int) context.getVariable("进位");
        
        context.setVariable("结果", carry + result);
        
        return null;
    }
}
