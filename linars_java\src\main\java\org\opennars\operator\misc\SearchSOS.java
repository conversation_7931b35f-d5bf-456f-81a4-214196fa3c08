/*
 * The MIT License
 *
 * Copyright 2019 OpenNARS.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.operator.misc;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
//import org.neo4j.graphdb.Node;
import org.neo4j.graphdb.Transaction;
import org.opennars.entity.Task;
import org.opennars.interfaces.Timable;
import org.opennars.io.Parser;
import org.opennars.operator.Operation;
import org.opennars.operator.Operator;

import java.util.*;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.narsese;

//SearchSingleObject，搜索单条，输出具体内容，基于单个条件
//案例：苹果是什么，工作好不好
public class SearchSOS extends Operator {
    public SearchSOS() {
        super("^SearchSOS");
    }
    public SearchSOS(final String name) {
        super(name);
    }
    // todo 与pam扩散整合，结合实时反馈，做更智能灵活的搜索
    @Override
    public List<Task> execute(Operation operation, Term[] args, Memory memory, Timable time) {
        List<Task> tasks = new ArrayList<>();
        if (args[0] instanceof CompoundTerm){
            CompoundTerm ctt = (CompoundTerm) args[0];
            Term[] terms = ctt.term;
            // 可能是语句数或词数
            System.out.println("Search:--------- " + Arrays.toString(terms));
            String sname = "";
            sname = getAnswer(terms);
            try {
                Task task = narsese.parseTask(sname + ".");
                tasks.add(task);
            } catch (Parser.InvalidInputException e) {
                throw new RuntimeException(e);
            }
        }

        // todo 可能是各种模态，如果是纯文本，可直接触发脑内语音或语言。感知数据，则要转语言
        return tasks;
    }

    private static String getAnswer(Term[] terms) {
        String sname = "";
        // todo 拓展为与内容无关，适配各种搜索场景和条件集，组装查询语句
        String cypher = "MATCH (c{name:'苹果'})-[r:arg0]->(a)<-[r1:arg1]-(b)-[r2:相似]->(d{name:'去哪了'}) return a";

        try (Transaction tx = graphDb.beginTx()) {
            List<Map<String, Object>> result = NeoUtil.getByCypher(cypher,tx);
            // 遍历结果，找到a的节点，然后获取场景，直接输出
            for (Map<String, Object> map : result) {
                org.neo4j.graphdb.Node a = (org.neo4j.graphdb.Node) map.get("a");
                sname = (String) a.getProperty("name");
                break;
            }
            tx.commit();
        }
        return sname;
    }
}