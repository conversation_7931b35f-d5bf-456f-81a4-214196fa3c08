package some;

public class AddOp0 {
    public static void main(String[] args) {
        String num1 = "123456789"; // 加数1
        String num2 = "987654321"; // 加数2

        String result = addStrings0(num1, num2);
        System.out.println("最终得数是：" + result);
    }

    // 自定义截取字符串方法，越界返回null
    public static String safeSubstring0(String str, int beginIndex) {
        if (str == null || beginIndex < 0 || beginIndex >= str.length()) {
            return null;
        }
        return str.substring(beginIndex, beginIndex + 1);
    }

    public static String addStrings(String num1, String num2) {
        // 获取两个字符串的最大长度
        int maxLength = Math.max(num1.length(), num2.length());
        StringBuilder result = new StringBuilder();
        int carry = 0; // 进位

        // 遍历每一位，直到达到最长长度或进位为0
        for (int i = 0; i < maxLength || carry > 0; i++) {
            // 获取num1当前位的数字
            int digit1 = getDigit(num1, maxLength - 1 - i);
            // 获取num2当前位的数字
            int digit2 = getDigit(num2, maxLength - 1 - i);
            // 将加数1和加数2对应位数的数字和进位加起来，得到当前位的和
            int sum = digit1 + digit2 + carry;

            // 拆分和的十位和个位，分别获取
            int tens = getDigit(String.valueOf(sum), 0);
            int ones = getDigit(String.valueOf(sum), 1);

            // 以和的十位数为进位
            carry = tens;

            // 将个位数添加到结果字符串
            result.insert(0, ones);
        }

        // 将结果字符串转换为普通字符串并返回
        return result.toString();
    }

    // 调用自定义截取方法获取当前位数字，如果越界则返回0
    private static int getDigit(String num, int index) {
        String digitStr = safeReverseSubstring(num, index);

        if (digitStr == null) {
            return 0;
        }else{
            return Integer.parseInt(digitStr);
        }

        // return digitStr.isEmpty() ? 0 : Integer.parseInt(digitStr);
    }

    // 自定义截取字符串方法，越界返回空字符串
    public static String safeSubstring(String str, int beginIndex) {
        if (str == null || (beginIndex != 0 && (beginIndex < 0 || beginIndex >= str.length()))) {
            return ""; // 如果参数不合法，返回空字符串
        }

        if (beginIndex == 0) {
            // 如果beginIndex是0，返回最后一个字符
            if (str.length() > 0) {
                return str.substring(str.length() - 1);
            } else {
                return ""; // 如果字符串为空，返回空字符串
            }
        } else {
            // 否则，从给定的beginIndex返回字符
            return str.substring(beginIndex, beginIndex + 1);
        }
    }

    // 倒序截取字符串方法
    public static String safeReverseSubstring(String str, int index) {
        if (str == null || index < 0 || index >= str.length()) {
            return null; // 如果参数不合法，返回空字符串
        }
    
        // 计算实际截取的起始位置
        int startIndex = str.length() - index - 1;
        if (startIndex < 0) {
            startIndex = 0; // 如果计算出的起始位置小于0，则从字符串开头截取
        }
    
        // 只截取一个字符
        return str.substring(startIndex, startIndex + 1);
    }

    public static String addStrings1(String num1, String num2) {
        StringBuilder result = new StringBuilder();
        int carry = 0; // 进位
        int i = 0; // 公共的索引，从最低位（个位）开始

        // 遍历每一位，直到达到最长长度或进位为0
        while(true){
            // 截取加数1当前位的数字，称为数一数
            int digit1 = getDigit(num1, i);
            // 截取加数2当前位的数字，称为数二数
            int digit2 = getDigit(num2, i);

            // 将数一数和数二数和进位加起来，得到当前位的和
            int sum = digit1 + digit2 + carry;
            // 判断是否已经遍历到最末尾
            if (sum == 0 && i >= Math.max(num1.length(), num2.length())) {
                break; // 跳出循环
            }
            // 截取和的十位数，作为进位
            carry = getDigit(String.valueOf(sum), 1);

            // 将个位数添加到结果字符串
            result.insert(0, getDigit(String.valueOf(sum), 0));
            i++;
        }

        // 将结果字符串转换为普通字符串并返回
        return result.toString();
    }


    public static String addStrings0(String num1, String num2) {
        StringBuilder result = new StringBuilder();
        int carry = 0; // 进位
        int i = 0; // 公共的索引，从最低位（个位）开始

        // 遍历每一位，直到达到最长长度或进位为0
        while(true){
            // 截取加数1当前位的数字
            String digit1Str = safeReverseSubstring(num1, i);
            // 称为数一数
            int digit1;
            // 截取加数2当前位的数字
            String digit2Str = safeReverseSubstring(num2, i);
            // 称为数二数
            int digit2;
            // 如果数一数是空字符串，也就是当前位没有数字，则用0代替
            if (digit1Str == null) {
                digit1 = 0;
            }else{
                digit1 = Integer.parseInt(digit1Str);
            }
            // 如果数二数是空字符串，也就是当前位没有数字，则用0代替
            if (digit2Str == null) {
                digit2 = 0;
            }else{
                digit2 = Integer.parseInt(digit2Str);
            }

            // 如果数一数和数二数都是空字符串，则已经遍历到最末尾
            if (digit1Str == null && digit2Str == null) {
                // 如果进位为1，则直接设置到得数开头
                if (carry == 1){
                    result.insert(0, "1");
                }
                break; // 跳出循环
                // 循环机制：
                // 递归是层层嵌套，循环同级替换？回溯重新执行，维护新数值集合。也是调用搜索api等
                // 一切平面化=统一为显式结构，无底层结构=如树图回溯等，回溯、跳转、分支等=都是操作=显式执行
                // 底层先天循环等需要有=体感式，也有后天循环=心理式，认知上自主执行控制
                // 有树图等明确结构=好处理，全平面化=可能出差错，可能费劲，有比没有好？
            }

            // 将数一数和数二数和进位加起来，得到当前位的和sum
            int sum = digit1 + digit2 + carry;

            // 截取和的十位数
            carry = getDigit(String.valueOf(sum), 1);

            // 将和的个位数作为得数对应位数的数字
            result.insert(0, getDigit(String.valueOf(sum), 0));

            // todo 概括性时序=非实时执行时序=类似主要代码注释：
            //      【继续重复以上步骤，直到进位为0，且已经遍历到最高位】
            // 多方面：学习时输入=详细冗余+多实例实践=覆盖各种情况，编译沉淀=变量化+细节印记
            // 执行时=根据逻辑=选择性表达，输出=过程打印，注释=概括总结=脉络
            // 可以统一=输入+沉淀，执行+打印+注释=难统一

            // 往前移动一位，做为新的当前位数
            i++;
        }

        // 将结果字符串转换为普通字符串并返回
        return result.toString();
    }

}