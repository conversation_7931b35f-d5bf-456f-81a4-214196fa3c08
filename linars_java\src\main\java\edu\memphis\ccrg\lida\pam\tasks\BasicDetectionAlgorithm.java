/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.initialization.Initializable;
import edu.memphis.ccrg.lida.framework.shared.Linkable;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.shared.NodeStructureImpl;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PamLinkable;
import edu.memphis.ccrg.lida.pam.PamNode;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.sensorymemory.SensoryMemory;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * This class implements the FeatureDetector interface and provides default
 * methods. Users should extend this class and overwrite the detect() and
 * excitePam() methods. A convenience init() method is added to initialize the
 * class. This method can be overwritten as well. This implementation is
 * oriented to detect features from sensoryMemory, but the implementation can be
 * used to detect and burstActivation from other modules, like Workspace,
 * emotions or internal states.
 * 
 * <AUTHOR> J. McCall
 * <AUTHOR> Snaider
 * 
 */
public abstract class BasicDetectionAlgorithm extends FrameworkTaskImpl
		implements DetectionAlgorithm {

	private static final Logger logger = Logger
			.getLogger(BasicDetectionAlgorithm.class.getCanonicalName());

	private NodeStructure nodeStructure = new NodeStructureImpl();

	/**
	 * the {@link SensoryMemory}
	 */
	protected SensoryMemory sensoryMemory;
	/**
	 * the {@link PAMemory}
	 */
	protected PAMemory pam;
	/**
	 * {@link PamLinkable} this algorithm detects
	 */
	protected PamLinkable linkable;

	/**
	 * Default constructor. Associated {@link Linkable},
	 * {@link PAMemory} and {@link SensoryMemory} must be set
	 * using setters.
	 */
	public BasicDetectionAlgorithm() {
	}

	@Override
	public void setAssociatedModule(FrameworkModule module, String moduleUsage) {
		if (module instanceof PAMemory) {
			pam = (PAMemory) module;
		} else if (module instanceof SensoryMemory) {
			sensoryMemory = (SensoryMemory) module;
		} else {
			logger.log(Level.WARNING, "Cannot set associated module {1}",
					new Object[] { TaskManager.getCurrentTick(), module });
		}
	}

	@Override
	public void setPamLinkable(PamLinkable linkable) {
		this.linkable = linkable;
	}

	@Override
	public PamLinkable getPamLinkable() {
		return linkable;
	}

	/**
	 * This task can be initialized with the following parameters:<br><br/>
	 * 
	 * <b>node type=string</b>label of the Node in {@link PAMemory} this algorithm detects<br/>
	 * 
	 * @see Initializable
	 */
	@Override
	public void init() {
		super.init();
		String nodeLabel = (String) getParam("node", "");
		if (nodeLabel != null) {
			nodeLabel = nodeLabel.trim();	// todo 图数据
//			PamNode node = (PamNode) GlobalInitializer.getInstance().getAttribute(nodeLabel);
			Node node = nodeStructure.getNeoNode(nodeLabel);
			if (node != null && node.getNodeId() != 0 && !"Node".equals(nodeLabel)) {
				setPamLinkable((PamLinkable) node);
			} else {
				logger.log(Level.WARNING,
								"could not get node {1} from neo",
								new Object[] { TaskManager.getCurrentTick(),
										nodeLabel });
			}
		}
	}

	// 应该是信息激活神经信息，接受是被动的，而不是探测
	// 注意力调动的探测才是主动的，注意力越集中，信息越精准丰富，粒度越小，但也范围越小
	// 如果注意力被调动但是不集中，只是平均水平的激活，集中后是更快更高的解析度
	// 注意不完全是筛选，也是焦点本身信息的丰富，概念信息、场景属性等更完善，调动焦点是筛选，其他是完善
	// 听觉焦点=类似视觉，音轨分解？

	@Override
	protected void runThisFrameworkTask() {
		double amount = detect();
		if (logger.isLoggable(Level.FINEST)) {
			logger
					.log(Level.FINEST, "detection performed {1}: {2}",
							new Object[] { TaskManager.getCurrentTick(), this,
									amount });
		}
		if (amount > 0.0) {
			if (logger.isLoggable(Level.FINEST)) {
				logger.log(Level.FINEST, "Pam excited: {1} by {2}",
						new Object[] { TaskManager.getCurrentTick(), amount,
								this });
			}

//			pam.receiveExcitation(linkable, amount);
		}
	}

	/**
	 * Override this method implementing feature detection algorithm.
	 * 
	 * @return degree [0,1] to which the feature was detected
	 */
	@Override
	public abstract double detect();

}