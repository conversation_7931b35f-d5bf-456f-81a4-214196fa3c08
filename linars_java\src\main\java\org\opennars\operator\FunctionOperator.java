/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.operator;

import com.google.common.collect.Lists;
import org.opennars.entity.*;
import org.opennars.interfaces.Timable;
import org.opennars.io.Symbols;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Term;
import org.opennars.language.Variable;
import edu.memphis.ccrg.linars.Memory;

import java.util.Arrays;
import java.util.List;

import static org.opennars.inference.BudgetFunctions.truthToQuality;


/** 
 * Superclass of functions that execute synchronously (blocking, in thread) and take
 * N input parameters and one variable argument (as the final argument), generating a new task
 * with the result of the function substituted in the variable's place.
 * 同步执行的函数父类，阻塞，线程，N个输入参数，一个变量参数，生成一个新的任务，函数的结果替换变量的位置
 */
public abstract class FunctionOperator extends Operator {
    protected FunctionOperator(final String name) {
        super(name);
    }

    /** y = function(x) 
     * @return y, or null if unsuccessful
     */
    abstract protected Term function(Memory memory, Term[] x);
    
    /** the term that the output will inherit from; analogous to the 'Range' of a function in mathematical terminology 
     * 该输出将从中继承的术语；类似于数学术语中的“范围”
     * */
    @Deprecated 
    abstract protected Term getRange();
        
    //abstract protected int getMinArity();
    //abstract protected int getMaxArity();
    
    @Override
    public List<Task> execute(Operation operation, final Term[] args, final Memory m, final Timable time) {
        //TODO make memory access optional by constructor argument
        //TODO allow access to Nar instance?
        // 使内存访问可选通过构造函数参数
        final int numArgs;
        boolean isSelf = false;
        if (args[0].toString().equals("SELF") || args[0].toString().equals("{SELF}")) {
            isSelf = true;
            numArgs = args.length - 1;
        } else {
            numArgs = args.length;
        }
//        numArgs = args.length - 1;
        
        if (numArgs < 1) {
            throw new IllegalStateException("Requires at least 1 arguments " + Arrays.toString(args));
        }
        
        if (numArgs < 2 /*&& !(this instanceof Javascript)*/) {
            throw new IllegalStateException("Requires at least 2 arguments " + Arrays.toString(args));
        }
        
        //last argument a variable?
//        final Term lastTerm = args[numArgs];
//        final boolean variable = lastTerm instanceof Variable;
        
        int numParam = numArgs - 1;
        
        /*if(this instanceof Javascript && !variable) {
            numParam++;
        }*/

        if (!isSelf){
            numParam++;
        }

        final Term[] x = new Term[numParam];
        System.arraycopy(args, 0, x, 0, numParam);
        
        final Term y;
        //try {
            y = function(m, x);
            if (y == null) {
                return null;
            }
            /*if(!variable && this instanceof Javascript) {
                return null;
            }*/
            //m.emit(SynchronousFunctionOperator.class, Arrays.toString(x) + " | " + y);
        /*}
        catch (Exception e) {
            throw e;
        }*/
        final Sentence s;
        final Variable var = new Variable("$1");
        final float confidence = m.narParameters.DEFAULT_JUDGMENT_CONFIDENCE;
        if(isSelf) {
            //  Term actual_part = Similarity.make(var, y);
            //  Variable vardep=new Variable("#1");
            //Term actual_dep_part = Similarity.make(vardep, y);
            operation = (Operation) operation.setComponent(0,
                    ((CompoundTerm) operation.getSubject()).setComponent(
                            numArgs, y, m), m);
            s = new Sentence(operation,
                    Symbols.JUDGMENT_MARK,
                    new TruthValue(1.0f, confidence, m.narParameters),
                    new Stamp(time, m));
        }else {
            s = new Sentence(y,
                    Symbols.JUDGMENT_MARK,
                    new TruthValue(1.0f, confidence, m.narParameters),
                    new Stamp(time, m));
        }
        final BudgetValue budgetForNewTask = new BudgetValue(m.narParameters.DEFAULT_JUDGMENT_PRIORITY,
            m.narParameters.DEFAULT_FEEDBACK_DURABILITY,
            truthToQuality(s.getTruth()), m.narParameters);
        final Task newTask = new Task(s, budgetForNewTask, Task.EnumType.INPUT);
        return Lists.newArrayList(newTask);
    }

    /** (can be overridden in subclasses) the extent to which it is truth 
     * that the 2 given terms are equal.  in other words, a distance metric
     * 能够被子类覆盖，它是2个给定术语相等的程度。换句话说，距离度量
     */
    public float equals(final Term a, final Term b) {
        //default: Term equality
        return a.equals(b) ? 1.0f : 0.0f;
    }
}
