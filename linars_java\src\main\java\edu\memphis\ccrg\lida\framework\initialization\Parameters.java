package edu.memphis.ccrg.lida.framework.initialization;

import java.io.Serializable;

public class Parameters implements Serializable {
    // 默认注意模态
    public volatile String DEFAULT_MODULE = "SEE";

    // 现实六感，五感+文本，有基线，低于则抑制，高于则兴奋
    public volatile double see = 0.5;
    public volatile double listen = 0.5;
    public volatile double tast = 0.5;
    public volatile double smell = 0.5;
    // 计算机文本，可能有其他格式类型
    public volatile double textstr = 0.5;
    // 笼统体感
    public volatile double feel = 0.5;
    // 具体体感
    public volatile double hungry = 0.5;
    public volatile double full = 0.5;
    public volatile double sick = 0.5;
    // 思考六感
    public volatile double think_see = 0.5;
    public volatile double think_listen = 0.5;
    public volatile double think_feel = 0.5;
    public volatile double think_tast = 0.5;
    public volatile double think_smell = 0.5;
    public volatile double think_text = 0.5;
    // 视觉关注区域
    public volatile String see_area = "middle";
    public volatile double see_area_atten = 0.5;
    // 听觉关注频率
    public volatile String listen_hz = "middle";
    public volatile double listen_area_atten = 0.5;

    // 各种情绪值，多巴胺有基线，好感高于0，其他都为0，没有抑制，只有激活
    public volatile double happy = 0.05;
    public volatile double sad = 0.0;
    public volatile double angry = 0.0;
    public volatile double guilty = 0.0;
    public volatile double scare = 0.0;
    public volatile double worry = 0.0;
    public volatile double ill = 0.0;
    public volatile double hate = 0.0;
    public volatile double boring = 0.0;
}
