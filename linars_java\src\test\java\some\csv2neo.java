package some;

import cn.hutool.json.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import com.opencsv.exceptions.CsvValidationException;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class csv2neo {
    // 读csv文件，存到neo4j数据库中，先存为csv作为中间数据
    // cvs文件格式：
    // 每个vsv文件是一个句子的语法分析结果，包含文本、词性标注、词法分析、句法分析等，各列分别表示：
    // Text, tok/fine, tok/coarse, pos/ctb, pos/863, pos/pku, ner/msra, ner/ontonotes, srl, dep, sdp, con
    // 举例，以下是句子“后面是负载”各列的值，对应上面的列名：
    // 1、后面是负载,
    // 2、"['后面', '是', '负载']",
    // 3、"['后面', '是', '负载']",
    // 4、"['NN', 'VC', 'NN']",
    // 5、"['nl', 'vl', 'v']",
    // 6、"['f', 'v', 'n']",
    // 7、[],
    // 8、[],
    // 9、"[[('后面', 'ARG0', 0, 1), ('是', 'PRED', 1, 2), ('负载', 'ARG1', 2, 3)]]",
    // 10、"[(2, 'top'), (0, 'root'), (2, 'attr')]",
    // 11、"[[(2, 'Loc')], [(0, 'Root')], [(2, 'Clas')]]",
    // 12、(TOP (IP (NP (NN 后面)) (VP (VC 是) (NP (NN 负载)))))

    // 目前只考虑句法分析（短语结构）和依存分析，也就是con和sdp列
    // 将con列和sdp列的格式转换为neo4j的图数据库格式
    // 举例，con列的格式为：
    // (TOP (IP (NP (NN 后面)) (VP (VC 是) (NP (NN 负载)))))
    // con转换为neo4j的图数据库格式为：
    // 1. 创建节点，每个节点对应一个句法分析结果，并设置属性，包括：
    // 1.1 句法分析结果，即con列中的字符串
    // 1.2 句法分析结果对应的词性标注，即句法分析结果中的词性标注，如(NN 后面)中的'NN'
    // 2. 创建关系，将句法分析结果和其父句法分析结果建立关系，并设置属性，包括：
    // 2.1 关系类型，即句法分析结果中的top、root、attr等
    // 2.2 关系索引，即句法分析结果中的数字，如(2, 'top')中的2
    // 2.3 关系对应的词性标注，即句法分析结果中的词性标注
    // sdp依存关系转换为neo4j的图数据库格式为：
    // 1. 节点已由con转换等过程创建，只需新建关系边
    // 3. 关系连接的节点为本节点和sdp列中数字对应节点，如(2, 'Loc')中的2，对应词序为2的节点
    // 4. 关系类型为sdp列中关系，如(2, 'Loc')中的'Loc'
    // 节点和关系分别存在不同csv文件

    //todo 但是读取复杂结构就挺繁杂，只是操作csv和存neo，py也可以，不用深度融合linars


    public static void main(String[] args) {
        try {
            // 指定CSV文件路径
            String csvFilePath = "D:\\lida\\neo-lida-nars310-xr\\src\\test\\resources\\OLPC_2_35.csv";
            // 指定转换后的JSON文件保存路径
            String jsonFilePath = "D:\\lida\\neo-lida-nars310-xr\\src\\test\\resources\\OLPC_2_35.json";

            // 读取CSV文件
            // 使用InputStreamReader和FileReader来指定字符集
            InputStreamReader inputStreamReader = new InputStreamReader(new FileInputStream(csvFilePath), StandardCharsets.UTF_8);
            CSVReader csvReader = new CSVReaderBuilder(
                    inputStreamReader)
                    .withSkipLines(1) // 假设第一行是标题行，跳过它
                    .build();

            List<JSONObject> jsonObjects = new ArrayList<>();

            String[] nextLine;
            while ((nextLine = csvReader.readNext()) != null) {
                JSONObject jsonObject = new JSONObject();
                System.out.println(nextLine[11]);
                System.out.println(nextLine[12]);
//                jsonObject = JSON.parseObject("{" + nextLine[10] + "}"); // 假设Text列是第1列（索引从0开始）

                JSONArray jsonArray = TreeConverter.convertTreeToJSON2(nextLine[12]);

                System.out.println(jsonArray);

//                jsonObject.put("con", nextLine[10]); // 假设con列是第11列（索引从0开始）
//                jsonObject.put("sdp", nextLine[11]); // 假设sdp列是第12列
                // 如有其他需要转换的列，可以继续添加

                // 解析con列，并创建节点和关系



                // 解析sdp列，并创建关系



                jsonObjects.add(jsonObject);
            }

            csvReader.close();

//            // 将JSON对象列表转换为JSON数组字符串，并写入文件
//            JSONObject jsonArray = new JSONObject();
//            jsonArray.put("data", jsonObjects);
//
//            OutputStreamWriter outputStreamWriter = new OutputStreamWriter(new FileOutputStream(jsonFilePath), StandardCharsets.UTF_8);
//            try (BufferedWriter writer = new BufferedWriter(outputStreamWriter)) {
//                writer.write(jsonArray.toString()); // 使用4个空格的缩进
//            }
//
//            System.out.println("CSV文件已成功转换为JSON文件！");

        } catch (IOException e) {
            e.printStackTrace();
        } catch (CsvValidationException e) {
            throw new RuntimeException(e);
        }
    }


    public static void main00(String[] args) {
        String input = "(VP (VV 召开))";
        List<Object> result = parseInput(input);
        System.out.println(result);
    }

    public static List<Object> parseInput(String input) {
        List<Object> root = new ArrayList<>();
        String[] tokens = input.split("\\s+"); // 使用空格分割字符串
        parseTokens(tokens, root);
        return root;
    }

    private static void parseTokens(String[] tokens, List<Object> currentList) {
        if (tokens.length == 0) {
            return;
        }

        String token = tokens[0];
        if (token.startsWith("(") && token.endsWith(")")) {
            // 移除括号，并递归处理内部列表
            String innerString = token.substring(1, token.length() - 1);
            String[] innerTokens = innerString.split("\\s+");
            List<Object> innerList = new ArrayList<>();
            parseTokens(innerTokens, innerList);
            currentList.add(innerList);
        } else {
            // 添加单个元素
            currentList.add(token);
        }

        // 递归处理剩余tokens
        String[] remainingTokens = new String[tokens.length - 1];
        System.arraycopy(tokens, 1, remainingTokens, 0, tokens.length - 1);
        parseTokens(remainingTokens, currentList);
    }


    public static void main0(String[] args) {
        String treeString = "(VP (VV 召开))";
        JSONArray jsonArray = convertTreeToJSON(treeString);
        System.out.println(jsonArray.toString());
    }

    public static JSONArray convertTreeToJSON(String treeString) {
        JSONArray currentArray = new JSONArray();
        StringBuilder token = new StringBuilder();
        int depth = 0;

        for (char c : treeString.toCharArray()) {
            switch (c) {
                case '(':
                    if (depth > 0) {
                        JSONArray newArray = new JSONArray();
                        currentArray.put(newArray);
//                        currentArray = newArray;
                    }
                    depth++;
                    break;
                case ')':
                    depth--;
                    if (token.length() > 0) {
                        currentArray.put(token.toString());
                        token = new StringBuilder();
                    }
                    if (depth == 0) {
                        break;
                    }
                    currentArray = (JSONArray) currentArray.get(currentArray.size() - 2);
                    break;
                case ' ':
                    if (token.length() > 0) {
                        currentArray.put(token.toString());
                        token = new StringBuilder();
                    }
                    break;
                default:
                    token.append(c);
                    break;
            }
        }

        return currentArray;
    }

}



