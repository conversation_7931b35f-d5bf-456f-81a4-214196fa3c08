package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.nlanguage.SubGraphSet;
import edu.memphis.ccrg.lida.nlanguage.TreeNode;
import edu.memphis.ccrg.lida.nlanguage.Tree_nest;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.workspace.Workspace;
import edu.memphis.ccrg.lida.workspace.WorkspaceImpl;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;
import edu.memphis.ccrg.linars.*;
import org.opennars.control.DerivationContext;
import org.opennars.entity.Task;
import org.opennars.language.Implication;
import org.opennars.language.Similarity;
import org.opennars.language.Statement;
import org.opennars.main.Parameters;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;

/**
 */
public class GoalBackgroundTask extends FrameworkTaskImpl {
	private static final Logger logger = Logger.getLogger(GoalBackgroundTask.class.getCanonicalName());
//	private SensoryMemory sm;
	private PAMemory pam;
	private NodeStructure seqNs;
	private NodeStructure goalNs;
	private NodeStructure listenNs;
	public WorkspaceBuffer csm;
	public WorkspaceBuffer seqGraph;
	public WorkspaceBuffer goalGraph;
	public WorkspaceBuffer listenGraph;
	private String query;

	@Override
	public void setAssociatedModule(FrameworkModule m, String moduleUsage) {
		if(m instanceof PAMemory){    // 用factorydata里的初始化，不是alifeagent里的
			pam = (PAMemory) m;
		}else if(m instanceof Workspace){
			csm = (WorkspaceBuffer) m.getSubmodule(ModuleName.CurrentSM);
			seqGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.SeqGraph);
//			goalGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.GoalGraph);
			listenGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.ListenGraph);

			seqNs = seqGraph.getBufferContent(null);
//			goalNs = goalGraph.getBufferContent(null);
			listenNs = listenGraph.getBufferContent(null);
		}else{
			logger.log(Level.WARNING, "Cannot add module {1}", new Object[]{TaskManager.getCurrentTick(),m});
		}
	}

	/**
	 * 动机管理：动机维持，动机执行，动机挂起，动机激活，动机取消，动机中断，动机竞争
	 */
	@Override
	protected void runThisFrameworkTask() {
		WorkspaceBuffer buffer = (WorkspaceBuffer) ((WorkspaceImpl) pam.getListener()).getSubmodule(ModuleName.GoalGraph);
		NodeStructure nsmem = buffer.getBufferContent(null);
//		Memory mem = (Memory) nsmem;
		// 树图结构操作
		SubGraphSet mem = (SubGraphSet) nsmem;

		Long tick = TaskManager.getCurrentTick() - AgentStarter.doStartick;

		// 第一方面：判断listenNs是否包含顺承链接，有则激活整个场景，做备用动机
		// 应该是先多模态整合，这里暂限听觉输入，
//		for(Link glink:listenNs.getLinks()){
//			if(glink.getTNname().equals("顺承")){
//				// 涉我判断+真假判断，感知buffer直达，构建场景，todo 模糊匹配=上位对象+上位动作=类似场景
//				// 默认主人+对我说+X，一个音就直接激活动机，一般知道是谁说的时候，已经有几个音
//				// 动机：我+对主人+说，具体内容待定，等有足够激励即可执行，语无伦次是内容不足
//
////				String query = "match (n)<-[r]-(m:场景)-[r0]->(i) where n.name = \'"
////						+ "对主人" + "\'  and i.name = \'" + "说"  + "\' return *";
//
//				// 搜索glink的尾节点场景，将场景所有要素加入goalGraph，充当备用动机挂起
//				Node goal = (Node) glink.getSink();
//				query = "match (m:场景)-[r]->(n) where m.name = \'" + goal.getTNname() + "\' return *";
//				NeoUtil.addNodesAndRelsToBuffer(query, ModuleName.GoalGraph);
//			}
//		}

		// 第二方面：从体感舒适的根本目标反推
		// 需要两边夹击，场景是状态条件，体感是根本目标
		// 直接搜条件与体感的最短路，中间是备选动机，最后是根本目标
		// todo 场景动态变化，需要重新搜索，实时构建调整
		//
		Concept root = mem.concepts.get("<SELF --> [happy]>");
		Tree_nest goalTree = mem.goalTree;
		if (root != null) {
			goalTree.addNode(root, null);
			// todo 竞争构建动机链（or树），不能在扩散激活时进行，因为没有root。可构建备选树，但竞争后的链没法构建
			// 备选链也可以预先构建，扩散时可动态竞争，同节点多分支竞争，不同节点不同分支竞争需语义推理
			recursiveBuild0(goalTree, root, mem);
		}

		// 知识和程序分离，知识=概念和组分，意向=时序和体感，意向和信念分离。
		// 动机子图目标驱动，只处理动机任务，不推理+不管知识任务，知识子图信念驱动，不管动机任务，但推理
		// 这里整体调控，全面竞争，nars目标是单条处理

//		excuteBuffer(mem);
		excuteTree(mem);

		// 不取消就是一直执行，与常驻线程有别，常驻是agi启动即有+一直执行+也可取消，这里是调用才有
//		cancel();
	}

	// todo 不用每个都从根本目标开始，大量经验积累，转为每个经历过或潜在的目标有激励度
	private void excuteTree(SubGraphSet mem) {
		int size = mem.goalTree.allNodeSize;
		if (size > 1) {
			// 获取树的最底层节点，并执行
			TreeNode node = mem.goalTree.root;
			while (!node.getChildren().isEmpty()) {
				node = node.getChildren().get(0);
			}
			// 执行任务，并移除节点
			Task task0 = (Task) node.getValue();
			Task task = task0.toOtherType("goal");
			mem.goalTree.removeNode(task0.getTerm());

			// 目标概念和任务
			Concept goalC = mem.concepts.get(((Statement)task0.getTerm()).getPredicate().toString()).clone();
//			Task task1 = goalC.beliefs.get(0);

			Task task00 = mem.globalBuffer.getByTerm(goalC.term.toString());
			Task goalTask;
			if (task00 == null){
				goalTask = goalC.toTask('!');
			}else {
				goalTask = task00;
			}

			// 执行任务，暂用原目标执行逻辑
//			execute(mem, task);

			if (goalTask != null) {
				List<Task> execPreconditions = new ArrayList<>();
				// 顺承时序，加入当前已决定言行
				execPreconditions.add(task0);
				DerivationContext nal = new DerivationContext(mem, new Parameters(), nar);

				ProcessGoal.calcBestExecutablePrecondition(nal, goalC,
						goalTask.sentence, execPreconditions, new LinkedHashMap<>(), goalTask);
//				mem.localInference(task1, mem.narParameters, nar, mem);
			}
			System.out.println("excuteTree ----- " + task0);
			// 递归执行树
//			excuteTree(mem);
		} else {
			// todo 闲时任务，后台任务，如学习，记忆，推理等
			System.out.println("goal----excuteTree ----- 无");
		}
	}

	private static void excuteBuffer(SubGraphSet mem) {
		// 根据mem.globalBuffer的数量，决定单次执行几条任务，区间按万算。作用是按数量调整，减少任务数量，提高效率
		if(mem.globalBuffer.nameSize() > 10){
			for (int i = 0; i < mem.globalBuffer.nameSize() / 1000; i++) {
				taskExecute_bf(mem);
			}
		}else{
			taskExecute_bf(mem);
		}
	}

	private void recursiveBuild(Tree_nest goalTree, Concept root, Memory mem) {
		List<Task> totals = new ArrayList<>();
		List<Task> generalPreconditions = root.general_executable_preconditions;
		List<Task> exePreconditions = root.executable_preconditions;
		totals.addAll(generalPreconditions);
		totals.addAll(exePreconditions);
		// 多条备选语句竞争，按权重优先级排，取最优先的
		// 优先级：1. 权重，2. 词项数量
		Concept last = null;
		// 输入概念为分支节点，task为边，连接分支节点和叶节点，叶节点为task里的尾词项关联的概念
		for (Task task : totals) {
			Term term = task.getTerm();
			Term pred = ((Statement)task.getTerm()).getPredicate();
			Term subj = ((Statement)task.getTerm()).getSubject();
			// 可能为顺承，则尾词项为pred
			if (term instanceof Implication){
				last = mem.concepts.get(subj.toString());
				goalTree.addNode(last, goalTree.root);
			}
			// 也可能为相似，pred和subj都可能为尾词项，需判断是否与root相同
			if (term instanceof Similarity){
				Concept predConcept = mem.concepts.get(pred.toString());
				if(predConcept.equals(root)){
					last = mem.concepts.get(subj.toString());
				}else {
					last = mem.concepts.get(pred.toString());
				}
			}
		}
		// 递归，直到叶节点
		if(last != null){
			recursiveBuild(goalTree, last, mem);
		}
	}

	private void recursiveBuild0(Tree_nest goalTree, Concept root, Memory mem) {
		List<Task> generalPreconditions = root.general_executable_preconditions;
		List<Task> exePreconditions = root.executable_preconditions;

		// 创建一个优先队列来存放任务，并根据优先级进行排序
		PriorityQueue<Task> priorityQueue = new PriorityQueue<>(
			Comparator.comparingInt(
				(Task t) -> {
					// 这里定义优先级计算逻辑
					// todo 后期有更多标准，如按权重排序，如果权重相同则按词项数量排序
					// 根据任务类型，计算任务的特定组件里的组件数量，顺承则算pred，相似则算与root不同的另一项
					// todo 满足的条件数量，需要是激活的，以总工作记忆中的状态为准
					int termCount = 0;
					Term pred = ((Statement) t.getTerm()).getPredicate();
					Term subj = ((Statement) t.getTerm()).getSubject();
					if (t.getTerm() instanceof Implication) {
						if(subj instanceof CompoundTerm){
							termCount = subj.toCompoundTerm().countTermRecursively(null).size();
						}else {
							termCount = 1;
						}
					} else if (t.getTerm() instanceof Similarity) {
						Concept predConcept = mem.concepts.get(pred.toString());
						if (predConcept.equals(root)){
							if(subj instanceof CompoundTerm){
								termCount = subj.toCompoundTerm().countTermRecursively(null).size();
							}else {
								termCount = 1;
							}
						} else {
							if(pred instanceof CompoundTerm){
								termCount = pred.toCompoundTerm().countTermRecursively(null).size();
							}else {
								termCount = 1;
							}
						}
					}
					System.out.println(t + " ---------- termCount: " + termCount);
					// 可以用更精细的计算方式替换这里的简单组合
					return termCount;
				}
			// 使用reversed()来得到降序排列，即优先级最高的在队列头部
			)
			.reversed()
		);

		// 将所有任务添加到优先队列中
		priorityQueue.addAll(generalPreconditions);
		priorityQueue.addAll(exePreconditions);

		// 处理优先队列中的最优任务，只处理一个，处理后都初始化队列，实现竞争=多选一
		if (!priorityQueue.isEmpty()) {
			build(goalTree, root, mem, priorityQueue);
		}
	}

	private void build(Tree_nest goalTree, Concept root, Memory mem, PriorityQueue<Task> priorityQueue) {
		// 取出优先级最高的任务
		Task highestPriorityTask = priorityQueue.poll();
		Term taskTerm = null;
		if (highestPriorityTask != null) {
			taskTerm = highestPriorityTask.getTerm();
		}
		Term pred = null;
		if (taskTerm != null) {
			pred = ((Statement) taskTerm).getPredicate();
		}
		Term subj = null;
		if (taskTerm != null) {
			subj = ((Statement) taskTerm).getSubject();
		}
		// 根据任务类型决定如何递归构建
		if (taskTerm instanceof Implication) {
			Concept last = mem.concepts.get(subj.toString());
			if (last != null) {
				// 将task而不是概念加入，任务包括目标部分和条件部分
				goalTree.addNode(highestPriorityTask, goalTree.root);
				recursiveBuild0(goalTree, last, mem); // 递归处理pred对应的概念
			}
		} else if (taskTerm instanceof Similarity) {
			Concept predConcept = mem.concepts.get(pred.toString());
			Concept last;
			if (predConcept.equals(root)) {
				// 如果predConcept与root相同，则选择subj
				last = mem.concepts.get(subj.toString());
			} else {
				last = predConcept;
			}
			if (last != null) {
				goalTree.addNode(last, goalTree.findNode(root).get());
				recursiveBuild0(goalTree, last, mem); // 递归处理选定词项对应的概念
			}
		}
	}

	private static void taskExecute_bf(Memory mem) {
		final Task task = mem.globalBuffer.takeOut();// 无key提取
		execute(mem, task);
	}

	private static void execute(Memory mem, Task task) {
		if (task != null) {
			if(!task.processed) {
				task.processed = true;
				mem.localInference(task, mem.narParameters, nar, mem);
			}
		}
	}
}
