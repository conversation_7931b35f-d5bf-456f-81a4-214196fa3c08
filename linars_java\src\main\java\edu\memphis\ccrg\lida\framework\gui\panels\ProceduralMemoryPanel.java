/*
 * Created by <PERSON><PERSON>ormDesigner on Sat Dec 21 08:35:29 CST 2019
 */

package edu.memphis.ccrg.lida.framework.gui.panels;

import java.awt.*;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.proceduralmemory.Condition;
import edu.memphis.ccrg.lida.proceduralmemory.ProceduralMemory;
import edu.memphis.ccrg.lida.proceduralmemory.Scheme;

import javax.swing.*;
import javax.swing.table.AbstractTableModel;
import java.text.DecimalFormat;
import java.util.Collection;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class ProceduralMemoryPanel extends GuiPanelImpl {
    public ProceduralMemoryPanel() {
        initComponents();
    }

    private static final Logger logger = Logger
            .getLogger(ProceduralMemoryPanel.class.getCanonicalName());
    private Collection<Scheme> schemes;
    private Scheme[] schemeArray = new Scheme[0];
    private ProceduralMemory module;

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents
        jToolBar1 = new JToolBar();
        refreshButton = new JButton();
        jScrollPane1 = new JScrollPane();
        schemeTable = new JTable();

        //======== this ========
        setPreferredSize(new Dimension(300, 200));

        //======== jToolBar1 ========
        {
            jToolBar1.setRollover(true);

            //---- refreshButton ----
            refreshButton.setText("Refresh");
            refreshButton.setFocusable(false);
            refreshButton.setHorizontalTextPosition(SwingConstants.CENTER);
            refreshButton.setVerticalTextPosition(SwingConstants.BOTTOM);
            refreshButton.addActionListener(e -> refreshButtonActionPerformed(e));
            jToolBar1.add(refreshButton);
        }

        //======== jScrollPane1 ========
        {

            //---- schemeTable ----
            schemeTable.setModel(new SchemeTableModel());
            schemeTable.setPreferredScrollableViewportSize(new Dimension(250, 200));
            jScrollPane1.setViewportView(schemeTable);
        }

        GroupLayout layout = new GroupLayout(this);
        setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup()
                .addComponent(jToolBar1, GroupLayout.DEFAULT_SIZE, 400, Short.MAX_VALUE)
                .addComponent(jScrollPane1, GroupLayout.DEFAULT_SIZE, 400, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createSequentialGroup()
                    .addComponent(jToolBar1, GroupLayout.PREFERRED_SIZE, 25, GroupLayout.PREFERRED_SIZE)
                    .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                    .addComponent(jScrollPane1, GroupLayout.DEFAULT_SIZE, 269, Short.MAX_VALUE))
        );
        // JFormDesigner - End of component initialization  //GEN-END:initComponents
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables
    private JToolBar jToolBar1;
    private JButton refreshButton;
    private JScrollPane jScrollPane1;
    private JTable schemeTable;
    // JFormDesigner - End of variables declaration  //GEN-END:variables

    private void refreshButtonActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_refreshButtonActionPerformed
        refresh();
    }// GEN-LAST:event_refreshButtonActionPerformed

    @Override
    public void initPanel(String[] param) {
        module = (ProceduralMemory) agent
                .getSubmodule(ModuleName.ProceduralMemory);
        if (module == null) {
            logger.log(
                    Level.WARNING,
                    "Error initializing ProceduralMemoryPanel, Module does not exist in agent.",
                    0L);
            return;
        }
    }

    @Override
    public void refresh() {
        display(module.getModuleContent("schemes"));
    }

    @SuppressWarnings("unchecked")
    @Override
    public void display(Object o) {
        schemes = (Collection<Scheme>) o;
        schemeArray = schemes.toArray(new Scheme[0]);

        ((AbstractTableModel) schemeTable.getModel()).fireTableStructureChanged();
    }

    private class SchemeTableModel extends AbstractTableModel {

        private String[] columNames = { "Scheme Label", "ID",
                "Current Activation", "Base-level Activation", "Context",
                "Action", "Adding Result", "Deleting Result" };
        private DecimalFormat df = new DecimalFormat("0.0000");

        @Override
        public int getColumnCount() {
            return columNames.length;
        }

        @Override
        public int getRowCount() {
            return schemeArray.length;
        }

        @Override
        public String getColumnName(int column) {
            if (column < columNames.length) {
                return columNames[column];
            }
            return "";
        }

        @Override
        public Object getValueAt(int rowIndex, int columnIndex) {
            if (rowIndex > schemeArray.length || rowIndex < 0
                    || columnIndex > columNames.length || columnIndex < 0) {
                return null;
            }
            Scheme scheme = schemeArray[rowIndex];
            switch (columnIndex) {
                case 0:
                    return scheme.getName();
                case 1:
                    return scheme.getId();
                case 2:
                    return df.format(scheme.getActivation());
                case 3:
                    return df.format(scheme.getBaseLevelActivation());
                case 4:
                    return generateConditionsString(scheme.getContextConditions());
                case 5:
                    return scheme.getAction().getName();
                case 6:
                    return generateConditionsString(scheme.getAddingList());
                case 7:
                    return generateConditionsString(scheme.getDeletingList());
                default:
                    return "";
            }

        }

        private String generateConditionsString(Collection<Condition> conditions){
            if(conditions == null){
                return "";
            }
            StringBuilder res = new StringBuilder();
            for(Condition c: conditions){
                res.append(c.toString());
                res.append(",");
            }
            return res.toString();
        }
    }
}
