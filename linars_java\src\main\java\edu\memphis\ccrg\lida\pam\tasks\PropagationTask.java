/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.framework.shared.Linkable;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PamLink;
import edu.memphis.ccrg.lida.pam.PamLinkable;
import edu.memphis.ccrg.lida.pam.PamNode;
import edu.memphis.ccrg.lida.pam.PAMemory;

/**
 * A task which propagates an amount of activation
 * along a {@link PamLink} to its sink.  
 *
 * <AUTHOR> J. McCall
 */
public class PropagationTask extends FrameworkTaskImpl {

	/**
	 * Link along which the excitation is being propagated.
	 */
	protected PamLink link;
	/**
	 * The sink of the link.
	 */
	protected Linkable sink;
	/**
	 * excitation amount being propagated
	 */
	protected double excitationAmount;
	private PAMemory pam;

	private int deep;

	private String from;

	/**
	 * Default constructor.
	 * param ticksPerRun task's ticks per run
	 * 
	 * param link
	 *            the link from the source to the parent
	 * param amount
	 *            the amount to excite
	 * @param pam
	 *            the pam
	 */
	public PropagationTask(int tpr, PamLink l, double a, PAMemory pam, int deep, String from) {
		super(tpr);
		link = l;
		sink = l.getSink();
		excitationAmount = a;
		this.deep = deep;
		this.pam = pam;
		this.from = from;
	}

	/**
	 * Excites the {@link PamLink} specified amount. Excites link's sink based
	 * on link's new activation. If this puts sink over its percept threshold
	 * then both Link and sink will be send as a percept. Calls
	 * with sink and finishes. 
	 */
	@Override
	protected void runThisFrameworkTask() {
//		link.exciteActivation(excitationAmount);
//		sink.exciteActivation(excitationAmount*link.getBaseLevelActivation());

		runPostExcitation();
	}

	/**
	 * Processes the link and its sink after excitation. Add link and sink to the percept if the sink is over threshold.
	 * Continues the activation propagation beyond the sink by calling propagateActivationToParents
	 * before finishing with a call to {@link #cancel()}.
	 *
	 * 激发后处理链接及其接收器。如果接收器超过阈值，则将链接和接收器添加到感知中。 *在结束对{@link #cancel（）}的调用之前，通过调用来继续激活传递到接收器之外
	 */
	protected void runPostExcitation() {
		// 不用过阈值，直接加入睡前buffer，展示done
		AddLinkToPerceptTask task = new AddLinkToPerceptTask(link, pam);
		pam.getAssistingTaskSpawner().addTask(task);
		// todo 注意力调控sink激活值，关注则提高，否则降低
//		if (pam.isOverPerceptThreshold(sink)
//				|| sink.getName().equals("ft29")
//		) {
//			String from = "想象视觉";
			pam.propagateActivationToParents((Node) sink, deep, from);
//		}

		cancel();
	}
}
