package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamLink;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
import org.jetbrains.annotations.NotNull;
import org.opennars.control.DerivationContext;
import org.opennars.entity.Task;
import org.opennars.io.Parser;

import static edu.memphis.ccrg.lida.framework.FrameworkModuleImpl.taskSpawner;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.narsese;
import static edu.memphis.ccrg.lida.pam.PAMemoryImpl.seqNs;

/**
 *
 */
public class LearnTask extends FrameworkTaskImpl {
	private PAMemory pam;
	private Link link;
	private Task task;
	private Memory mem;
	private DerivationContext nal;
	private Term[] terms;

	private boolean isDaemon = false;// 是否是守护线程，守护线程不会阻止程序的结束
	private boolean isRoot = false;// 是否是时序根节点
	private String rootNode = null;// 时序根节点
	private String source = null;// 时序节点来源，前一位节点
	private String actStamp;

	public LearnTask(Term[] terms, String rootNode, String source, String actStamp) {
		super(1, "tact");
		this.terms = terms;
		this.rootNode = rootNode;
		this.source = source;
		this.actStamp = actStamp;
	}

	public LearnTask() {
		super(1, "tact");
	}

	@Override
	protected void runThisFrameworkTask() {
		// 多种大框架，分别对应各自构建方案，关注特定标志词，细节差异，后天习得的时序构建时序
		// 纯大步骤高度概括时序，小步骤解释时序，形式抽象时序，实例时序，带参数表等时序，混合时序
		// 如方法论：方法名，可能无。方法体，可能无。参数，可能无。返回值，可能无。异常，可能无
		// 各种标志词，连词，可能无：首先，然后，最后，因为，所以，但是，如果，否则等
		// 各步骤，各模态，各类型：赋值，判断，循环，调用，返回，异常等

		// 可能有插入其他类型内容，解释性，甚至无关内容，有时需要拓展补充
		// 结合构建目标判断筛选，每一句判断：这样的可作为时序。不一定是操作，可能是陈述、概念等
		// 礼貌需求等同理。可事后判断，可直接结合最短路约搜。被动输入构建=只能事后辨别
		// 标点符号，语音无，但有停顿。在理解阶段辅助分句，这里直接拿到分句后结构

		// 直接按分句构建即可？嵌套、循环等需序号，结合变量句，理解阶段做好嵌套等准备，这里直接构建
		// 嵌套标志，$次序标志-类型1-次序1，$次序标志-类型2-次序1。同类型为同层，不同类型不同层，同层次序递增
		// nars先后现在时态即构建时序，适合具身操作，心理、语言待定，即使有后天学习图程，也无法很好执行

		// 原始输入队列，用以获取标志词后续信息，限制形式、模态、内容、对象等，如谁+说的+方法论，理解队列不一定是原始顺序
		// 结合理解池和原始队列，按原始排序。义法歧义可能造成排序混乱，各种歧义情况需附加推理
		// 次序认知也是理解一部分，时间次序、空间次序、逻辑次序等。总之分清次序并通达即可

		String mark = getMark();

		// 拼接标志词后信息，原始输入队列形式是：教你26加8+其他
		String inputQueueStr = AgentStarter.inputQueueStr;

		for(Node node : nar.memory.getNodes()){
			if (node.getTNname().contains(mark) && !node.getTNname().equals(mark)) {
				// todo 方法名等也适用变量句，后续再优化
				// 识别到关注的标志词，如果标志后信息在原始输入队列中，可开始构建
				// 标志词形式是（*，教你（*，26，加，8）），需要将标志词后信息拼接为：教你26加8，然后判断
				String attentStr = node.getStrs();
				if (inputQueueStr.contains(attentStr)){
					System.out.println("conceptNodes: " + node);
					// 截取标志词后信息，作为方法名，需实时理解哪些是方法名，哪些是其他，过滤掉其他
					// todo 识别哪些是参数，构建参数表，带符变量式描述，直接构建即可
					String methodName = "";
					CompoundTerm compoundTerm = null;
					try {
						compoundTerm = (CompoundTerm) narsese.parseTerm(node.getTNname());
					} catch (Parser.InvalidInputException e) {
						throw new RuntimeException(e);
					}
					Term[] terms = compoundTerm.getTerms();
					methodName = terms[1].toString();

//					methodName = inputQueueStr.substring(inputQueueStr.indexOf(mark) , inputQueueStr.indexOf(mark) + attentStr.length());
					// todo 新建两个节点，一是方法名，二是时序根节点，并新建相似边
					Link link = NeoUtil.mergeLink(null, "相似", methodName, "nars1111", "Verb", "Verb");

					// 如果有可能的后续嵌套时序构建，则将上位时序存入主路线，以便回溯执行。与执行分开
					seqNs.getCreateMainPath().add(link);

					if(link != null){
						// 新建时序根节点，作为时序首，需前后两标志词截取，后天+理解=得出嵌套，标志词出自构建线程内部，而不是参数输入
						CreateActRootTask0 createActRootTask = new CreateActRootTask0("nars1111",
										inputQueueStr.indexOf(attentStr) + attentStr.length(), node.getTNname(), true, actStamp);
						taskSpawner.addTask(createActRootTask);
						// todo 结束当前线程，还要维护一个嵌套栈，用以判断当前线程是否结束，结束后再继续执行上一线程
						cancel();
					}
				}
			}
		}
	}

	@NotNull
	private String getMark() {
		String mark = "";
		String mark2 = "";

		// 如（*，听到，教你）
		mark = ((PamLink)terms[0]).getSource().getTNname().split(",")[2].replace(")", "");
		return mark;
	}

}

