package edu.memphis.ccrg.lida.framework.shared.scene;

import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Term;
import org.opennars.io.Symbols;

public class CompoundTerm0 extends CompoundTerm {
    @Override
    public Symbols.NativeOperator operator() {
        return null;
    }

    @Override
    public CompoundTerm clone() {
        return null;
    }

    @Override
    public Term clone(Term[] replaced) {
        return null;
    }
}
