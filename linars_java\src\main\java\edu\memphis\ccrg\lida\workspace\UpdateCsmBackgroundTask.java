/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.workspace;

import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTask;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.framework.tasks.TaskSpawner;
import edu.memphis.ccrg.lida.globalworkspace.Coalition;
import edu.memphis.ccrg.lida.globalworkspace.CoalitionImpl;
import edu.memphis.ccrg.lida.globalworkspace.GlobalWorkspace;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * A background task in the {@link Workspace} which transfers percepts from the
 * Perceptual buffer to the Current Situational Model
 * 
 * <AUTHOR> J. McCall
 * 
 */
public class UpdateCsmBackgroundTask extends FrameworkTaskImpl {

	private static final Logger logger = Logger
			.getLogger(UpdateCsmBackgroundTask.class.getCanonicalName());

	private WorkspaceBuffer perceptualBuffer;
	private WorkspaceBuffer csm;

	private WorkspaceBuffer vison;
	private WorkspaceBuffer listen;
	private WorkspaceBuffer text;
	private WorkspaceBuffer vv;
	private WorkspaceBuffer vlisten;

	@Override
	public void setAssociatedModule(FrameworkModule module, String moduleUsage) {
		if (module instanceof Workspace) {
			perceptualBuffer = (WorkspaceBuffer) module
					.getSubmodule(ModuleName.PerceptualBuffer);
			csm = (WorkspaceBuffer) module.getSubmodule(ModuleName.CurrentSM);

			vison = (WorkspaceBuffer) module.getSubmodule(ModuleName.VisionGraph);
			listen = (WorkspaceBuffer) module.getSubmodule(ModuleName.ListenGraph);
			text = (WorkspaceBuffer) module.getSubmodule(ModuleName.TextGraph);
			vv = (WorkspaceBuffer) module.getSubmodule(ModuleName.VVGraph);
			vlisten = (WorkspaceBuffer) module.getSubmodule(ModuleName.VListenGraph);
		}
	}

	/**
	 * Retrieves nodes from PAM and provides them to attentional codelets. This
	 * function gets PAM's nodes and provides them to CurrentSituationalModel,
	 * which will be accessed by attentional codelets.
	 * 从PAM检索节点，并将其提供给注意代码。 *该函数获取PAM的节点并将其提供给 当前情景模型*，注意小码将访问该节点
	 */
	@Override
	protected void runThisFrameworkTask() {
		if (logger.isLoggable(Level.FINEST)) {
			logger.log(Level.FINEST, "Updating CSM with perceptual buffer content.",
					TaskManager.getCurrentTick());
		}
		// todo csm做广播子图，由pb转入二次加工模块，然后输出到csm，如语言
		// pb需要多个副本，代表多个模态，独立进行
//		csm.addBufferContent(perceptualBuffer.getBufferContent(null));

		// todo 环境信息，场景预测，当前场景缓存，意识链，关系都是图像，符号没有
		//  显式描述，隐式沉淀，全隐式=场景框架+语义，全显式=案例实例+约定搭配
//		WorkspaceBuffer csm = (WorkspaceBuffer) ((WorkspaceImpl) KgmakerApplication.pam
//				.getListener()).getSubmodule(ModuleName.CurrentSituationalModel);

		// todo 场景、意图、目标
		GlobalWorkspace globalWorkspace = AgentStarter.pam.getGlobalWorkspace();
		// 无意识提交的报告，生产消费模式=这里充当通道桥梁
		// 必须以完整场景为准？缺失要素都不能通过阈值，如果没有备选则新建场景
		// 要素齐备=动作+施受事，可能仅有施事，被动+不确定施事=某人 + 动作 + 某物，对空气做动作
		// 其他非核心要素，如时间、地点、程度、工具、方式等，还有属性的属性，
		// 时间等属性导致不同结果=与常规场景整合，共同约束后面场景，在某些场景是核心要素？

		// 持续场景 + 场景转换 = 事件分割，受未衰减元素影响，近期事件影响也更大
		// 按帧=每帧的效果类似=分子机制和刺激元素类似，情感衰减率更低=持续更久
		// 体验+想到+自传回忆+假装场景，情感独立=需要多一层联动，整合到场景=激活下一层都是一度
		// 实时情感=实时动机？场景激发情感=其实是每次都实时判断价值？当前场景情感价值=不必要实时
		NodeStructure csmContent = csm.getBufferContent(null);

		// 脑内电影=多个模态同时存在？伪同时=快速切换？动机调控=可真并行同时，无明显动机则单模态
		// 即使是快切也是动机驱动，无动机真并行=无筛选意义，无动机伪并行=无法沉浸，注意力缺陷
		// 先识别出具体的概念的模态属性，再参与筛选，之后进行更深的理解和模态交互，模态整合
		// 猴子概念，置信度=确定程度，情感激活=危险程度+新鲜程度，情感调制滞后于各模态=需要多周期？
		NodeStructure visonNs = vison.getBufferContent(null);
		NodeStructure listenNs = listen.getBufferContent(null);
		NodeStructure textNs = text.getBufferContent(null);
		NodeStructure vvNs = vv.getBufferContent(null);
		NodeStructure vlistenNs = vlisten.getBufferContent(null);

//		payattent(visonNs, listenNs, textNs, vvNs, vlistenNs);

		if(csmContent == null){
			logger.log(Level.WARNING, "Null WorkspaceContent returned in {1}. Coalition cannot be formed.",
					new Object[]{TaskManager.getCurrentTick(), this});
		}else if (csmContent.getLinkableCount() > 0) {
			// 本来是注意小码联盟，报告与否+筛选与否，筛选是增强大于抑制？只是辅助=不能阻塞？
			// 毫秒级的阻塞=难以觉察，形成流水线=也几乎等于不阻塞？pam到广播到pam？
			Coalition coalition = new CoalitionImpl(csmContent);
			// 多线程汇总交互？
			globalWorkspace.addCoalition(coalition);
			logger.log(Level.FINER, "{1} adds new coalition with activation {2}",
					new Object[]{TaskManager.getCurrentTick(), this, coalition.getActivation()});
			setNextTicksPerRun(8);
		}
	}

	// todo 模态不全、不用省算力，暂时调控没意义，但拆分task有意义
	private void payattent(NodeStructure visonNs, NodeStructure listenNs, NodeStructure textNs, NodeStructure vvNs, NodeStructure vlistenNs) {
		double vact = 0.0;
		double lact = 0.0;
		double vvact = 0.0;
		double tact = 0.0;
		double vlact = 0.0;

		// 根据各模态总激活度算最大值，按当前激活，而非初始加入的激活
		// 因需要统计对比各模态，每个都要算，而不是只算被选模态，被选也是对比出来的
		for (Node n: visonNs.getNodes()) {
			vact  += n.getActivation();
		}
		for (Node n: listenNs.getNodes()) {
			lact  += n.getActivation();
		}
		for (Node n: textNs.getNodes()) {
			vvact  += n.getActivation();
		}
		for (Node n: vvNs.getNodes()) {
			tact  += n.getActivation();
		}
		for (Node n: vlistenNs.getNodes()) {
			vlact  += n.getActivation();
		}

		Map<String,NodeStructure> mtnsmap = new HashMap<>();
		mtnsmap.put("vact", visonNs);
		mtnsmap.put("lact", listenNs);
		mtnsmap.put("vvact", vvNs);
		mtnsmap.put("tact", textNs);
		mtnsmap.put("vlact", vlistenNs);

		// 模态总激活与模态名绑定，切换当前注意模态。常规线程也能临时绑定模态，表示内容来源
		Map<String,Double> mtmap = new HashMap<>();
		mtmap.put("vact",vact);
		mtmap.put("lact",lact);
		mtmap.put("vvact",vvact);
		mtmap.put("tact",tact);
		mtmap.put("vlact",vlact);

		double maxActivation = -1.0D;
		String fianlmotai = "";

		for (String motai: mtmap.keySet()) {
			double act = mtmap.get(motai);
			if (act > maxActivation) {
				maxActivation = act;
				fianlmotai = motai;
			}
		}
		AgentStarter.currenMotai = fianlmotai;
		AgentStarter.currenAttenNum ++;

		// 开始控制线程阶段，当前注意阶段不允许新线程进入
		AgentStarter.isAttentNow = true;
		TaskSpawner tasksp = AgentStarter.pam.getAssistingTaskSpawner();

		// 筛选后，直接中断被抛弃线程？没有情不自禁+意外，线程内扬抑难做到=需要大量埋点,还要设计各模态抑制程度
		// 情不自禁也只是多周期问题，并非当前周期问题？当前动机可抑制中断，下一周期被现状反超=有缓存+非突变
		// 无法做到神经元级别控制，复杂线程内外协调=扬扬+扬抑+抑扬+抑抑，要做也是以后！
		Collection<FrameworkTask> tasks = tasksp.getTasks();
		for (FrameworkTask task : tasks) {
			// 非兴奋模态，且非常驻线程，则直接抑制中断
			if (!task.getType().equals(fianlmotai) && !task.getType().equals("normal")) {
//				task.getTaskId()
			}
//			tasksp.cancelTask()
		}

		// 结束控制线程阶段，允许线程进出，周期性控制
		AgentStarter.isAttentNow = false;

		// 加入WM和自我，需要过阈值和可意识模态转化
	}
}
