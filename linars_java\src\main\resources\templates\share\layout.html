<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
	xmlns:layout="http://www.ultraq.net.nz/web/thymeleaf/layout">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" /> 
<title>首页</title>
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="viewport" content="width=device-width, initial-scale=1" />

<script type="text/javascript">
	var contextRoot = "[[@{/}]]";
</script>
<!-- import css -->
<link rel="stylesheet" th:href="@{/css/index0.css}">
<link rel="stylesheet" th:href="@{/css/x-index.css}">

<style type="text/css">
[v-cloak] {
	display: none !important;
}
</style>

<script th:src="@{/js/jquery.min.js}"></script>
<script th:src="@{/js/iconfont.js}"></script>
<script th:src="@{/js/vue.js}" src="https://cdn.bootcss.com/vue/2.5.16/vue.js"></script>
<script th:src="@{/js/index.js}"></script>
</head>
<body>
	<div>
		<!-- <div class="index"> -->
		<!-- 页头 -->
		<div id='appheader' v-cloak  th:include="share/header::header"></div>
		<!-- 正文内容 -->
		<div id='app' v-cloak layout:fragment="content"></div>
		<!-- 页脚 -->
		<!-- <div th:include="/share/footer::footer"></div> -->
		<!-- </div> -->
	</div>
	<script th:inline="javascript" type="text/javascript">
	 var appheader=  new Vue({
		    el: '#appheader',
		    data:{
		    	currentUrl:[[${#request.getRequestURI()}]],//当前路由
		    	navList:[
		    		{
		    			title:'首页',icon:'glyphicon glyphicon-cog',linkUrl:'http://www.miaoleyan.com',active:false,childrens:[]
		    		},
		    		{
		    			title:'分享',icon:'glyphicon glyphicon-th-list',linkUrl:'javascript:void(0);',active:false,childrens:[
		    				{title:'杨青博客',icon:'',linkUrl:'http://www.yangqq.com/',active:false,childrens:[]},
		    				{title:'程序猿DD',icon:'',linkUrl:'http://blog.didispace.com/',active:false,childrens:[]},
		    				{title:'hAdmin',icon:'',linkUrl:'http://demo.mycodes.net/houtai/hAdmin',active:false,childrens:[]},
		    			]
		    		}
		    	],
		    	isOpen:false,
		    	search_active:false,
		    	searchkeyword:'',
		      },
		    methods: {
		    	selectStyle(nav){
		    		var _this=this;
		            this.$nextTick(function () {
		            	_this.navList.forEach(function (item) {
		            		item.active=false;
		              });
		            	nav.active=true;
		            });
		    	},
		    	outStyle(nav) {
		    		nav.active=false;
	            },
	            changeIcon(){
	        	  this.isOpen=!this.isOpen;
	            },
	            searchActive() {
	            	 this.search_active=!this.search_active;
	            },
	            clickNav(nav){
		          nav.active=!nav.active;
		    	}
	        }
		 })
 
     </script>
</body>
<!-- JS内容 -->
<div layout:fragment="jscontent"></div>
</html>