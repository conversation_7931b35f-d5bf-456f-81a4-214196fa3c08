/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.storage;

import com.google.common.collect.MinMaxPriorityQueue;
import edu.memphis.ccrg.linars.Concept;
import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
import org.opennars.entity.Item;
import org.opennars.entity.Task;
import org.opennars.inference.BudgetFunctions;

import java.io.Serializable;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class Bag1<V extends Item<K>, K> implements Serializable, Iterable<V> {
    // todo 有序但无结构，有结构但无序，两者都要
    public final MinMaxPriorityQueue<V> queue;
    public final Map<String,V> theMap;
    final int maxSize;
    public Bag1(int maxSize) {
        this.maxSize = maxSize;
        theMap = new HashMap<>();
        // 优先级队列，默认升序，最大容量为maxSize，加入元素时，如果队列已满，则移除优先级最低的元素
        // todo 整合ns？既要衰减，又要排序，还要投票
        queue = MinMaxPriorityQueue
                .orderedBy(Comparator.comparing(V::getPriority))
                .create();
    }

    public V putIn(V item) {
        synchronized (queue) {
            V displaced = null;
//            if(queue.size() >= maxSize) {
//                V itemRemove = queue.removeFirst();
//                displaced = itemRemove;
//            }
            // 加到队列尾部
            queue.add(item);
//            if (theMap.size() >= maxSize) {
//                theMap.remove(queue.peekFirst().name().toString());
//            }
            theMap.put(item.name().toString(), item);
            return displaced;
        }
    }
    
    public V get(String key) {
        synchronized (theMap) {
            return theMap.getOrDefault(key, null);
        }
    }
    // 根据词项找任务等
    public V getByTerm(String key) {
        synchronized (theMap) {
            for (V v : theMap.values()) {
                if (((Task)v).getTerm().toString().equals(key)) {
                    return v;
                }
            }
        }
        return null;
    }

//    public V pickOut(String key) {
////        return get(key);
//        return takeOut(key);
//    }

    public V pickOut(String key) {
        synchronized (queue) {
            if (theMap.containsKey(key)) {
                queue.remove(theMap.get(key));
                V ret = theMap.get(key);
//                theMap.remove(key);
                return ret;
            }
            return null;
        }
    }
    
    public V takeHighestPriorityItem() {
        synchronized (queue) {
            if (queue.isEmpty()) {
                return null;
            }
//            return queue.pollFirst();
            return queue.pollLast();// 添加在队列尾部（0为尾，反过来的），移除时从头部移除
        }
    }
    
    public Iterator iterator() {
        synchronized (queue) {
            return queue.iterator();
        }
    }
    
    public V putBack(final V oldItem, final float forgetCycles, final Memory m) {
//        if(oldItem instanceof Concept){
//            Concept c = (Concept) oldItem;
//            Term t = c.getTerm();
//            if (t.toString().equals("<SELF --> [happy]>")){
//                System.out.println("putBack ------ " + t.toString());
//            }
//        }

        final float relativeThreshold = 0.1f; //m.narParameters.QUALITY_RESCALED
        BudgetFunctions.applyForgetting(oldItem.budget, forgetCycles, relativeThreshold);
        return putIn(oldItem);
    }
    
    public boolean isEmpty() {
        return this.queue.isEmpty();
    }

    public void clear() {
        this.queue.clear();
        this.theMap.clear();
    }

    public float getAveragePriority() {
        synchronized (queue) {
            float sum = 0;
            for (V v : queue) {
                sum += v.getPriority();
            }
            return sum / queue.size();
        }
    }

    public int nameSize() {
        synchronized (queue) {
            return queue.size();
        }
    }

    public int itemSize() {
        synchronized (theMap) {
            return theMap.size();
        }
    }

    public V takeOut(){
        return takeHighestPriorityItem();
    }

    @Override
    public String toString() {
        StringBuffer buf = new StringBuffer(" ");
        synchronized (queue) {
            for (V v : queue) {
                buf.append(v.toString());
                buf.append(" ");
            }
        }
        return buf.toString();
    }
}
