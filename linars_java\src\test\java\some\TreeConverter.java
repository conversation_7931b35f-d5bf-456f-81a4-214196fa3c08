package some;

import cn.hutool.json.JSONArray;

import java.util.Stack;

public class TreeConverter {

    public static void main1(String[] args) {
        String treeString = "(VP (VV 召开))";
        JSONArray jsonArray = convertTreeToJSON0(treeString);
        System.out.println(jsonArray.toString());
    }

    public static JSONArray convertTreeToJSON2(String treeString) {
        // 去掉所有\n
        treeString = treeString.replaceAll("\\n", "");
        JSONArray rootArray = new JSONArray();
        JSONArray currentArray = rootArray;
        StringBuilder currentToken = new StringBuilder();
        Stack<JSONArray> parentStack = new Stack<>(); // 使用Stack替代JSONArray作为栈

        for (char c : treeString.toCharArray()) {
            switch (c) {
                case '(':
                    if (currentToken.length() > 0) {
                        currentArray.put(currentToken.toString());
                        currentToken = new StringBuilder();
                    }
                    JSONArray newArray = new JSONArray();
                    currentArray.put(newArray);
                    parentStack.push(currentArray);
                    currentArray = newArray;
                    break;
                case ')':
                    if (currentToken.length() > 0) {
                        currentArray.put(currentToken.toString());
                        currentToken = new StringBuilder();
                    }
                    if (!parentStack.isEmpty()) {
                        currentArray = parentStack.pop();
                    }
                    break;
                case ' ':
                    if (currentToken.length() > 0) {
                        currentArray.put(currentToken.toString());
                        currentToken = new StringBuilder();
                    }
                    break;
                default:
                    currentToken.append(c);
                    break;
            }
        }

        return rootArray;
    }


    public static JSONArray convertTreeToJSON0(String treeString) {
        // 转换以下格式不对："(CP (CP (IP (VP (NP (NT 2005年) (NT 1月)) (PP (P 在) (NP (NR 瑞士) (NR 达沃斯)))) (VP (VV 召开))))";

        // 去掉所有\n
        treeString = treeString.replaceAll("\\n", "");
        JSONArray rootArray = new JSONArray();
        JSONArray currentArray = rootArray;
        StringBuilder currentToken = new StringBuilder();
        int depth = 0;
        JSONArray parentStack = new JSONArray(); // 用于存储上一级的数组

        for (char c : treeString.toCharArray()) {
            switch (c) {
                case '(':
                    if (currentToken.length() > 0) {
                        currentArray.put(currentToken.toString());
                        currentToken = new StringBuilder();
                    }
                    JSONArray newArray = new JSONArray();
                    currentArray.put(newArray);
                    parentStack.put(currentArray);
                    currentArray = newArray;
                    depth++;
                    break;
                case ')':
                    if (currentToken.length() > 0) {
                        currentArray.put(currentToken.toString());
                        currentToken = new StringBuilder();
                    }
                    depth--;
                    if (depth > 0) {
                        currentArray = (JSONArray) parentStack.get(parentStack.size() - depth);
                        parentStack.remove(parentStack.size() - 1);
                    }
                    break;
                case ' ':
                    if (depth > 0 && currentToken.length() > 0) {
                        currentArray.put(currentToken.toString());
                        currentToken = new StringBuilder();
                    }
                    break;
                default:
                    currentToken.append(c);
                    break;
            }
        }

        return rootArray;
    }

    public static JSONArray convertTreeToJSON(String treeString) {
        JSONArray currentArray = new JSONArray();
        StringBuilder token = new StringBuilder();
        int depth = 0;

        for (char c : treeString.toCharArray()) {
            switch (c) {
                case '(':
                    if (token.length() > 0) {
                        JSONArray newArray = new JSONArray();
                        newArray.put(token.toString());
                        currentArray.put(newArray);
                        currentArray = newArray;
                        token = new StringBuilder();
                    }
                    depth++;
                    break;
                case ')':
                    depth--;
                    if (token.length() > 0) {
                        currentArray.put(token.toString());
                        token = new StringBuilder();
                    }
                    if (depth == 0) {
                        break;
                    }
                    currentArray = (JSONArray) currentArray.get(currentArray.size() - 2);
                    break;
                case ' ':
                    if (depth == 1 && token.length() > 0) {
                        currentArray.put(token.toString());
                        token = new StringBuilder();
                    }
                    break;
                default:
                    token.append(c);
                    break;
            }
        }

        return currentArray;
    }


    public static void main(String[] args) {
        // 调整后的树状结构字符串
        String treeString = "(CP (CP (IP (VP (NP (NT 2005年) (NT 1月)) (PP (P 在) (NP (NR 瑞士) (NR 达沃斯)))) (VP (VV 召开))))";

        // 使用 convertTreeToJSON 方法进行转换
        JSONArray jsonArray = convertTreeToJSON2(treeString);

        // 打印转换后的 JSON 数组
        System.out.println(jsonArray.toString());
    }

    public static JSONArray convertTreeToJSON1(String treeString) {
        JSONArray currentArray = new JSONArray();
        StringBuilder token = new StringBuilder();
        int depth = 0;

        for (char c : treeString.toCharArray()) {
            switch (c) {
                case '(':
                    if (token.length() > 0) {
                        JSONArray newArray = new JSONArray();
                        newArray.put(token.toString());
                        currentArray.put(newArray);
                        currentArray = newArray;
                        token = new StringBuilder();
                    }
                    depth++;
                    break;
                case ')':
                    depth--;
                    if (token.length() > 0) {
                        currentArray.put(token.toString());
                        token = new StringBuilder();
                    }
                    if (depth == 0) {
                        break;
                    }
                    currentArray = (JSONArray) currentArray.get(currentArray.size() - 2);
                    break;
                case ' ':
                    // 忽略空格，不进行处理
                    break;
                default:
                    token.append(c);
                    break;
            }
        }

        // 如果最后一个元素是 StringBuilder，则将其转换为 String 并添加到数组中
        if (token.length() > 0) {
            currentArray.put(token.toString());
        }

        return currentArray;
    }


}
