package schema.execution;

import java.util.ArrayList;
import java.util.List;

/**
 * 执行结果，包含执行的结果值、执行路径和执行状态
 */
public class ExecutionResult {
    private Object result;
    private boolean success;
    private String errorMessage;
    private List<String> executionPath = new ArrayList<>();
    private long executionTime;
    private long memoryUsage;

    public ExecutionResult(Object result, boolean success) {
        this.result = result;
        this.success = success;
    }

    public ExecutionResult(Object result, boolean success, String errorMessage) {
        this.result = result;
        this.success = success;
        this.errorMessage = errorMessage;
    }

    public Object getResult() {
        return result;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public List<String> getExecutionPath() {
        return executionPath;
    }

    public void addToExecutionPath(String step) {
        executionPath.add(step);
    }

    public long getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(long executionTime) {
        this.executionTime = executionTime;
    }

    public long getMemoryUsage() {
        return memoryUsage;
    }

    public void setMemoryUsage(long memoryUsage) {
        this.memoryUsage = memoryUsage;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("ExecutionResult:\n");
        sb.append("Success: ").append(success).append("\n");
        sb.append("Result: ").append(result).append("\n");
        if (errorMessage != null) {
            sb.append("Error: ").append(errorMessage).append("\n");
        }
        sb.append("Execution Time: ").append(executionTime).append(" ms\n");
        sb.append("Memory Usage: ").append(memoryUsage).append(" bytes\n");
        sb.append("Execution Path: ").append("\n");
        for (String step : executionPath) {
            sb.append("  ").append(step).append("\n");
        }
        return sb.toString();
    }
}
