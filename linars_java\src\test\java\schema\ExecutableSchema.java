package schema;

import schema.model.Edge;
import schema.model.Node;
import schema.model.nodes.ContextNode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 可执行图式，表示一个完整的可执行图结构
 */
public class ExecutableSchema {
    private String id;
    private String name;
    private ContextNode contextNode;
    private List<Node> nodes = new ArrayList<>();
    private List<Edge> edges = new ArrayList<>();
    private Map<String, Node> nodeMap = new HashMap<>();
    private Map<String, List<Edge>> outgoingEdges = new HashMap<>();
    private Map<String, List<Edge>> incomingEdges = new HashMap<>();

    public ExecutableSchema(String id, String name, ContextNode contextNode) {
        this.id = id;
        this.name = name;
        this.contextNode = contextNode;
        addNode(contextNode);
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public ContextNode getContextNode() {
        return contextNode;
    }

    public void addNode(Node node) {
        if (!nodeMap.containsKey(node.getId())) {
            nodes.add(node);
            nodeMap.put(node.getId(), node);
            outgoingEdges.put(node.getId(), new ArrayList<>());
            incomingEdges.put(node.getId(), new ArrayList<>());
        }
    }

    public void addEdge(Edge edge) {
        edges.add(edge);
        outgoingEdges.get(edge.getSource().getId()).add(edge);
        incomingEdges.get(edge.getTarget().getId()).add(edge);
    }

    public Node getNode(String id) {
        return nodeMap.get(id);
    }

    public List<Node> getNodes() {
        return nodes;
    }

    public List<Edge> getEdges() {
        return edges;
    }

    public List<Edge> getOutgoingEdges(Node node) {
        return outgoingEdges.get(node.getId());
    }

    public List<Edge> getIncomingEdges(Node node) {
        return incomingEdges.get(node.getId());
    }

    public List<Edge> getOutgoingEdges(Node node, String edgeType) {
        List<Edge> result = new ArrayList<>();
        for (Edge edge : outgoingEdges.get(node.getId())) {
            if (edge.getEdgeType().equals(edgeType)) {
                result.add(edge);
            }
        }
        return result;
    }

    public List<Edge> getIncomingEdges(Node node, String edgeType) {
        List<Edge> result = new ArrayList<>();
        for (Edge edge : incomingEdges.get(node.getId())) {
            if (edge.getEdgeType().equals(edgeType)) {
                result.add(edge);
            }
        }
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("ExecutableSchema: ").append(name).append("\n");
        sb.append("Context: ").append(contextNode).append("\n");
        sb.append("Nodes: ").append(nodes.size()).append("\n");
        sb.append("Edges: ").append(edges.size()).append("\n");
        return sb.toString();
    }
}
