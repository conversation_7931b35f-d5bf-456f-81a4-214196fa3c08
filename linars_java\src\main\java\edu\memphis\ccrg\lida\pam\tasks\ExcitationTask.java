/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import java.util.logging.Level;
import java.util.logging.Logger;

import edu.memphis.ccrg.lida.framework.tasks.FrameworkTask;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PamLinkable;
import edu.memphis.ccrg.lida.pam.PamNode;
import edu.memphis.ccrg.lida.pam.PAMemory;

/**
 * A task which performs the excitation of a single {@link PamNode}.
 * 
 * see PAMemory#receiveExcitation(PamLinkable, double)
 * 
 * <AUTHOR> J. McCall
 * 
 */
public class ExcitationTask extends FrameworkTaskImpl {

	private static final Logger logger = Logger.getLogger(ExcitationTask.class
			.getCanonicalName());

	// PamNode to be excited
	private PamNode node;

	//Amount to excite
	private double excitationAmount;

	// Used to make another excitation call
	private PAMemory pam;

	private String from = "";

	/**
	 * Instantiates a new excitation task to excite supplied {@link PamNode}
	 * specified amount.
	 * 
	 * @param ticksPerRun
	 *            the ticks per run
	 * @param n
	 *            to be excited
	 * @param excitation
	 *            amount to excite
	 * @param pam
	 *            PerceptualAssociativeMemory module
	 */
	public ExcitationTask(int ticksPerRun, PamNode n, double excitation, PAMemory pam, String from) {
		super(ticksPerRun);
		node = n;
		excitationAmount = excitation;
		this.pam = pam;
		this.from = from;
	}

	/**
	 * This method first excites the {@link PamNode}, if this puts the {@link PamNode}
	 * over the percept threshold it creates an {@link AddNodeToPerceptTask} to
	 * add it to the percept. In either case it calls
	 * to pass the node's activation, then the tasks finishes.
	 */
	@Override
	protected void runThisFrameworkTask() {
		// 外感知激活=点基础值*乘积，累积在pam联动里，点边累积统一
//		node.setActivation(node.getActivation()*excitationAmount);
//		node.exciteActivation(excitationAmount);

//		System.out.println(node.getName() + " +++++ act ++++++++" + node.getActivation());

//		if (pam.isOverPerceptThreshold(node)) {
//			if (logger.isLoggable(Level.FINEST)) {
//				logger.log(Level.FINEST, "PamNode {1} over threshold",
//						new Object[] { TaskManager.getCurrentTick(), node });
//			}
			// 不用过阈值，直接加入睡前buffer，展示done。这里加入的是pamnode
			FrameworkTask task = new AddNodeToPerceptTask(node, pam);
			pam.getAssistingTaskSpawner().addTask(task);
//		}
		// 这里只要node就行，实际是pamnode（中期记忆），而接下来的联动是node
		pam.propagateActivationToParents(node,0, from);
		cancel();
	}
}