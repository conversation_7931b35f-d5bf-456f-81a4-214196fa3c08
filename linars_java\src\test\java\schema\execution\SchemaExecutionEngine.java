package schema.execution;

import schema.ExecutableSchema;
import test.schema.model.Edge;
import test.schema.model.Node;
import test.schema.model.edges.ConditionalEdge;
import test.schema.model.edges.LoopEdge;
import test.schema.model.edges.SequenceEdge;
import test.schema.model.edges.SuccessionEdge;
import test.schema.model.nodes.ControlNode;
import test.schema.model.nodes.OperationNode;

import java.util.List;
import java.util.Map;

/**
 * 图式执行引擎，负责执行可执行图式
 */
public class SchemaExecutionEngine {
    /**
     * 执行图式
     * @param schema 可执行图式
     * @param parameters 输入参数
     * @return 执行结果
     */
    public ExecutionResult execute(ExecutableSchema schema, Map<String, Object> parameters) {
        long startTime = System.currentTimeMillis();
        long startMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        
        try {
            // 创建执行上下文
            ExecutionContext context = new ExecutionContext();
            
            // 设置输入参数
            for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                context.setVariable(entry.getKey(), entry.getValue());
            }
            
            // 获取起始节点（上下文节点）
            Node contextNode = schema.getContextNode();
            
            // 查询时序首节点
            Node startNode = getFirstSequenceNode(schema, contextNode);
            if (startNode == null) {
                return new ExecutionResult(null, false, "找不到时序首节点");
            }
            
            // 执行图式
            Object result = executeNode(startNode, schema, context);
            
            // 计算执行时间和内存使用
            long endTime = System.currentTimeMillis();
            long endMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
            
            // 创建执行结果
            ExecutionResult executionResult = new ExecutionResult(result, true);
            executionResult.setExecutionTime(endTime - startTime);
            executionResult.setMemoryUsage(endMemory - startMemory);
            
            return executionResult;
        } catch (Exception e) {
            // 处理执行异常
            long endTime = System.currentTimeMillis();
            long endMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
            
            ExecutionResult errorResult = new ExecutionResult(null, false, e.getMessage());
            errorResult.setExecutionTime(endTime - startTime);
            errorResult.setMemoryUsage(endMemory - startMemory);
            
            return errorResult;
        }
    }
    
    /**
     * 获取时序首节点
     * @param schema 可执行图式
     * @param contextNode 上下文节点
     * @return 时序首节点
     */
    private Node getFirstSequenceNode(ExecutableSchema schema, Node contextNode) {
        List<Edge> edges = schema.getOutgoingEdges(contextNode);
        for (Edge edge : edges) {
            if (edge instanceof SequenceEdge && ((SequenceEdge) edge).isFirst()) {
                return edge.getTarget();
            }
        }
        return null;
    }
    
    /**
     * 执行节点
     * @param node 当前节点
     * @param schema 可执行图式
     * @param context 执行上下文
     * @return 执行结果
     */
    private Object executeNode(Node node, ExecutableSchema schema, ExecutionContext context) {
        // 根据节点类型执行不同的操作
        String nodeType = node.getNodeType();
        
        if ("OperationNode".equals(nodeType)) {
            // 执行操作节点
            OperationNode operationNode = (OperationNode) node;
            String operationName = operationNode.getName();
            
            // 记录执行路径
            System.out.println("执行操作: " + operationName);
            
            // 获取并执行操作
            Operation operation = OperationRegistry.getOperation(operationName);
            if (operation == null) {
                throw new RuntimeException("找不到操作: " + operationName);
            }
            
            Object result = operation.execute(context);
            
            // 获取下一个节点（通过顺承边）
            Node nextNode = getNextNode(schema, node);
            if (nextNode != null) {
                return executeNode(nextNode, schema, context);
            }
            
            return result;
        } else if ("ControlNode".equals(nodeType)) {
            // 处理控制节点
            ControlNode controlNode = (ControlNode) node;
            String controlType = controlNode.getControlType();
            
            if ("loop".equals(controlType)) {
                // 处理循环结构
                return executeLoopNode(controlNode, schema, context);
            } else if ("conditional".equals(controlType)) {
                // 处理条件结构
                return executeConditionalNode(controlNode, schema, context);
            } else {
                throw new RuntimeException("不支持的控制类型: " + controlType);
            }
        } else {
            throw new RuntimeException("不支持的节点类型: " + nodeType);
        }
    }
    
    /**
     * 执行循环节点
     * @param loopNode 循环节点
     * @param schema 可执行图式
     * @param context 执行上下文
     * @return 执行结果
     */
    private Object executeLoopNode(ControlNode loopNode, ExecutableSchema schema, ExecutionContext context) {
        // 获取循环体节点（通过时序边）
        List<Node> bodyNodes = getSequenceNodes(schema, loopNode);
        
        // 循环执行
        boolean continueLoop = true;
        while (continueLoop) {
            // 执行循环体
            for (Node bodyNode : bodyNodes) {
                executeNode(bodyNode, schema, context);
            }
            
            // 检查循环条件
            continueLoop = checkLoopCondition(schema, loopNode, context);
            
            // 记录执行路径
            System.out.println("循环条件检查: " + (continueLoop ? "继续循环" : "结束循环"));
        }
        
        // 获取循环后的下一个节点（通过顺承边）
        Node nextNode = getNextNode(schema, loopNode);
        if (nextNode != null) {
            return executeNode(nextNode, schema, context);
        }
        
        return null;
    }
    
    /**
     * 执行条件节点
     * @param conditionalNode 条件节点
     * @param schema 可执行图式
     * @param context 执行上下文
     * @return 执行结果
     */
    private Object executeConditionalNode(ControlNode conditionalNode, ExecutableSchema schema, ExecutionContext context) {
        // 评估条件
        boolean condition = evaluateCondition(conditionalNode.getCondition(), context);
        
        // 记录执行路径
        System.out.println("条件评估: " + conditionalNode.getCondition() + " = " + condition);
        
        // 获取对应分支
        Node branchNode = condition ? 
                          getThenBranchNode(schema, conditionalNode) : 
                          getElseBranchNode(schema, conditionalNode);
        
        if (branchNode != null) {
            return executeNode(branchNode, schema, context);
        }
        
        return null;
    }
    
    /**
     * 获取顺序节点
     * @param schema 可执行图式
     * @param node 当前节点
     * @return 顺序节点列表
     */
    private List<Node> getSequenceNodes(ExecutableSchema schema, Node node) {
        List<Edge> edges = schema.getOutgoingEdges(node, "时序");
        List<Edge> firstEdges = schema.getOutgoingEdges(node, "时序首");
        
        // 合并所有时序边
        edges.addAll(firstEdges);
        
        // 提取目标节点
        return edges.stream()
                .map(Edge::getTarget)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取下一个节点
     * @param schema 可执行图式
     * @param node 当前节点
     * @return 下一个节点
     */
    private Node getNextNode(ExecutableSchema schema, Node node) {
        List<Edge> edges = schema.getOutgoingEdges(node, "顺承");
        if (!edges.isEmpty()) {
            return edges.get(0).getTarget();
        }
        return null;
    }
    
    /**
     * 获取then分支节点
     * @param schema 可执行图式
     * @param conditionalNode 条件节点
     * @return then分支节点
     */
    private Node getThenBranchNode(ExecutableSchema schema, Node conditionalNode) {
        List<Edge> edges = schema.getOutgoingEdges(conditionalNode);
        for (Edge edge : edges) {
            if (edge instanceof ConditionalEdge && ((ConditionalEdge) edge).isThen()) {
                return edge.getTarget();
            }
        }
        return null;
    }
    
    /**
     * 获取else分支节点
     * @param schema 可执行图式
     * @param conditionalNode 条件节点
     * @return else分支节点
     */
    private Node getElseBranchNode(ExecutableSchema schema, Node conditionalNode) {
        List<Edge> edges = schema.getOutgoingEdges(conditionalNode);
        for (Edge edge : edges) {
            if (edge instanceof ConditionalEdge && !((ConditionalEdge) edge).isThen()) {
                return edge.getTarget();
            }
        }
        return null;
    }
    
    /**
     * 检查循环条件
     * @param schema 可执行图式
     * @param loopNode 循环节点
     * @param context 执行上下文
     * @return 是否继续循环
     */
    private boolean checkLoopCondition(ExecutableSchema schema, Node loopNode, ExecutionContext context) {
        // 查找循环条件边
        for (Edge edge : schema.getIncomingEdges(loopNode)) {
            if (edge instanceof LoopEdge) {
                LoopEdge loopEdge = (LoopEdge) edge;
                return evaluateCondition(loopEdge.getCondition(), context);
            }
        }
        return false;
    }
    
    /**
     * 评估条件
     * @param condition 条件表达式
     * @param context 执行上下文
     * @return 条件结果
     */
    private boolean evaluateCondition(String condition, ExecutionContext context) {
        if (condition == null || condition.isEmpty()) {
            return false;
        }
        
        // 简单条件评估，支持基本比较操作
        if (condition.contains(">=")) {
            String[] parts = condition.split(">=");
            String leftVar = parts[0].trim();
            String rightVar = parts[1].trim();
            
            Object leftValue = getVariableValue(leftVar, context);
            Object rightValue = getVariableValue(rightVar, context);
            
            if (leftValue instanceof Integer && rightValue instanceof Integer) {
                return (Integer) leftValue >= (Integer) rightValue;
            }
        } else if (condition.contains(">")) {
            String[] parts = condition.split(">");
            String leftVar = parts[0].trim();
            String rightVar = parts[1].trim();
            
            Object leftValue = getVariableValue(leftVar, context);
            Object rightValue = getVariableValue(rightVar, context);
            
            if (leftValue instanceof Integer && rightValue instanceof Integer) {
                return (Integer) leftValue > (Integer) rightValue;
            }
        } else if (condition.contains("<=")) {
            String[] parts = condition.split("<=");
            String leftVar = parts[0].trim();
            String rightVar = parts[1].trim();
            
            Object leftValue = getVariableValue(leftVar, context);
            Object rightValue = getVariableValue(rightVar, context);
            
            if (leftValue instanceof Integer && rightValue instanceof Integer) {
                return (Integer) leftValue <= (Integer) rightValue;
            }
        } else if (condition.contains("<")) {
            String[] parts = condition.split("<");
            String leftVar = parts[0].trim();
            String rightVar = parts[1].trim();
            
            Object leftValue = getVariableValue(leftVar, context);
            Object rightValue = getVariableValue(rightVar, context);
            
            if (leftValue instanceof Integer && rightValue instanceof Integer) {
                return (Integer) leftValue < (Integer) rightValue;
            }
        } else if (condition.contains("==")) {
            String[] parts = condition.split("==");
            String leftVar = parts[0].trim();
            String rightVar = parts[1].trim();
            
            Object leftValue = getVariableValue(leftVar, context);
            Object rightValue = getVariableValue(rightVar, context);
            
            return leftValue.equals(rightValue);
        } else if (condition.contains("!=")) {
            String[] parts = condition.split("!=");
            String leftVar = parts[0].trim();
            String rightVar = parts[1].trim();
            
            Object leftValue = getVariableValue(leftVar, context);
            Object rightValue = getVariableValue(rightVar, context);
            
            return !leftValue.equals(rightValue);
        }
        
        return false;
    }
    
    /**
     * 获取变量值
     * @param varName 变量名或字面量
     * @param context 执行上下文
     * @return 变量值
     */
    private Object getVariableValue(String varName, ExecutionContext context) {
        // 如果是数字字面量，直接返回
        try {
            return Integer.parseInt(varName);
        } catch (NumberFormatException e) {
            // 不是数字，继续处理
        }
        
        // 如果是布尔字面量，直接返回
        if ("true".equalsIgnoreCase(varName)) {
            return true;
        } else if ("false".equalsIgnoreCase(varName)) {
            return false;
        }
        
        // 否则从上下文中获取变量值
        return context.getVariable(varName);
    }
}
