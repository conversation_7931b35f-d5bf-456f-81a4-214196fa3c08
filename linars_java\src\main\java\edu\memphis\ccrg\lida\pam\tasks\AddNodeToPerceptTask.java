/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PamNode;
import edu.memphis.ccrg.lida.pam.PAMemory;

/**
 * A task which adds a {@link PamNode} to the percept.
 * <AUTHOR> J. McCall
 */
public class AddNodeToPerceptTask extends FrameworkTaskImpl {
	
	private Node node;
	private PAMemory pam;

	/**
	 * Default constructor
	 * @param n the {@link Node} to add
	 * @param pam {@link PAMemory}
	 */
	public AddNodeToPerceptTask(Node n, PAMemory pam) {
		node = n;
		this.pam = pam;
	}

	/**
	 * Adds {@link Node} to the percept then finishes.
	 */
	@Override
	protected void runThisFrameworkTask() {		
		pam.addToPercept(node);
		cancel();
	}
}
