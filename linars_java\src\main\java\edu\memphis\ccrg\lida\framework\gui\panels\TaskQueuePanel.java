/*
 * Created by <PERSON><PERSON>orm<PERSON>esigner on Sat Dec 21 08:35:29 CST 2019
 */

package edu.memphis.ccrg.lida.framework.gui.panels;

import edu.memphis.ccrg.lida.framework.tasks.FrameworkTask;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;

import javax.swing.*;
import javax.swing.table.AbstractTableModel;
import javax.swing.table.DefaultTableColumnModel;
import javax.swing.table.TableColumn;
import java.awt.*;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class TaskQueuePanel extends GuiPanelImpl {
    public TaskQueuePanel() {
        initComponents();
    }

    private static final Logger logger = Logger.getLogger(TaskQueuePanel.class.getCanonicalName());
    private Map<Long, Set<FrameworkTask>> tasks = new HashMap<Long, Set<FrameworkTask>>();

    //TODO xml support
    private int DEFAULT_COLUMN_WIDTH = 150;
    private int columnWidth = DEFAULT_COLUMN_WIDTH;


    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents
        jToolBar1 = new JToolBar();
        refreshButton = new JButton();
        taskPane = new JScrollPane();
        taskTable = new JTable();

        //======== this ========

        //======== jToolBar1 ========
        {
            jToolBar1.setRollover(true);

            //---- refreshButton ----
            refreshButton.setText("Refresh");
            refreshButton.setFocusable(false);
            refreshButton.setHorizontalTextPosition(SwingConstants.CENTER);
            refreshButton.setVerticalTextPosition(SwingConstants.BOTTOM);
            refreshButton.addActionListener(e -> refreshButtonActionPerformed(e));
            jToolBar1.add(refreshButton);
        }

        //======== taskPane ========
        {

            //---- taskTable ----
            taskTable.setModel(new TaskQueueTableModel());
            taskTable.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);
            taskTable.setMaximumSize(new Dimension(1000, 1000));
            taskPane.setViewportView(taskTable);
        }

        GroupLayout layout = new GroupLayout(this);
        setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup()
                .addComponent(taskPane, GroupLayout.DEFAULT_SIZE, 400, Short.MAX_VALUE)
                .addComponent(jToolBar1, GroupLayout.DEFAULT_SIZE, 400, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createSequentialGroup()
                    .addComponent(jToolBar1, GroupLayout.PREFERRED_SIZE, 25, GroupLayout.PREFERRED_SIZE)
                    .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                    .addComponent(taskPane, GroupLayout.DEFAULT_SIZE, 215, Short.MAX_VALUE))
        );
        // JFormDesigner - End of component initialization  //GEN-END:initComponents
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables
    private JToolBar jToolBar1;
    private JButton refreshButton;
    private JScrollPane taskPane;
    private JTable taskTable;
    // JFormDesigner - End of variables declaration  //GEN-END:variables

    private void refreshButtonActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_refreshButtonActionPerformed
        refresh();
    }//GEN-LAST:event_refreshButtonActionPerformed

    /*
     * Implementation of abstract table model to display the contents of the TaskManager's
     * task queue to a Table.
     *
     * <AUTHOR> Snaider
     */
    private class TaskQueueTableModel extends AbstractTableModel {

        /**
         * Returns the size of the largest queue in the task queue plus 1
         * @see javax.swing.table.TableModel#getColumnCount()
         */
        @Override
        public int getColumnCount() {
            int total = 0;
            for (Set<FrameworkTask> qt : tasks.values()) {
                if (qt.size() > total) {
                    total = qt.size();
                }
            }
            return total + 1; //the first one is the tick number
        }

        @Override
        public int getRowCount() {
            int rows = (int) (agent.getTaskManager().getMaxTick() - TaskManager.getCurrentTick()) + 1;
            return rows;
        }

        @Override
        public String getColumnName(int column) {
            String cName;
            if (column == 0) {
                cName = "Scheduled Tick";
            } else {
                cName = "Task " + column;
            }
            return cName;
        }

        /**
         * Based on the specified indices, returns a FrameworkTask.
         * @param rowIndex scheduled tick of the FrameworkTask
         * @param columnIndex the position of the FrameworkTask in the rowIndex row.
         * @see javax.swing.table.TableModel#getValueAt(int, int)
         */
        @Override
        public Object getValueAt(int rowIndex, int columnIndex) {
            Object o = null;
            if (columnIndex == 0) {
                return TaskManager.getCurrentTick() + rowIndex;
            }

            Set<FrameworkTask> qt = tasks.get(TaskManager.getCurrentTick()
                    + rowIndex);
            if (qt == null) {
                return "";
            }
            Iterator<FrameworkTask> it = qt.iterator();
            for (int i = 1; i <= columnIndex; i++) {
                if (it.hasNext()) {
                    o = it.next();
                } else {
                    o = "";
                    break;
                }
            }
            return o;
        }

        @Override
        public boolean isCellEditable(int row, int column) {
            return false;
        }
    }

    @Override
    public void refresh() {
        logger.log(Level.FINEST, "Refreshing TaskQueue Panel",
                TaskManager.getCurrentTick());
        display(agent.getTaskManager().getTaskQueue());
    }

    @Override
    @SuppressWarnings("unchecked")
    public void display(Object o) {
        if (o instanceof Map) {
            tasks = (Map<Long, Set<FrameworkTask>>) o;
            ((AbstractTableModel) taskTable.getModel()).fireTableStructureChanged();
        }
    }

    private class TaskColumnTableModel extends DefaultTableColumnModel {

        private int width;

        public TaskColumnTableModel(int width) {
            this.width = width;
        }

        @Override
        public void addColumn(TableColumn column) {
            column.setPreferredWidth(width);
            super.addColumn(column);
        }
    }
}
