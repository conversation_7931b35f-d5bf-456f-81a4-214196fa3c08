package edu.memphis.ccrg.linars;

import edu.memphis.ccrg.lida.framework.shared.*;
import edu.memphis.ccrg.lida.framework.shared.activation.ActivatibleImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamNode;
import edu.memphis.ccrg.lida.pam.PamNodeImpl;
import org.neo4j.graphdb.Transaction;
import org.neo4j.kernel.api.KernelTransaction;
import org.neo4j.kernel.impl.core.NodeEntity;
import org.neo4j.kernel.impl.coreapi.InternalTransaction;
import org.opennars.io.Parser;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.narsese;

public class TermNodeImpl extends ActivatibleImpl implements Node {
    public static final Logger logger = Logger.getLogger(TermNodeImpl.class
            .getCanonicalName());
    // todo 信息来源 如听觉 视觉
    public int nodeId;
    public ExtendedId extendedId;
    public String label ="word0";
    public String factoryName;
    public String toStringName;

    public String TNname;

    private String bcastid = "0";
    private String lastAct = "0";		// 最后一轮激活，将衰减留存和当前周期激活区分

    private String ptwath = "see";	// 信息来源，感知渠道
    private String from = "柯东";	// 施事者，动作或内容
    private String to = "小柯";		// 受事者，动作或内容

    private NodeEntity nodeProxy;
    // 虚实区分属性，7个，0是虚，虚多
    private int truth = 0;

    private int fromsceneid = 0;
    private int fromnodeid = 0;
    private String fromLinkType = "";

    private int doneNum = 0;

    private Map<String, Object> properties = new HashMap<>();

    public List<String> getLabels() {
        return labels;
    }

    public void setLabels(List<String> labels) {
        this.labels = labels;
    }

    private List<String> labels = new ArrayList<>();

    public NodeEntity getNodeProxy() {
        return nodeProxy;
    }

    // 版本4将事务内置到点边里，查完就close，无法在其他地方和时间取属性
    // 尝试打开原事务、复制点边、将新事务设置进点边，均无果
    // 20210704 gds不完善，嵌入开发繁杂，尚不比将属性和labels设置为lida点边内属性好
    public NodeEntity getNodeProxy(Transaction tx, String property) {
        NodeEntity newnode = new NodeEntity((InternalTransaction) tx,nodeProxy.getId());

//		newnode.setProperty(nodeProxy.get);

        KernelTransaction kt = ((InternalTransaction) tx).kernelTransaction();
        nodeProxy.getTransaction().setTransaction(kt);

        return nodeProxy;
    }

    public void setNodeProxy(NodeEntity nodeProxy) {
        this.nodeProxy = nodeProxy;
    }

    @Override
    public String getLocation() {
        return location;
    }

    @Override
    public void setBcastid(String bcastid) {
        this.bcastid = bcastid;
    }

    @Override
    public String getBcastid() {
        return bcastid;
    }

    @Override
    public void setLocation(String location) {
        this.location = location;
    }
    // todo 硬场景属性，后期可能去掉
    public String location;

    /**
     * {@link PamNode} in {@link PAMemory} which grounds this {@link Node}
     */
    protected PamNode groundingPamNode;

    @Override
    public synchronized void setFactoryType(String n) {
        factoryName = n;
    }

    @Override
    public String getFactoryType() {
        return factoryName;
    }

    /**
     * Default constructor
     */
    public TermNodeImpl(){
        super();
    }

    public TermNodeImpl(String name) {
//        super(name);
        setNodeName(name);
    }

    /**
     * Copy constructor.
     * @deprecated Use {@link ElementFactory#getNode(Node)} instead.
     * @param n source {@link TermNodeImpl}
     */
    @Deprecated
    public TermNodeImpl(TermNodeImpl n) {
        if (n == null) {
            logger.log(Level.WARNING, "Cannot construct a Node from null.",
                    TaskManager.getCurrentTick());
        } else {
            nodeId = n.nodeId;
            this.extendedId = n.extendedId;
            this.groundingPamNode = n.groundingPamNode;
            this.label = n.label;
            this.location = n.location;
            updateName();
        }
    }

    @Override
    public synchronized void setNodeId(int id0) {
        nodeId = id0;
        extendedId = new ExtendedId(id0);
        updateName();
    }

    /**
     * Convenience method to set Node's {@link ExtendedId}.  Also sets node's id.
     * @param eid {@link ExtendedId}
     */
    public synchronized void setExtendedId(ExtendedId eid) {
        if(eid == null){
            logger.log(Level.WARNING, "Supplied ExtendedId was null. ExtendedId not set.", TaskManager.getCurrentTick());
        }else if(eid.isNodeId()){
            this.extendedId = eid;
            this.nodeId = eid.getSourceNodeId();
            updateName();
        } else {
            logger.log(Level.WARNING, "Cannot give a Node a Link's ExtendedId",
                    TaskManager.getCurrentTick());
        }
    }

    /*
     * update node's name
     */
    public void updateName(){
//		toStringName = label + "[" + nodeId + "]";
        toStringName = TNname + "[" + nodeId + "] " + getActivation();
    }

    @Override
    public String getTNname() {
        return TNname;
//		return label;
//		return (String) nodeProxy.getProperty("name");
    }

    @Override
    public void setNodeName(String name0) {
//        setTermName0(name0);
        this.TNname = name0;
//		this.label = name0;
        updateName();
    }

    @Override
    public ExtendedId getExtendedId() {
        return extendedId;
    }

    @Override
    public int getNodeId() {
        return nodeId;
    }

    public String getLabel() {
        return label;
//		return nodeProxy.getLabels().toString();
    }
    @Override
    public void setLabel(String l) {
        this.label=l;
        updateName();
    }


    @Override
    public PamNode getGroundingPamNode() {
        return groundingPamNode;
    }
    @Override
    public synchronized void setGroundingPamNode(PamNode n) {
        groundingPamNode = n;
    }

    /**
     * This method compares this object with any kind of Node. returns true if
     * the id of both are the same.
     * @param o Object
     */
    @Override
    public boolean equals(Object o) {
        if (o instanceof Link){
            return false;
        } else if (o instanceof TermNodeImpl) {
            return ((TermNodeImpl) o).getNodeId() == nodeId;
        } else if (o instanceof CompoundTerm) {
            // 复合词项没有id，直接false？
            return false;
        }
        return false;
    }

    @Override
    public int hashCode() {
        return nodeId;
    }

    @Override
    public String toString(){
        DecimalFormat df = new DecimalFormat("#.00");
        return TNname + " [" + nodeId + "] " + df.format(getActivation());
    }


    /**
     * This default implementation of {@link Node} has all of its attributes
     * updated by {@link NodeStructureImpl} or {@link ElementFactory} when nodes
     * are updated. Therefore this class does not have to implement this method.
     * Any subclass with specific class members (e.g. PamNodeImpl) should
     * however override this method.
     *
     * @see PamNodeImpl#updateNodeValues(Node)
     * @see NodeStructureImpl#addNode(Node, String)
     */
    @Override
    public void updateNodeValues(Node n) {
        if(n instanceof TermNodeImpl){
            setIncentiveSalience(n.getTotalIncentiveSalience());//TODO move to ElementFactory methods
        }
    }

    @Override
    public ExtendedId getConditionId() {
        return extendedId;
    }

    public String getStringName() {
        return toStringName;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public int getTruth() {
        return truth;
    }

    public void setTruth(int truth) {
        this.truth = truth;
    }

    public String getLastAct() {
        return lastAct;
    }

    public void setLastAct(String lastAct) {
        this.lastAct = lastAct;
    }

    public int getFromsceneid() {
        return fromsceneid;
    }

    public void setFromsceneid(int fromsceneid) {
        this.fromsceneid = fromsceneid;
    }

    @Override
    public int getFromnodeid() {
        return fromnodeid;
    }

    @Override
    public void setFromnodeid(int fromnodeid) {
        this.fromnodeid = fromnodeid;
    }

    @Override
    public String getFromLinkType() {
        return fromLinkType;
    }

    @Override
    public void setFromLinkType(String fromLinkType) {
        this.fromLinkType = fromLinkType;
    }

    @Override
    public int getDoneNum() {
        return doneNum;
    }
    @Override
    public void setDoneNum(int doneNum) {
        this.doneNum = doneNum;
    }

    @Override
    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }

    @Override
    public Map<String, Object> getProperties() {
        return this.properties;
    }

    @Override
    public Object getProperty(String key) {
        for (String k: properties.keySet()) {
            if (k.equals(key)) {
                return properties.get(k);
            }
        }
        return null;
    }

    @Override
    public Term toTerm() {
        Term term = new Term();
        term.setTermName(getTNname());
        term.setTermId(getNodeId());
        try {
            term = narsese.parseTerm(getTNname());
        } catch (Parser.InvalidInputException e) {
            throw new RuntimeException(e);
        }
        return term;
    }

    @Override
    public String getStrs() {
        String strs = "";
        // 转为term后，拼接原子词项的字符串
        Term term = toTerm();
        if(term instanceof CompoundTerm){
            CompoundTerm ct = (CompoundTerm) term;
            strs = ct.getComponentsStrs();
        }else {
            strs = getTNname();
        }
        return strs;
    }
}
