package edu.memphis.ccrg.lida.framework.shared.scene;

import org.neo4j.graphdb.Node;
import org.opennars.io.Symbols;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Term;

import java.io.Serializable;
import java.util.List;

public class <PERSON>erson extends CompoundTerm0 implements Serializable {
//    private int id;
    private String name;
    private String status;

    private List<Node> nodeList;
    private SAttrs sAttrs;

    public SPerson(){

    }

    @Override
    public Symbols.NativeOperator operator() {
        return null;
    }

    @Override
    public CompoundTerm clone() {
        return null;
    }

    @Override
    public Term clone(Term[] replaced) {
        return null;
    }

    public SPerson(String name){
        this.name = name;
    }

//    public int getId() {
//        return id;
//    }
//
//    public void setId(int id) {
//        this.id = id;
//    }
//
    public String getLabel() {
        return name;
    }

    public void setLabel(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<Node> getNodeList() {
        return nodeList;
    }

    public void setNodeList(List<Node> nodeList) {
        this.nodeList = nodeList;
    }

    public SAttrs getsAttrs() {
        return sAttrs;
    }

    public void setsAttrs(SAttrs sAttrs) {
        this.sAttrs = sAttrs;
    }
}
