/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

import edu.memphis.ccrg.lida.environment.Environment;
import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.shared.*;
import edu.memphis.ccrg.lida.globalworkspace.GlobalWorkspace;
import edu.memphis.ccrg.lida.pam.tasks.BasicDetectionAlgorithm;
import edu.memphis.ccrg.lida.pam.tasks.DetectionAlgorithm;
import edu.memphis.ccrg.lida.pam.tasks.ExcitationTask;
import edu.memphis.ccrg.lida.pam.tasks.PropagationTask;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;

/**
 * A main module which contains feature detectors, nodes, and links.
 * <AUTHOR> J. McCall
 */
public interface PAMemory extends FrameworkModule{
	/**
	 * Adds a new {@link PamNode} of default type to PAM with specified label.
	 * Label must be unique. If not, existing node with specified label is returned.
	 * @param label the label of the new {@link PamNode}
	 * @return the new {@link PamNode} added to PAM, the existing Node with specified label or null
	 */
	public PamNode addDefaultNode(String label);

	/**
	 * Adds a new {@link PamNode} of specified type to PAM with specified label.  Type should refer to a subclass of {@link PamNodeImpl}.
	 * Label must be unique. If not, existing node with specified label is returned.
	 * @param type the type of the new {@link PamNode}
	 * @param label the label of the new {@link PamNode}
	 * @return the new {@link PamNode} added to PAM, the existing Node with specified label or null
	 */
	public PamNode addNode(String type, String label);

	/**
	 * Adds a new {@link PamLink} of default type to PAM. If a link with the same attributes already exists
	 * the existing link will be returned instead.
	 * @param src the link's source
	 * @param snk the link's sink
	 * @param cat the link's category
	 * @return the new PamLink, the existing PamLink, or null
	 */
	public PamLink addDefaultLink(Node src, Linkable snk, LinkCategory cat);

	/**
	 * 
	 * Adds a new {@link PamLink} of specified type to PAM. Type should refer to a subclass of {@link PamLinkImpl}.
	 * If a link with the same attributes already exists the existing link will be returned instead. 
	 * @param type link's type
	 * @param src the link's source
	 * @param snk the link's sink
	 * @param cat the link's category
	 * @return the new PamLink, the existing PamLink, or null
	 */
	public PamLink addLink(String type, Node src, Linkable snk, LinkCategory cat);
	
	/**
	 * Adds a COPY of specified node to this {@link PAMemory}.
	 * Node will be of Pam's default type.
	 * 
	 * @deprecated Use either {@link #addNode(String, String)} or {@link #addDefaultNode(String)} instead. 
	 * @param node PamNode
	 * @return Copied PamNode actually stored in this PAM.
	 */
//	@Deprecated
	public PamNode addDefaultNode(Node node);
	
	/**
	 * Adds a COPY of a collection of Nodes to this PAM.
	 * Nodes will be of Pam's default type.
	 * 
	 * @deprecated Use either {@link #addNode(String, String)} or 
	 * 	{@link #addDefaultNode(String)} instead.
	 * @param nodes nodes to add
	 * @return Copied PamNodes actually stored in this PAM
	 */
	@Deprecated
	public Set<PamNode> addDefaultNodes(Set<? extends Node> nodes);
	
	/**
	 * Adds a COPY of specified link to this PAM.
	 * Link will be of Pam's default type.
	 *
	 * @deprecated Use either {@link #addLink(String, Node, Linkable, LinkCategory)} or
	 * 	 {@link #addDefaultLink(Node, Linkable, LinkCategory)} instead.
	 * @param link  PamLink to add
	 * @return Copied PamLink actually stored in this PAM
	 */
	@Deprecated
	public PamLink addDefaultLink(Link link);
	
	/**
	 * Adds a COPY of specified collection of PamLinks to this PAM.
	 * Links will be of Pam's default type.
	 *
	 * @deprecated Use either {@link #addLink(String, Node, Linkable, LinkCategory)} or
	 * 	 {@link #addDefaultLink(Node, Linkable, LinkCategory)} instead.
	 * @param links  PamLinks to add
	 * @return Copied PamLinks actually stored in this PAM
	 */
	@Deprecated
	public Set<PamLink> addDefaultLinks(Set<? extends Link> links);
	
	/**
	 * Adds specified {@link DetectionAlgorithm} to be run.
	 * @param fd {@link DetectionAlgorithm}
	 */
	public void addDetectionAlgorithm(DetectionAlgorithm fd);
		
	/**
	 * Adds {@link PamListener}.
	 *
	 * @param pl listener
	 */
	public void addPamListener(PamListener pl);

	Environment getEnvironment();

	GlobalWorkspace getGlobalWorkspace();

	PamImpl0.PamNodeStructure getPamNodeStructure();

	WorkspaceBuffer getWorkspaceBuffer(String name);

	/**
	 * Sets {@link PropagationStrategy} governing how activation is propagated in this PAM.
	 *
	 * @param strategy {@link PropagationStrategy}
	 */
	public void setPropagationStrategy(PropagationStrategy strategy);
	
	/**
	 * Gets {@link PropagationStrategy} governing how activation is propagated in this PAM.
	 *
	 * @return this Pam's {@link PropagationStrategy}
	 */
	public PropagationStrategy getPropagationStrategy();

	//	public static int k = 0;
	void excite(String object, double amount, String from);

	/**
	 * Excites specified {@link PamLinkable} an amount of activation.
	 * @param linkable Id of the PamLinkable receiving the activation
	 * @param amount amount of activation to excite
	 * @see ExcitationTask {@link BasicDetectionAlgorithm}
	 */
	public void receiveExcitation(Linkable linkable, double amount, String from);
	
	/**
	 * Excites {@link PamLinkable} with an amount of activation.
	 * @param linkables Ids of PamLinkable to be excited
	 * @param amount amount of activation
	 */
	public void receiveExcitation(Set<PamLinkable> linkables, double amount, String from);
	
	/**
	 * Propagates activation from a {@link PamNode} to its parents.
	 *
	 * param pamNode The {@link PamNode} to propagate activation from.
	 * @param i
	 * @see ExcitationTask
	 * @see PropagationTask
	 */
	public void propagateActivationToParents(Node Node, int i, String from);

    /**
	 * Adds a NodeStructure to the percept.
	 *
	 * @param ns NodeStructure
	 */
	public void addToPercept(NodeStructure ns);
	/**
	 * Adds {@link Node} to the percept.
	 * @param n Node to add
	 */
	public void addToPercept(Node n);
	/**
	 * Adds {@link Link} to the percept.
	 * @param l Link to add
	 */
	public void addToPercept(Link l);
	
	/**
	 * Returns LinkCategory with specified id.
	 * @param id id of LinkCategory sought
	 * @return LinkCategory or null if category does not exist in PAM.
	 */
	public LinkCategory getLinkCategory(int id);
	
	/**
	 * Returns all categories in this Pam
	 * @return Collection of all {@link LinkCategory}
	 */
	public Collection<LinkCategory> getLinkCategories();
	
	/**
	 * Adds a COPY of specified LinkCategory to this {@link PAMemory}.
	 * Category must also be a node in order to be added. Node will be of Pam's default type. 
	 * @param cat {@link LinkCategory}
	 * @return Copied LinkCategory actually stored in this PAM.
	 */
	public LinkCategory addLinkCategory(LinkCategory cat);
	
	/**
	 * Returns true if this PAM contains specified PamNode.
	 *
	 * @param node the node
	 * @return true, if successful
	 */
	public boolean containsNode(Node node);
	
	/**
	 * Contains node.
	 *
	 * @param id ExtendedId of sought node
	 * @return true if PAM contains the node with this id.
	 */
	public boolean containsNode(ExtendedId id);
	
	/**
	 * Returns true if this PAM contains specified PamLink.
	 *
	 * @param link the link
	 * @return true, if successful
	 */
	public boolean containsLink(Link link);
	
	/**
	 * Contains link.
	 *
	 * @param id ExtendedId of sought link
	 * @return true if PAM contains the link with this id.
	 */
	public boolean containsLink(ExtendedId id);
	
	/**
	 * Sets perceptThreshold
	 * @param t threshold for a {@link Linkable} to become part of the percept
	 */
	public void setPerceptThreshold(double t);
	
	/**
	 * Sets upscaleFactor
	 * @param f scale factor for feed-forward activation propagation
	 */
	public void setUpscaleFactor(double f);
	
	/**
	 * Gets upscaleFactor
	 * @return scale factor for feed-forward activation propagation
	 */
	public double getUpscaleFactor();
	
	/**
	 * Sets downscaleFactor 
	 * @param f scale factor for top-down activation propagation
	 */
	public void setDownscaleFactor(double f);

	/**
	 * Gets downscaleFactor
	 * @return scale factor for top-down activation propagation
	 */
	public double getDownscaleFactor();
	
	/**
	 * Returns whether PamLinkable is above percept threshold.
	 * @param l a PamLinkable
	 * @return true if PamLinkable's total activation is above percept threshold 
	 */
	public boolean isOverPerceptThreshold(Linkable l);
	
	/**
	 * Returns the {@link PamNode} with specified id from this PAM or null.
	 *
	 * @param id the id
	 * @return the pam node
	 */
	public Node getNode(int id);
	
	/**
	 * Returns the {@link PamNode} with specified {@link ExtendedId} or null
	 * @param id sought {@link ExtendedId}
	 * @return PamNode  the actual Node
	 */
	public Node getNode(ExtendedId id);
	
	/**
	 * Returns the {@link PamNode} with specified label or null.
	 * This method is intended to be used only during initialization.
	 * @param label sought
	 * @return PamNode  the actual Node
	 */
	public Node getNode(String label);
	
	/**
	 * 
	 * @param id link's eid
	 * @return the {@link PamLink} with specified id from this PAM or null.
	 */
	public Link getLink(ExtendedId id);
	
	/**
	 * Returns an unmodifiable collection of the {@link PamNode}s in this PAM as {@link Node}s.
	 *
	 * @return the PamNodes of this PAM
	 */
	public Collection<Node> getNodes();
	
	/**
	 * Returns an unmodifiable collection of the {@link PamLink}s in this PAM as {@link Link}s.
	 *
	 * @return the PamLink of this PAM
	 */
	public Collection<Link> getLinks();

    PamListener getListener();

	// todo 认知执行语句化，在类似nars时序上执行，尽量不用线程？语句只是小图程，直接替换并改元组即可，大图程还需线程
	// 		图程需要动机管理分配，不能直接根据时序连续执行，集中管理=能派生+能中断+能回溯
	// 动机管理介入时序执行，时刻关注当前上层动机，如果上层改动，则不继续往下执行，上层不变则按时序执行
	// 动机时序与语法整合，语法时序本身内隐，思考语法的外显时序跟内隐的不是同一个，外显时序是认知动机时序，内隐时序是语法时序
	// 以下几行为copilot生成-20230321
	// 形成动机语法，动机语法形成动机树，动机树形成动机图，动机图形成动机场景，动机场景形成动机世界，语法、时序、动机、语句、时刻
	// 时刻是语句的执行时间，语句是动机的执行语句，动机是动机图的执行动机，动机图是动机场景的执行动机图，动机场景是动机世界的执行动机场景，动机世界是动机的执行动机世界
	// 语句是动机的执行语句，动机是动机图的执行动机，动机图是动机场景的执行动机图，动机场景是动机世界的执行动机场景，动机世界是动机的执行动机世界
	void getActRoot(Link link, boolean isVar, boolean isLoop, String actStamp);
	void getActRoot0(Link link, boolean isVar);
//	void getActRoot(Link link1);

	void putMap(Node node, String name);

	void setSceneMainNode(Node sink);

	Map getIsaLink(Node node, Node toNode, LinkCategory category, PAMemory pam);

	void activGrammarLink(Link link, String retype);

	public void getSceneNode(Node scene, String name, boolean b);


}