/*
 * Created by <PERSON><PERSON>ormDesigner on Sat Dec 21 08:36:45 CST 2019
 */

package edu.memphis.ccrg.lida.framework.gui.panels;

import javax.swing.*;
import javax.swing.table.AbstractTableModel;
import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.Enumeration;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class ConfigurationFilesPanel extends GuiPanelImpl {
    private static final Logger logger = Logger.getLogger(ConfigurationFilesPanel.class.getCanonicalName());
    private Properties properties;
    private String propertiesFile = "configs/lidaConfig.properties";

    /** Creates new form PropertiesPanel */
    public ConfigurationFilesPanel() {
        initComponents();
    }

    @Override
    public void initPanel(String[] params) {
        properties = new Properties();
        if (params.length > 0) {
            propertiesFile = params[0];
        }

        try {
            properties.load(new BufferedReader(new FileReader(propertiesFile)));
        } catch (FileNotFoundException e) {
            logger.log(Level.SEVERE, "Error reading properties  {0}", e.toString());
        } catch (IOException e) {
            logger.log(Level.SEVERE, "Error reading properties  {0}", e.toString());
        }
        fileNameTextField.setText(propertiesFile);

    }
    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents
        jToolBar1 = new JToolBar();
        fileNameTextField = new JTextField();
        threadPane = new JScrollPane();
        PropertiesTable = new JTable();

        //======== this ========

        //======== jToolBar1 ========
        {
            jToolBar1.setRollover(true);

            //---- fileNameTextField ----
            fileNameTextField.setEditable(false);
            jToolBar1.add(fileNameTextField);
        }

        //======== threadPane ========
        {

            //---- PropertiesTable ----
            PropertiesTable.setModel(new PropertiesTableModel());
            threadPane.setViewportView(PropertiesTable);
        }

        GroupLayout layout = new GroupLayout(this);
        setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createSequentialGroup()
                    .addComponent(jToolBar1, GroupLayout.DEFAULT_SIZE, 390, Short.MAX_VALUE)
                    .addContainerGap())
                .addComponent(threadPane, GroupLayout.DEFAULT_SIZE, 400, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createSequentialGroup()
                    .addComponent(jToolBar1, GroupLayout.PREFERRED_SIZE, 25, GroupLayout.PREFERRED_SIZE)
                    .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                    .addComponent(threadPane, GroupLayout.DEFAULT_SIZE, 215, Short.MAX_VALUE))
        );
        // JFormDesigner - End of component initialization  //GEN-END:initComponents
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables
    private JToolBar jToolBar1;
    private JTextField fileNameTextField;
    private JScrollPane threadPane;
    private JTable PropertiesTable;
    // JFormDesigner - End of variables declaration  //GEN-END:variables
    private class PropertiesTableModel extends AbstractTableModel {

        @Override
        public int getColumnCount() {
            return 2;
        }

        @Override
        public int getRowCount() {
            return properties.size();
        }

        @Override
        public String getColumnName(int column) {
            if (column == 0) {
                return "Key";
            } else {
                return "Value";
            }
        }

        @Override
        public Object getValueAt(int rowIndex, int columnIndex) {
            if (columnIndex == 0) {
                return getKey(rowIndex);
            } else {
                return properties.get(getKey(rowIndex));
            } // if-else

        }

        private String getKey(int a_index) {
            String retval = "";
            Enumeration<Object> e = properties.keys();
            for (int i = 0; i < a_index + 1; i++) {
                retval = (String) e.nextElement();
            } // for

            return retval;
        }

        @Override
        public void setValueAt(Object value, int row, int column) {
            if (column == 1) {
                properties.setProperty(getKey(row), (String) value);
            }
        }

        @Override
        public boolean isCellEditable(int row, int column) {
            return (column == 1);
        }
    }//inner class

    @Override
    public void display(Object o) {
        if (o instanceof Properties) {
            properties = (Properties) o;
        }
    }
}
