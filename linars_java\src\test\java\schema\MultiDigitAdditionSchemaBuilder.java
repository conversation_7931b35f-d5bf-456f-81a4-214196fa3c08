package schema;

import schema.model.Edge;
import schema.model.Node;
import schema.model.edges.*;
import schema.model.nodes.*;

import java.util.Arrays;
import java.util.Collections;

/**
 * 多位数加法图式构建器，用于构建多位数加法的可执行图式
 */
public class MultiDigitAdditionSchemaBuilder {
    /**
     * 构建多位数加法的可执行图式
     * @return 可执行图式
     */
    public static ExecutableSchema build() {
        // 创建上下文节点
        ContextNode additionContext = new ContextNode("1", "加法计算", "global");
        
        // 创建可执行图式
        ExecutableSchema schema = new ExecutableSchema("multi_digit_addition", "多位数加法", additionContext);
        
        // 创建数据节点
        DataNode operand1 = new DataNode("2", "操作数1", "string", "123");
        DataNode operand2 = new DataNode("3", "操作数2", "string", "456");
        DataNode result = new DataNode("4", "结果", "string", "");
        DataNode carry = new DataNode("5", "进位", "integer", 0);
        DataNode currentIndex = new DataNode("6", "当前位索引", "integer", 2);
        
        // 添加数据节点到图式
        schema.addNode(operand1);
        schema.addNode(operand2);
        schema.addNode(result);
        schema.addNode(carry);
        schema.addNode(currentIndex);
        
        // 创建操作节点
        OperationNode initOperation = new OperationNode("10", "初始化", "initialization", 
                Arrays.asList("操作数1", "操作数2"));
        OperationNode getCurrentDigit = new OperationNode("11", "获取当前位", "data_access", 
                Arrays.asList("操作数1", "操作数2", "当前位索引"));
        OperationNode calculateDigitAndCarry = new OperationNode("12", "计算当前位和进位", "arithmetic", 
                Arrays.asList("位1", "位2", "进位"));
        OperationNode updateResult = new OperationNode("13", "更新结果", "data_update", 
                Arrays.asList("结果", "当前位结果"));
        OperationNode moveToNextDigit = new OperationNode("14", "移动到下一位", "arithmetic", 
                Arrays.asList("当前位索引"));
        OperationNode addFinalCarry = new OperationNode("15", "添加进位", "data_update", 
                Arrays.asList("结果", "进位"));
        OperationNode skipCarry = new OperationNode("16", "跳过进位", "noop", 
                Collections.emptyList());
        OperationNode returnResult = new OperationNode("17", "返回结果", "return", 
                Arrays.asList("结果"));
        
        // 添加操作节点到图式
        schema.addNode(initOperation);
        schema.addNode(getCurrentDigit);
        schema.addNode(calculateDigitAndCarry);
        schema.addNode(updateResult);
        schema.addNode(moveToNextDigit);
        schema.addNode(addFinalCarry);
        schema.addNode(skipCarry);
        schema.addNode(returnResult);
        
        // 创建控制节点
        ControlNode processDigitsLoop = new ControlNode("8", "处理位循环", "loop", "当前位索引 >= 0");
        ControlNode processFinalCarry = new ControlNode("9", "处理最终进位", "conditional", "进位 > 0");
        
        // 添加控制节点到图式
        schema.addNode(processDigitsLoop);
        schema.addNode(processFinalCarry);
        
        // 创建时序边（树状结构）
        Edge contextToInit = new SequenceEdge("101", additionContext, initOperation, true);
        Edge contextToLoop = new SequenceEdge("102", additionContext, processDigitsLoop, false);
        Edge contextToCondition = new SequenceEdge("103", additionContext, processFinalCarry, false);
        Edge contextToReturn = new SequenceEdge("104", additionContext, returnResult, false);
        
        // 添加时序边到图式
        schema.addEdge(contextToInit);
        schema.addEdge(contextToLoop);
        schema.addEdge(contextToCondition);
        schema.addEdge(contextToReturn);
        
        // 创建循环内部时序边
        Edge loopToGetDigit = new SequenceEdge("105", processDigitsLoop, getCurrentDigit, true);
        Edge loopToCalculate = new SequenceEdge("106", processDigitsLoop, calculateDigitAndCarry, false);
        Edge loopToUpdate = new SequenceEdge("107", processDigitsLoop, updateResult, false);
        Edge loopToMove = new SequenceEdge("108", processDigitsLoop, moveToNextDigit, false);
        
        // 添加循环内部时序边到图式
        schema.addEdge(loopToGetDigit);
        schema.addEdge(loopToCalculate);
        schema.addEdge(loopToUpdate);
        schema.addEdge(loopToMove);
        
        // 创建条件分支时序边
        Edge conditionToAddCarry = new ConditionalEdge("109", processFinalCarry, addFinalCarry, true);
        Edge conditionToSkipCarry = new ConditionalEdge("110", processFinalCarry, skipCarry, false);
        
        // 添加条件分支时序边到图式
        schema.addEdge(conditionToAddCarry);
        schema.addEdge(conditionToSkipCarry);
        
        // 创建顺承边（链式结构）
        Edge initToLoop = new SuccessionEdge("201", initOperation, processDigitsLoop);
        Edge loopToCondition = new SuccessionEdge("202", processDigitsLoop, processFinalCarry);
        Edge conditionToReturn = new SuccessionEdge("203", processFinalCarry, returnResult);
        
        // 添加顺承边到图式
        schema.addEdge(initToLoop);
        schema.addEdge(loopToCondition);
        schema.addEdge(conditionToReturn);
        
        // 创建循环内部顺承边
        Edge getDigitToCalculate = new SuccessionEdge("204", getCurrentDigit, calculateDigitAndCarry);
        Edge calculateToUpdate = new SuccessionEdge("205", calculateDigitAndCarry, updateResult);
        Edge updateToMove = new SuccessionEdge("206", updateResult, moveToNextDigit);
        
        // 添加循环内部顺承边到图式
        schema.addEdge(getDigitToCalculate);
        schema.addEdge(calculateToUpdate);
        schema.addEdge(updateToMove);
        
        // 创建循环条件边
        Edge moveToLoop = new LoopEdge("207", moveToNextDigit, processDigitsLoop, "当前位索引 >= 0");
        
        // 添加循环条件边到图式
        schema.addEdge(moveToLoop);
        
        // 创建条件分支顺承边
        Edge addCarryToReturn = new SuccessionEdge("208", addFinalCarry, returnResult);
        Edge skipCarryToReturn = new SuccessionEdge("209", skipCarry, returnResult);
        
        // 添加条件分支顺承边到图式
        schema.addEdge(addCarryToReturn);
        schema.addEdge(skipCarryToReturn);
        
        // 创建变量边
        Edge contextToOperand1 = new VariableEdge("301", additionContext, operand1);
        Edge contextToOperand2 = new VariableEdge("302", additionContext, operand2);
        Edge contextToResult = new VariableEdge("303", additionContext, result);
        Edge contextToCarry = new VariableEdge("304", additionContext, carry);
        Edge contextToCurrentIndex = new VariableEdge("305", additionContext, currentIndex);
        
        // 添加变量边到图式
        schema.addEdge(contextToOperand1);
        schema.addEdge(contextToOperand2);
        schema.addEdge(contextToResult);
        schema.addEdge(contextToCarry);
        schema.addEdge(contextToCurrentIndex);
        
        // 创建数据流边
        Edge operand1ToGetDigit = new DataFlowEdge("401", operand1, getCurrentDigit, "input", "in");
        Edge operand2ToGetDigit = new DataFlowEdge("402", operand2, getCurrentDigit, "input", "in");
        Edge currentIndexToGetDigit = new DataFlowEdge("403", currentIndex, getCurrentDigit, "input", "in");
        Edge getDigitToCalculate2 = new DataFlowEdge("404", getCurrentDigit, calculateDigitAndCarry, "output", "out");
        Edge carryToCalculate = new DataFlowEdge("405", carry, calculateDigitAndCarry, "input", "in");
        Edge calculateToCarry = new DataFlowEdge("406", calculateDigitAndCarry, carry, "output", "out");
        Edge calculateToUpdate2 = new DataFlowEdge("407", calculateDigitAndCarry, updateResult, "output", "out");
        Edge resultToUpdate = new DataFlowEdge("408", result, updateResult, "input", "in");
        Edge updateToResult = new DataFlowEdge("409", updateResult, result, "output", "out");
        Edge currentIndexToMove = new DataFlowEdge("410", currentIndex, moveToNextDigit, "input", "in");
        Edge moveToCurrentIndex = new DataFlowEdge("411", moveToNextDigit, currentIndex, "output", "out");
        Edge carryToAddCarry = new DataFlowEdge("412", carry, addFinalCarry, "input", "in");
        Edge resultToAddCarry = new DataFlowEdge("413", result, addFinalCarry, "input", "in");
        Edge addCarryToResult = new DataFlowEdge("414", addFinalCarry, result, "output", "out");
        Edge resultToReturn = new DataFlowEdge("415", result, returnResult, "input", "in");
        
        // 添加数据流边到图式
        schema.addEdge(operand1ToGetDigit);
        schema.addEdge(operand2ToGetDigit);
        schema.addEdge(currentIndexToGetDigit);
        schema.addEdge(getDigitToCalculate2);
        schema.addEdge(carryToCalculate);
        schema.addEdge(calculateToCarry);
        schema.addEdge(calculateToUpdate2);
        schema.addEdge(resultToUpdate);
        schema.addEdge(updateToResult);
        schema.addEdge(currentIndexToMove);
        schema.addEdge(moveToCurrentIndex);
        schema.addEdge(carryToAddCarry);
        schema.addEdge(resultToAddCarry);
        schema.addEdge(addCarryToResult);
        schema.addEdge(resultToReturn);
        
        return schema;
    }
}
