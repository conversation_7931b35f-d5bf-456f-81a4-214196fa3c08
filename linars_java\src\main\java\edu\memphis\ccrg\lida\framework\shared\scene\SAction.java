package edu.memphis.ccrg.lida.framework.shared.scene;


import org.neo4j.graphdb.Node;
import org.opennars.io.Symbols;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Term;

import java.io.Serializable;
import java.util.List;

public class SAction extends CompoundTerm0 implements Serializable {
//    private String id;
//    private String name;
    private String status;

    private List<Node> nodeList;
    private SAttrs sAttrs;

//    public String getId() {
//        return id;
//    }
//
//    public void setId(String id) {
//        this.id = id;
//    }
//
//    public String getName() {
//        return name;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<Node> getNodeList() {
        return nodeList;
    }

    public void setNodeList(List<Node> nodeList) {
        this.nodeList = nodeList;
    }

    public SAttrs getsAttrs() {
        return sAttrs;
    }

    public void setsAttrs(SAttrs sAttrs) {
        this.sAttrs = sAttrs;
    }

    @Override
    public Symbols.NativeOperator operator() {
        return null;
    }

    @Override
    public CompoundTerm clone() {
        return null;
    }

    @Override
    public Term clone(Term[] replaced) {
        return null;
    }
}
