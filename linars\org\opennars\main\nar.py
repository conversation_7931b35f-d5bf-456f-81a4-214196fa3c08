"""
非公理推理机(Non-Axiomatic Reasoner)。

每个实例代表一个连接到内存(Memory)的推理机，包含一组输入输出通道。

所有状态都存储在内存中。Nar负责管理I/O通道和执行内存操作。它支持两种运行模式：
  * 单步模式 - 由外部系统控制，用于调试或测试
  * 线程模式 - 以特定最大帧率运行的可暂停闭环模式
"""
import logging
import threading
import time
import uuid
from typing import Dict, List, Optional, Set, Tuple, Union, Any, Callable

print("Importing modules in nar.py...")
from linars.org.opennars.main.parameters import Parameters
# print("Imported Parameters")
from linars.edu.memphis.ccrg.linars.memory import Memory
# print("Imported Memory")
from linars.org.opennars.storage.buffer import Buffer
# print("Imported Buffer")
from linars.org.opennars.storage.bag1 import Bag1
# print("Imported Bag1")
from linars.org.opennars.storage.internal_experience_buffer import InternalExperienceBuffer
# print("Imported InternalExperienceBuffer")
from linars.org.opennars.entity.task import Task
# print("Imported Task")
from linars.org.opennars.entity.sentence import Sentence
# print("Imported Sentence")
from linars.org.opennars.entity.stamp import Stamp
# print("Imported Stamp")
from linars.org.opennars.entity.budget_value import BudgetValue
# print("Imported BudgetValue")
from linars.org.opennars.io.symbols import QUESTION_MARK
# print("Imported QUESTION_MARK")

# 尝试导入Concept类
try:
    # print("Trying to import Concept...")
    from linars.edu.memphis.ccrg.linars.concept import Concept
    print("Successfully imported Concept")
except ImportError as e:
    print(f"Error importing Concept: {e}")
    # 如果无法导入，创建一个空的Concept类
    class Concept:
        def __init__(self, *args, **kwargs):
            print(f"Created dummy Concept with args: {args}, kwargs: {kwargs}")
    print("Created dummy Concept class")

class Nar:
    """
    非公理推理机(Non-Axiomatic Reasoner)核心类。

    每个实例代表一个连接到内存(Memory)的推理机，包含一组输入输出通道。
    负责整个推理系统的生命周期管理和核心逻辑执行。
    """

    # Default config file path
    DEFAULTCONFIG_FILEPATH = "config/defaultConfig.xml"

    # Version information
    VERSION = "v3.1.0"
    NAME = "Open-NARS"
    WEBSITE = """
        Open-NARS website:  http://code.google.com/p/open-org.opennars/
        NARS website:  http://sites.google.com/site/narswang/
        Github website:  http://github.com/opennars/
        IRC:  http://webchat.freenode.net/?channels=org.opennars
    """

    def __init__(self, nar_id=None, config_file_path=None, parameter_overrides=None):
        # print(f"Initializing Nar with nar_id={nar_id}, config_file_path={config_file_path}, parameter_overrides={parameter_overrides}")
        """
        初始化非公理推理机实例。

        参数:
            nar_id: 推理机唯一标识符，如未提供将自动生成
            config_file_path: 配置文件路径，如未提供使用默认路径
            parameter_overrides: 参数覆盖字典，用于自定义配置
        """
        # Initialize parameters
        # print("Creating Parameters instance...")
        self.narParameters = Parameters()
        # print("Created Parameters instance")

        # System clock
        self.cycle = 0
        self.cycle_mutex = threading.Lock()

        # Runtime variables
        self.running = False
        self.stopped = True
        self.threads = None
        self.min_cycle_period_ms = 0
        self.thread_yield = False

        # Generate NAR ID if not provided
        if nar_id is None:
            nar_id = uuid.uuid4().int & 0xFFFFFFFF

        # Use default config file path if not provided
        if config_file_path is None:
            config_file_path = self.DEFAULTCONFIG_FILEPATH

        # Override parameters if provided
        if parameter_overrides:
            self.override_parameters(parameter_overrides)

        mem1 = Memory()

        self.memory = self.init_mem(mem1)

        # Set NAR ID
        self.memory.narId = nar_id

        # Initialize internal experience
        self.init_internal_experience()

        # Store config file path
        self.used_config_file_path = config_file_path

        # Initialize plugins
        self.plugins = []
        self.sensory_channels = {}
        self.sensory_channels_mutex = threading.Lock()

        # Load plugins from config file
        # This would be implemented in a real system

    def init_internal_experience(self):
        """初始化内部经验缓冲区，用于存储系统运行时的内部状态和经验数据。"""
        levels = self.narParameters.INTERNAL_BUFFER_LEVELS
        capacity = self.narParameters.INTERNAL_BUFFER_SIZE
        self.memory.internalExperienceBuffer = InternalExperienceBuffer(
            self, levels, capacity, self.narParameters
        )

    def init_mem(self, mem1):
        # Initialize memory
        # print("Creating Memory instance...")
        try:
            # print("Creating Bag1 for concepts...")
            concepts_bag = Bag1(self.narParameters.CONCEPT_BAG_SIZE)
            # print("Created Bag1 for concepts")

            # print("Creating Buffer for global buffer...")
            globalBuffer = Buffer(self, self.narParameters.GLOBAL_BUFFER_LEVELS, self.narParameters.GLOBAL_BUFFER_SIZE, self.narParameters, False)
            # print("Created Buffer for global buffer")

            # print("Creating Buffer for sequence buffer...")
            sequence_buffer = Buffer(self, self.narParameters.SEQUENCE_BAG_LEVELS, self.narParameters.SEQUENCE_BAG_SIZE, self.narParameters, True)
            # print("Created Buffer for sequence buffer")

            # print("Creating Bag1 for operations...")
            operations_bag = Bag1(self.narParameters.OPERATION_BAG_SIZE)
            # print("Created Bag1 for operations")

            # print("Creating Memory with all components...")
            # memory0 = Memory(
            #     self.narParameters,
            #     concepts_bag,
            #     globalBuffer,
            #     sequence_buffer,
            #     operations_bag
            # )
            # return memory0

            mem1.narParameters = self.narParameters
            mem1.concepts = concepts_bag
            mem1.globalBuffer = globalBuffer
            if sequence_buffer and mem1.globalBuffer:
                mem1.globalBuffer.seq_current = sequence_buffer
                mem1.globalBuffer.seq_current.mem = mem1
            mem1.recent_operations = operations_bag
            return mem1

            # print("Created Memory instance")
        except Exception as e:
            print(f"Error creating Memory: {e}")
            import traceback
            traceback.print_exc()

    @staticmethod
    def override_parameters(parameters, overrides):
        """
        按名称覆盖参数值。

        参数:
            parameters: 待覆盖的参数对象
            overrides: 包含参数名和对应新值的字典
        """
        for key, value in overrides.items():
            if hasattr(parameters, key):
                setattr(parameters, key, value)

    def reset(self):
        """重置系统状态，清空内存并重置时钟。用于系统重新初始化。"""
        self.cycle = 0
        self.memory.reset()

    def add_input(self, text_or_task, time_obj=None):
        """
        向系统添加输入，支持文本或任务对象。

        参数:
            text_or_task: 输入文本或任务对象
            time_obj: 时间对象，如未提供使用当前系统时间

        返回:
            self: 支持方法链式调用
        """
        if isinstance(text_or_task, str):
            self.add_input_text(text_or_task)
        elif isinstance(text_or_task, Task):
            self.add_input_task(text_or_task, time_obj or self)
        return self

    def add_input_text(self, text):
        """
        向系统添加文本输入，会自动解析为任务对象。

        参数:
            text: 输入文本，将被解析为Narsese格式
        """
        if not text:
            return

        # Parse text into task
        # This would call a Narsese parser in a real system
        task = None

        if task:
            # Set occurrence time if not eternal
            if not task.sentence.is_eternal():
                task.sentence.stamp.set_occurrence_time(self.time())

            # Set creation time
            task.sentence.stamp.set_creation_time(self.time(), self.narParameters.DURATION)

            # Add to memory
            self.memory.emit("IN", task)
            self.memory.globalBuffer.put_in(task)

    def add_input_task(self, task, time_obj):
        """
        向系统添加任务对象输入。

        参数:
            task: 输入的任务对象
            time_obj: 关联的时间对象
        """
        self.memory.input_task(time_obj, task)

    def add_input_to(self, text, memory):
        """
        将输入添加到指定的内存中。

        参数:
            text: 输入文本（Narsese格式）
            memory: 目标内存对象
        """
        # 处理输入文本
        text = self.get_string(text)
        if text is None:
            return

        # 使用线程锁确保线程安全
        with threading.Lock():
            try:
                # 创建Narsese解析器
                from linars.org.opennars.io.narsese import Narsese
                from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter

                # 使用当前的NAR实例或全局NAR实例
                nar = self if hasattr(self, 'memory') else AgentStarter.nar
                narsese = Narsese(nar)

                # 解析任务
                t = narsese.parse_task(text)

                # 设置时间戳
                if not t.sentence.is_eternal():
                    t.sentence.stamp.set_occurrence_time(nar.time())

                t.sentence.stamp.set_creation_time(nar.time(), nar.narParameters.DURATION)

                # 发送事件并添加到内存
                memory.emit("IN", t)

                # 检查内存状态
                if not hasattr(memory, 'checked') or not memory.checked:
                    memory.checked = True
                    memory.is_junit = False  # Python中没有直接的JUnit测试检测

                # 将任务添加到全局缓冲区
                memory.globalBuffer.put_in(t)

            except Exception as e:
                import traceback
                traceback.print_exc()
                raise RuntimeError(f"Error in add_input_to: {str(e)}")

    def get_string(self, text):
        """
        处理输入字符串，处理多行输入和命令。

        参数:
            text: 输入文本

        返回:
            str: 处理后的文本，如果是命令或注释则返回None
        """
        if text is None:
            return None

        text = text.strip()

        # 处理多行输入
        if '\n' in text:
            lines = text.split('\n')
            for line in lines:
                if line.strip():
                    self.add_input(line)
            return None

        # 忽略注释
        if text.startswith("'") or text.startswith("//") or not text:
            return None

        # 处理命令
        if text.startswith("*"):
            if text.startswith("**"):
                self.reset()
                return None
            elif text.startswith("*decisionthreshold="):
                value = float(text.split("decisionthreshold=")[1])
                self.narParameters.DECISION_THRESHOLD = value
                return None
            elif text.startswith("*volume="):
                value = int(text.split("volume=")[1])
                self.narParameters.VOLUME = value
                return None
            elif text.startswith("*threads="):
                value = int(text.split("threads=")[1])
                self.narParameters.THREADS_AMOUNT = value
                return None

        return text

    def dispatch_to_sensory_channel(self, task):
        """
        将任务分发到感知通道(如果需要)。

        参数:
            task: 待分发的任务对象

        返回:
            bool: 是否成功分发到感知通道
        """
        t = task.get_term()
        if t is None:
            return False

        # Check if task should be dispatched to a sensory channel
        # This would be implemented in a real system

        return False

    def concept(self, concept_str):
        """
        查询系统中是否存在指定概念。

        参数:
            concept_str: 概念字符串(Narsese格式)

        返回:
            Concept: 如存在返回概念对象，否则返回None
        """
        # Parse concept string into term
        # This would call a Narsese parser in a real system
        term = None

        if term:
            return self.memory.concept(term)
        return None

    def ask(self, term_string, answered=None):
        """
        向系统提出一个问题，可设置回答处理器。

        参数:
            term_string: 问题字符串(Narsese格式)
            answered: 回答处理器对象，用于处理系统返回的答案

        返回:
            self: 支持方法链式调用
        """
        # Parse term string into term
        # This would call a Narsese parser in a real system
        term = None

        if term:
            # Create sentence
            sentence = Sentence(
                term,
                QUESTION_MARK,
                None,
                Stamp(self, self.memory, "Eternal")
            )

            # Create budget
            budget = BudgetValue(
                self.narParameters.DEFAULT_QUESTION_PRIORITY,
                self.narParameters.DEFAULT_QUESTION_DURABILITY,
                1,
                self.narParameters
            )
            from linars.org.opennars.entity.task import EnumType
            # Create task
            task = Task(sentence, budget, EnumType.INPUT)

            # Add input
            self.add_input(task, self)

            # Start answer handler
            if answered:
                answered.start(task, self)

        return self

    def start(self, min_cycle_period_ms=None):
        """
        启动推理过程，可设置最小周期时间。

        参数:
            min_cycle_period_ms: 最小周期时间(毫秒)，如未提供使用默认参数
        """
        if min_cycle_period_ms is None:
            min_cycle_period_ms = self.narParameters.MILLISECONDS_PER_STEP

        self.min_cycle_period_ms = min_cycle_period_ms

        if self.threads is not None:
            return

        self.stopped = False
        self.running = True

        # Create and start threads
        self.threads = []
        for i in range(self.narParameters.THREADS_AMOUNT):
            thread = threading.Thread(target=self.run)
            thread.daemon = True
            thread.start()
            self.threads.append(thread)

    def stop(self):
        """停止推理过程，终止相关线程。"""
        if self.threads is not None:
            for thread in self.threads:
                thread.join(1)  # Wait for 1 second
            self.threads = None

        self.stopped = True
        self.running = False

    def cycles(self, cycles):
        """
        执行指定数量的推理周期。

        参数:
            cycles: 要执行的周期数
        """
        self.memory.allowExecution = True
        self.emit("CyclesStart")
        was_running = self.running
        self.running = True
        self.stopped = False

        for i in range(cycles):
            self.cycle_fn()

        self.running = was_running
        self.emit("CyclesEnd")

    def run(self):
        """线程执行的主循环，不应直接调用。"""
        self.stopped = False
        while self.running and not self.stopped:
            self.emit("CyclesStart")
            self.cycle_fn()
            self.emit("CyclesEnd")

            if self.min_cycle_period_ms > 0:
                time.sleep(self.min_cycle_period_ms / 1000.0)
            elif self.thread_yield:
                time.sleep(0)  # Yield to other threads

    def emit(self, c, *o):
        """
        发射一个事件通知。

        参数:
            c: 事件类型
            *o: 事件参数
        """
        if self.memory.event:
            self.memory.event.emit(c, *o)

    def cycle_fn(self):
        """执行一个推理周期，包含一个或多个内存操作周期。"""
        try:
            self.memory.cycle(self)
            with self.cycle_mutex:
                self.cycle += 1
        except Exception as e:
            # Handle reasoning errors
            self.emit("ERR", e)
            logging.error(f"Reasoning error: {e}")

    def time(self):
        """
        获取当前时间。

        返回:
            int: 当前时间(毫秒或周期数，取决于配置)
        """
        if self.narParameters.STEPS_CLOCK:
            return self.cycle
        else:
            return int(time.time() * 1000)  # Current time in milliseconds

    def is_running(self):
        """
        检查系统是否正在运行。

        返回:
            bool: 如果正在运行返回True
        """
        return self.running

    def get_min_cycle_period_ms(self):
        """
        获取最小周期时间(毫秒)。

        返回:
            int: 最小周期时间(毫秒)
        """
        return self.min_cycle_period_ms

    def __str__(self):
        """
        获取系统的字符串表示形式。

        返回:
            str: 系统的字符串表示
        """
        return str(self.memory)
