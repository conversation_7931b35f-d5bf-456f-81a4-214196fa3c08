package schema.model.edges;

import schema.model.Edge;
import schema.model.Node;

/**
 * 数据流边，表示数据传递，如参数传递和结果返回
 */
public class DataFlowEdge extends Edge {
    private String dataType;  // 数据类型：input, output
    private String direction; // 数据流向：in, out

    public DataFlowEdge(String id, Node source, Node target, String dataType, String direction) {
        super(id, source, target, "数据流");
        this.dataType = dataType;
        this.direction = direction;
        setProperty("data_type", dataType);
        setProperty("direction", direction);
    }

    public String getDataType() {
        return dataType;
    }

    public String getDirection() {
        return direction;
    }
}
