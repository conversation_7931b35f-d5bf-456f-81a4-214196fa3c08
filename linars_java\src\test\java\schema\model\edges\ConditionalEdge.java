package schema.model.edges;

import schema.model.Edge;
import schema.model.Node;

/**
 * 判断边，表示条件分支
 */
public class ConditionalEdge extends Edge {
    private boolean isThen; // 是否是then分支

    public ConditionalEdge(String id, Node source, Node target, boolean isThen) {
        super(id, source, target, isThen ? "判断首" : "判断");
        this.isThen = isThen;
        setProperty("is_then", isThen);
    }

    public boolean isThen() {
        return isThen;
    }
}
