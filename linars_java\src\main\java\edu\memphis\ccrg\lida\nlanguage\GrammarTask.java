/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.nlanguage;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import org.neo4j.graphdb.Relationship;
import org.neo4j.graphdb.Result;
import org.neo4j.graphdb.Transaction;

import java.util.*;
import java.util.logging.Logger;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;

/**
 * 语法任务
 */
public class GrammarTask extends FrameworkTaskImpl {
	private static final Logger logger = Logger.getLogger(GrammarTask.class
			.getCanonicalName());
	private NodeStructure yufaNs;
	private NodeStructure sceneNs;
	private PAMemory pam;
	private Map<Integer, String> words;
	private int wordnum;
	private boolean isdone;
	private Node node;

	Map<String,Collection<Link>> scenemap = new HashMap<>();
	Map<String,List<Link>> yufamap = new HashMap<>();

	public GrammarTask(NodeStructure yufaNs, NodeStructure sceneNs, int ticksPerRun, PAMemory pam) {
		super(ticksPerRun, "tact");
		this.yufaNs = yufaNs;
		this.sceneNs = sceneNs;
		this.pam = pam;
	}

	@Override
	protected void runThisFrameworkTask() {
		int yufacore;
		int yufatotal;
		int scenecore;
		int size;
		words = new HashMap<>();

		try (Transaction tx = graphDb.beginTx()) {
			// todo 目前是只能当前待生成场景进入场景buffer，生成后清理掉，但其他情况也会有混入，如中断后重来、复杂句群
			// 公共树匹配，先把场景构建为树结构，再根据场景树构建语法树，输出两个树结构
			// 本身是图结构，但不能随意找到任意层任意段节点，不在激活时构建是因为太复杂，这里相对简单
			Collection<Link> mainlinsks = sceneNs.getLinksOfSink(sceneNs.getMainNodeId());

			int level = 0;
			scenemap.put(level + "_" + sceneNs.getMainNodeId(), mainlinsks);

			getTree(mainlinsks, scenemap, level + 1);

			if(scenemap.size() > 0){
				sceneNs.setMainMap(scenemap);
			}
			List<Node> yufaNodes = new ArrayList<>();
			// 语法节点满足边条数，且跟场景元素数量一致，则设置
			// 无序遍历，无法从树根部开始建模，只能用map，键值模拟树枝，如果要先找根部，需要多次遍历
			for (Node node : yufaNs.getNodes()) {
				if(AgentStarter.yufamap.containsKey(node.getTNname())){
					yufaNodes.add(node);
				}
			}
			// 遍历场景，而不是语法，场景框架可能有相同的
			for(String scene: scenemap.keySet()){
				mainlinsks = scenemap.get(scene);

				for (Node node: yufaNodes){
					Collection<Link> yufalinks =  yufaNs.getLinksOfSink(node.getNodeId());
					yufacore = Integer.valueOf((String) node.getProperty("core"));
					List<Link> yufalist = new ArrayList<Link>();
					for (Link link:yufalinks){
						Node source = link.getSource();
						if (AgentStarter.facaomap.containsKey(source.getTNname())){
							// 统计语法槽，额外的语法连接词等，另计，另查
							yufalist.add(link);
						}
					}
					// 如果语法槽数对应
					if (yufalist.size() == yufacore) {
						List<Link> finalyufa;
						// 预设语序，方便查找，建模稍麻烦，不是嵌套，则直接进入主语法框架，嵌套场景，暂不作为节点属性设置
						String[] listyufa = ((String) node.getProperty("listyufa")).split("_");
						// 如果语法槽数跟某层某个子场景元素数一样，则判断具体内容是否一致
						if(yufalist.size() == mainlinsks.size()){
							int fitsize = 0;
							for (Link link :mainlinsks){
								for(Link link0 : yufalist){
									if (link.getCategory().getName().equals(link0.getSource().getTNname())){
										fitsize ++;
									}
								}
							}
							// 找到全匹配，则按场景树结构，建立一个语法树结构
							if (fitsize == mainlinsks.size()) {
								yufatotal = Integer.valueOf((String) node.getProperty("total"));
								finalyufa = new ArrayList<Link> ();
								// 如果还有其他非场景元素语法连接词没取到，去记忆取，无意识里也没有，有就加入语法buffer了
								if(yufalinks.size() != yufatotal ){
									getTotal(node, finalyufa, listyufa);
								}else {
									for (int i = 0; i < listyufa.length; i++) {
										for (Link link : yufalinks){
											// 按既存的语法序列整理list
											if (String.valueOf(link.getSource().getNodeId()).equals(listyufa[i])){
												finalyufa.add(link);
											}
										}
									}
								}
								yufamap.put(scene,finalyufa);
								break;
							}
						}
					}
				}
			}
			tx.commit();
		}
		if(scenemap.size() == yufamap.size()){
			wordnum = 0;
			// 取到第一层父语法
			List<Link> mainlinsks = yufamap.get("0_" + sceneNs.getMainNodeId());
			// 同层的都顺次解决，子层的继续深挖
			for (int i = 0; i < mainlinsks.size(); i++) {
				node = mainlinsks.get(i).getSource();
				invoke(1,"0_" + sceneNs.getMainNodeId());
			}

			if(AgentStarter.scenelist.size() > 0){
				sceneNs.setMainNodeId(Integer.valueOf(AgentStarter.scenelist.get(0)));
				AgentStarter.scenelist.remove(0);
			}else {
				sceneNs.setMainNodeId(0);
			}

			yufaNs.setMainMap(null);
			sceneNs.setMainMap(null);
		}

		cancel();
	}

	public void invoke(int level0, String upmapkey) {
		String name = "";
		String mapkey = level0 + "_" + node.getTNname();
		List<Link> mainlinsks = yufamap.get(mapkey);
		isdone = false;
		if (mainlinsks == null) {
			// 不是子场景，则直接在前一层查场景元素，匹配则替换，scenename序号和点都是前一层
			for (Link scenelink:scenemap.get(upmapkey)){
				if(scenelink.getTNname().equals(node.getTNname())){
					pam.getListener().receivePercept(scenelink.getSource(), ModuleName.WordGraph);
					name = scenelink.getSource().getTNname();
					words.put(wordnum,name);
					isdone = true;
				}
			}
			// 没有场景元素匹配，直接拼接当前词
			if (!isdone) {
				name = node.getTNname();
				words.put(wordnum,name);
				pam.getListener().receivePercept(node, ModuleName.WordGraph);
			}
			// todo 内部语言重理解，重新输入pam走激活过程，主要是知道自己说了啥和正确与否
			System.out.println("新词-----------" + name);
			wordnum++;
		}else {
			for (int i = 0; i < mainlinsks.size(); i++) {
				node = mainlinsks.get(i).getSource();
				// 只能一个地方加加，深度担当，其他都按同层处理
				invoke(level0 + 1, mapkey );
			}
		}
	}

	private void getTotal(Node node, List<Link> yufalinks, String[] listyufa) {
		String query;
		List<Link> listyf = new ArrayList<Link> ();
		query = "match (n{name:\'" + node.getTNname() + "\'})<-[r:语序]-() return r";
		try (Transaction tx1 = graphDb.beginTx()) {
			try (Result result = tx1.execute(query, NeoUtil.parameters)) {
				int num = 0;
				Map<String, Object> row0;
				while (result.hasNext()) {
					row0 = result.next();
					Relationship yufacao;
					String name;
					Link link;
					for (String key0 : result.columns()) {
						yufacao = (Relationship) row0.get(key0);

						link = NeoUtil.CastNeoToLidaLink(yufacao,null);

						listyf.add(link);
					}
				}
			}
			tx1.commit();
		}

		for (int i = 0; i < listyufa.length; i++) {
			for(Link link : listyf){
				if (String.valueOf(link.getSource().getNodeId()).equals(listyufa[i])){
					yufalinks.add(link);
				}
			}
		}

	}

	private void getTree(Collection<Link> mainlinsks, Map<String, Collection<Link>> linksmap, int level) {
		for (Link link : mainlinsks) {
			Node source = link.getSource();
			if (AgentStarter.scenemap.containsKey(source.getTNname())){
				mainlinsks = sceneNs.getLinksOfSink(source.getNodeId());
				linksmap.put(level + "_" + link.getTNname(),mainlinsks);
				getTree(mainlinsks,linksmap,level + 1);
			}
		}
	}
}