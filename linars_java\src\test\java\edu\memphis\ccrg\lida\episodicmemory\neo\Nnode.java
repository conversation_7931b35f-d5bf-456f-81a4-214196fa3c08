package edu.memphis.ccrg.lida.episodicmemory.neo;

import org.neo4j.graphdb.Node;
//import org.neo4j.kernel.impl.core.EmbeddedProxySPI;
import org.neo4j.kernel.impl.core.NodeEntity;
//import org.neo4j.kernel.impl.core.NodeProxy;
import org.neo4j.kernel.impl.coreapi.InternalTransaction;
//import org.neo4j.kernel.impl.core.NodeProxy;

public class Nnode extends NodeEntity {

    public Nnode(InternalTransaction spi, long nodeId) {
        super(spi, nodeId);
    }

}
