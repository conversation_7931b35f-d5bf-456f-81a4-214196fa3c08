package edu.memphis.ccrg.lida.search;

import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
import org.opennars.entity.Task;

import java.util.List;

/**
 * 图式搜索工具接口，定义了搜索工具的基本功能。
 * 不同的搜索策略可以实现这个接口，提供统一的调用方式。
 */
public interface GraphSearchTool {
    /**
     * 执行搜索操作
     * 
     * @param terms 搜索参数，通常是操作的参数
     * @param memory 系统内存，包含当前状态
     * @param params 搜索参数配置
     * @return 搜索结果任务列表
     */
    List<Task> execute(Term[] terms, Memory memory, SearchParameters params);

    /**
     * 获取搜索类型
     * 
     * @return 搜索类型的标识符
     */
    String getSearchType();

    /**
     * 判断当前搜索工具是否适用于给定的查询
     * 
     * @param terms 查询参数
     * @param memory 系统内存
     * @return 是否适用
     */
    boolean isApplicable(Term[] terms, Memory memory);
}
