package edu.memphis.ccrg.lida.episodicmemory.neo;

import java.io.*;

public class TestGan {
    //逐行读取文件
    public static void readFileByLines(String fileName) {
        File file = new File(fileName);
        BufferedReader reader = null;
        try {
            System.out.println("以行为单位读取文件内容，一次读一整行：");
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            int line = 1;
            // 一次读入一行，直到读入null为文件结束
            while ((tempString = reader.readLine()) != null) {
                // 显示行号
                System.out.println("line " + line + ": " + tempString);
                line++;
            }
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                }
            }
        }
    }
    //全局工作空间
    public static final String WORKSPACE = "workspace";
    //模型名称
    public static final String MODEL_NAME = "model";
    //模型版本
    public static final String MODEL_VERSION = "1.0";
    //模型描述
    public static final String MODEL_DESCRIPTION = "test model";
    //模型创建时间
    public static final String MODEL_CREATION_TIME = "2017-04-01T00:00:00.000Z";
    //模型更新时间
    public static final String MODEL_UPDATE_TIME = "2017-04-01T00:00:00.000Z";
    //模型创建者
    public static final String MODEL_CREATOR = "test";
    //模型类型
    public static final String MODEL_TYPE = "Neo4j";
    //模型输入
    public static final String MODEL_INPUT = "input";
    //模型输出
    public static final String MODEL_OUTPUT = "output";
    //模型输入类型
    public static final String MODEL_INPUT_TYPE = "string";
    //模型输出类型
    public static final String MODEL_OUTPUT_TYPE = "string";

    //逐行
    public static void main(String[] args) {
        readFileByLines("/Users/<USER>/Desktop/test.txt");
    }

    //多线程
    public static void main1(String[] args) {
        for (int i = 0; i < 10; i++) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    readFileByLines("/Users/<USER>/Desktop/test.txt");
                }
            }).start();
        }
    }

    //逐字符
    public static void main3(String[] args) {
        readFileByChars("/Users/<USER>/Desktop/test.txt");
    }

    private static void readFileByChars(String s) {
        File file = new File(s);
        Reader reader = null;
        try {
            System.out.println("以字符为单位读取文件内容，一次读一个字节：");
            // 一次读一个字符
            reader = new InputStreamReader(new FileInputStream(file));
            int tempchar;
            while ((tempchar = reader.read()) != -1) {
                // 对于windows下，\r\n这两个字符在一起时，表示一个换行。
                // 但如果这两个字符分开显示时，会换两次行。
                // 因此，屏蔽掉\r，或者屏蔽\n。否则，将会多出很多空行。
                if (((char) tempchar) != '\r') {
                    System.out.print((char) tempchar);
                }
            }
            reader.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                }
            }
        }
    }

    //冒泡排序
    public static void bubbleSort(String[] arr) {
        int len = arr.length;
        for (int i = 0; i < len - 1; i++) {
            for (int j = 0; j < len - 1 - i; j++) {
                if (arr[j].compareTo(arr[j + 1]) > 0) {
                    String temp = arr[j];
                    arr[j] = arr[j + 1];
                    arr[j + 1] = temp;
                }
            }
        }
    }

    //普通排序
    public static void sort(String[] arr) {
        int len = arr.length;
        for (int i = 0; i < len - 1; i++) {
            for (int j = 0; j < len - 1 - i; j++) {
                if (arr[j].compareTo(arr[j + 1]) > 0) {
                    String temp = arr[j];
                    arr[j] = arr[j + 1];
                    arr[j + 1] = temp;
                }
            }
        }
    }





}
