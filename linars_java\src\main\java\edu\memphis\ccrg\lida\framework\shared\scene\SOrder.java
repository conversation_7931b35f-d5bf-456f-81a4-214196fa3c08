package edu.memphis.ccrg.lida.framework.shared.scene;

import org.opennars.io.Symbols;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Term;

public class SOrder extends CompoundTerm0 {
    private String id;
    private String name;
    private String status;

    @Override
    public Symbols.NativeOperator operator() {
        return null;
    }

    @Override
    public CompoundTerm clone() {
        return null;
    }

    @Override
    public Term clone(Term[] replaced) {
        return null;
    }
}
