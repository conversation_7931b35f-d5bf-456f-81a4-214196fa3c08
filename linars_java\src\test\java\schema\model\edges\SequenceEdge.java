package schema.model.edges;

import schema.model.Edge;
import schema.model.Node;

/**
 * 时序边，连接上下文节点和步骤节点，表示"包含步骤"的关系
 */
public class SequenceEdge extends Edge {
    private boolean isFirst; // 是否是时序首

    public SequenceEdge(String id, Node source, Node target, boolean isFirst) {
        super(id, source, target, isFirst ? "时序首" : "时序");
        this.isFirst = isFirst;
        setProperty("is_first", isFirst);
    }

    public boolean isFirst() {
        return isFirst;
    }
}
