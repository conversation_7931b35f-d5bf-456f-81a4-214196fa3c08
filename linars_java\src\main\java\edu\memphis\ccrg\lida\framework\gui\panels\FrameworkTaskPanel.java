/*
 * Created by <PERSON><PERSON>ormDesigner on Sat Dec 21 08:35:29 CST 2019
 */

package edu.memphis.ccrg.lida.framework.gui.panels;

import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.gui.utils.GuiUtils;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTask;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;

import javax.swing.*;
import javax.swing.table.AbstractTableModel;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableColumnModel;
import javax.swing.table.TableColumn;
import java.awt.*;
import java.text.DecimalFormat;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class FrameworkTaskPanel extends GuiPanelImpl {
    private static final Logger logger = Logger.getLogger(FrameworkTaskPanel.class.getCanonicalName());
    private Collection<FrameworkTask> tasks;
    private FrameworkTask[] taskArray;
    private FrameworkModule module;
    private TaskTableModel taskTableModel;

    /**
     * creates new {@link FrameworkTaskPanel}
     */
    public FrameworkTaskPanel() {
        taskTableModel = new TaskTableModel();
        tasks = new HashSet<FrameworkTask>();
        taskArray = tasks.toArray(new FrameworkTask[0]);
        initComponents();
    }

    @Override
    public void initPanel(String[] param) {
        if (param == null || param.length == 0) {
            logger.log(Level.WARNING,
                    "Error initializing FrameworkTaskPanel, not enough parameters.",
                    0L);
            return;
        }

        module = GuiUtils.parseFrameworkModule(param[0], agent);
        if (module != null) {
            refresh();
        } else {
            logger.log(Level.WARNING,
                    "Unable to parse module {1}. Panel not initialized.",
                    new Object[]{0L, param[0]});
        }
    }


    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents
        jToolBar1 = new JToolBar();
        refreshButton = new JButton();
        threadPane = new JScrollPane();
        tasksTable = new JTable();

        //======== this ========

        //======== jToolBar1 ========
        {
            jToolBar1.setRollover(true);

            //---- refreshButton ----
            refreshButton.setText("Refresh");
            refreshButton.setFocusable(false);
            refreshButton.setHorizontalTextPosition(SwingConstants.CENTER);
            refreshButton.setVerticalTextPosition(SwingConstants.BOTTOM);
            refreshButton.addActionListener(e -> refreshButtonActionPerformed(e));
            jToolBar1.add(refreshButton);
        }

        //======== threadPane ========
        {

            //---- tasksTable ----
            tasksTable.setModel(taskTableModel);
            tasksTable.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);
            tasksTable.setMaximumSize(new Dimension(1000, 1000));
            threadPane.setViewportView(tasksTable);
        }

        GroupLayout layout = new GroupLayout(this);
        setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createSequentialGroup()
                    .addComponent(jToolBar1, GroupLayout.DEFAULT_SIZE, 390, Short.MAX_VALUE)
                    .addContainerGap())
                .addComponent(threadPane, GroupLayout.DEFAULT_SIZE, 400, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createSequentialGroup()
                    .addComponent(jToolBar1, GroupLayout.PREFERRED_SIZE, 25, GroupLayout.PREFERRED_SIZE)
                    .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                    .addComponent(threadPane, GroupLayout.DEFAULT_SIZE, 215, Short.MAX_VALUE))
        );
        // JFormDesigner - End of component initialization  //GEN-END:initComponents
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JToolBar jToolBar1;
    private javax.swing.JButton refreshButton;
    private javax.swing.JTable tasksTable;
    private javax.swing.JScrollPane threadPane;
    // End of variables declaration//GEN-END:variables

    private void refreshButtonActionPerformed(java.awt.event.ActionEvent evt) {// GEN
        refresh();
    }// GEN-LAST:event_ApplyButtonActionPerformed

    @Override
    public void refresh() {
        if (module != null) {
            display(module.getAssistingTaskSpawner().getTasks());
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public void display(Object o) {
        logger.log(Level.FINEST, "Refreshing display", TaskManager.getCurrentTick());
        if (o instanceof Collection) {
            tasks = (Collection<FrameworkTask>) o;

            taskArray = tasks.toArray(new FrameworkTask[0]);

            ((AbstractTableModel) tasksTable.getModel()).fireTableDataChanged();
        }
    }

    /*
     * This TaskTableModel adapts the collection of FrameworkTasks to an
     * AbstractTableModel
     */
    private class TaskTableModel extends AbstractTableModel {

        private String[] columnNames = {"Task ID", "Activation", "Status", "Description", "Next scheduled tick"};
        private int[] columnAlign = {SwingConstants.RIGHT, SwingConstants.RIGHT, SwingConstants.LEFT, SwingConstants.LEFT, SwingConstants.RIGHT};
        private DecimalFormat df = new DecimalFormat("0.0000");
        private Map<String, Integer> columnAlignmentMap = new HashMap<String, Integer>();

        public TaskTableModel() {
            for (int i = 0; i < columnNames.length; i++) {
                columnAlignmentMap.put(columnNames[i], columnAlign[i]);
            }
        }

        @Override
        public int getColumnCount() {
            return columnNames.length;
        }

        @Override
        public int getRowCount() {
            return taskArray.length;
        }

        @Override
        public String getColumnName(int column) {
            String cName = "";
            if (column < columnNames.length) {
                cName = columnNames[column];
            }
            return cName;
        }

        @Override
        public Object getValueAt(int rowIndex, int columnIndex) {
            FrameworkTask task = taskArray[rowIndex];
            Object o = null;
            switch (columnIndex) {
                case 0:
                    o = task.getTaskId();
                    break;
                case 1:
                    o = df.format(task.getActivation());
                    break;
                case 2:
                    o = task.getTaskStatus();
                    break;
                case 3:
                    o = task;
                    break;
                case 4:
                    o = task.getScheduledTick();
                    break;
                case 5:
                    o = task.getNextTicksPerRun();
                    break;
                default:
                    o = "";
            }
            return o;
        }

        @Override
        public void setValueAt(Object value, int row, int column) {
        }

        @Override
        public boolean isCellEditable(int row, int column) {
            return false;
        }

        /**
         * @return the columnAlignmentMap
         */
        public Map<String, Integer> getColumnAlignmentMap() {
            return columnAlignmentMap;
        }
    }

    @SuppressWarnings("unused")
    private class AlignedColumnTableModel extends DefaultTableColumnModel {

        private DefaultTableCellRenderer render;

        public AlignedColumnTableModel() {
            render = new DefaultTableCellRenderer();
            render.setHorizontalAlignment(SwingConstants.RIGHT);
        }

        @Override
        public void addColumn(TableColumn column) {
            if (taskTableModel.getColumnAlignmentMap().get(column.getHeaderValue().toString()) == SwingConstants.RIGHT) {
                column.setCellRenderer(render);
            }
            super.addColumn(column);
        }
    }
}
