/*
 * Created by <PERSON><PERSON>ormDesigner on Sat Dec 21 08:35:29 CST 2019
 */

package edu.memphis.ccrg.lida.framework.gui.panels;

import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.globalworkspace.BroadcastListener;
import edu.memphis.ccrg.lida.globalworkspace.Coalition;
import edu.memphis.ccrg.lida.globalworkspace.GlobalWorkspace;
import edu.memphis.ccrg.lida.globalworkspace.triggers.BroadcastTrigger;

import javax.swing.*;
import javax.swing.table.AbstractTableModel;
import java.awt.*;
import java.text.DecimalFormat;
import java.util.Collection;
import java.util.LinkedList;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class GlobalWorkspaceTablePanel extends GuiPanelImpl implements
        BroadcastListener {
    private static final Logger logger = Logger.getLogger(GlobalWorkspaceTablePanel.class.getCanonicalName());
    private Collection<Coalition> coalitions;
    private Coalition[] coalitionArray = new Coalition[0];
    private GlobalWorkspace module;
    private LinkedList<BroadcastDetail> recentBbroadcasts = new LinkedList<BroadcastDetail>();
    private int recentBroadcastsSize;
    private final static int DEFAULT_RECENT_BROADCAST_SIZE = 30;

    /** Creates new form NodeStructureTable */
    public GlobalWorkspaceTablePanel() {
        initComponents();
    }


    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents
        jToolBar1 = new JToolBar();
        refreshButton = new JButton();
        jSplitPane1 = new JSplitPane();
        winnersPane = new JScrollPane();
        winnersTable = new JTable();
        coalitionsPane1 = new JScrollPane();
        coalitionsTable = new JTable();

        //======== this ========
        setPreferredSize(new Dimension(500, 320));
        setMinimumSize(new Dimension(480, 320));

        //======== jToolBar1 ========
        {
            jToolBar1.setRollover(true);

            //---- refreshButton ----
            refreshButton.setText("Refresh");
            refreshButton.setFocusable(false);
            refreshButton.setHorizontalTextPosition(SwingConstants.CENTER);
            refreshButton.setVerticalTextPosition(SwingConstants.BOTTOM);
            refreshButton.addActionListener(e -> refreshButtonActionPerformed(e));
            jToolBar1.add(refreshButton);
        }

        //======== jSplitPane1 ========
        {
            jSplitPane1.setDividerLocation(150);
            jSplitPane1.setOrientation(JSplitPane.VERTICAL_SPLIT);
            jSplitPane1.setPreferredSize(new Dimension(454, 310));

            //======== winnersPane ========
            {

                //---- winnersTable ----
                winnersTable.setModel(new WinnerCoalitionsTableModel());
                winnersPane.setViewportView(winnersTable);
            }
            jSplitPane1.setBottomComponent(winnersPane);

            //======== coalitionsPane1 ========
            {

                //---- coalitionsTable ----
                coalitionsTable.setModel(new CoalitionsTableModel());
                coalitionsPane1.setViewportView(coalitionsTable);
            }
            jSplitPane1.setTopComponent(coalitionsPane1);
        }

        GroupLayout layout = new GroupLayout(this);
        setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createSequentialGroup()
                    .addComponent(jToolBar1, GroupLayout.DEFAULT_SIZE, 394, Short.MAX_VALUE)
                    .addContainerGap())
                .addComponent(jSplitPane1, GroupLayout.Alignment.TRAILING, GroupLayout.DEFAULT_SIZE, 400, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createSequentialGroup()
                    .addComponent(jToolBar1, GroupLayout.PREFERRED_SIZE, 25, GroupLayout.PREFERRED_SIZE)
                    .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                    .addComponent(jSplitPane1, GroupLayout.DEFAULT_SIZE, 269, Short.MAX_VALUE))
        );
        // JFormDesigner - End of component initialization  //GEN-END:initComponents
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables
    private JToolBar jToolBar1;
    private JButton refreshButton;
    private JSplitPane jSplitPane1;
    private JScrollPane winnersPane;
    private JTable winnersTable;
    private JScrollPane coalitionsPane1;
    private JTable coalitionsTable;
    // JFormDesigner - End of variables declaration  //GEN-END:variables

    private void refreshButtonActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_refreshButtonActionPerformed
        refresh();
    }// GEN-LAST:event_refreshButtonActionPerformed

    @Override
    public void initPanel(String[] param) {
        module = (GlobalWorkspace) agent.getSubmodule(ModuleName.GlobalWorkspace);
        if (module == null) {
            logger.log(Level.WARNING,
                    "Error initializing NodeStructure Panel, Module does not exist in agent.",
                    0L);
            return;
        }
        module.addListener(this);

        recentBroadcastsSize = DEFAULT_RECENT_BROADCAST_SIZE;

        if(param.length > 0){
            try{
                recentBroadcastsSize = Integer.parseInt(param[0]);
            }catch(NumberFormatException e){
                logger.log(Level.WARNING, "parse error, using default recent broadcast size");
            }
        }else{
            logger.log(Level.INFO, "using default recent broadcast size");
        }
    }

    @Override
    public void refresh() {
        display(module.getModuleContent("coalitions"));
    }

    private class CoalitionsTableModel extends AbstractTableModel {

		private String[] columNames = {"CoalID_Acti_Incentive", "NodeStructure Content" }; // ,"Creating Attention Codelet","Codelet's Sought Content"
		private DecimalFormat df = new DecimalFormat("0.0000");

        @Override
        public int getColumnCount() {
            return columNames.length;
        }

        @Override
        public int getRowCount() {
            return coalitionArray.length;
        }

        @Override
        public String getColumnName(int column) {
            if (column < columNames.length) {
                return columNames[column];
            }
            return "";
        }

		@Override
		public Object getValueAt(int rowIndex, int columnIndex) {
			if (rowIndex > coalitionArray.length
					|| columnIndex > columNames.length || rowIndex < 0
					|| columnIndex < 0) {
				return null;
			}
			Coalition coal = coalitionArray[rowIndex];

			switch (columnIndex) {
				case 0:
					return coal.getId() + " _ "+ df.format(coal.getActivation()) + " _ "+ df.format(coal.getIncentiveSalience());
				case 1:
//					return df.format(coal.getActivation());
//				case 2:
//					return df.format(coal.getIncentiveSalience());
//				case 3:
					return coal.getContent();
//				case 4:
//					return coal.getCreatingAttentionCodelet();
//				case 5:
//					return coal.getCreatingAttentionCodelet().getSoughtContent();
				default:
					return "";
			}

        }
    }

    private class WinnerCoalitionsTableModel extends AbstractTableModel {

		private String[] columNames = { "Tick_Trigger", "count_activation_location",
				"Broadcast NodeStructure"};
		private DecimalFormat df = new DecimalFormat("0.0000");

        @Override
        public int getColumnCount() {
            return columNames.length;
        }

        @Override
        public int getRowCount() {
            return recentBbroadcasts.size();
        }

        @Override
        public String getColumnName(int column) {
            if (column < columNames.length) {
                return columNames[column];
            }
            return "";
        }

		@Override
		public Object getValueAt(int rowIndex, int columnIndex) {
			if (rowIndex > recentBbroadcasts.size()
					|| columnIndex > columNames.length || rowIndex < 0
					|| columnIndex < 0) {
				return null;
			}
			BroadcastDetail bd = recentBbroadcasts.get(rowIndex);
			switch (columnIndex) {
				case 0:
                    BroadcastTrigger trigger = bd.getLastBroadcastTrigger();
                    if (trigger != null) {
                        return bd.getTickAtBroadcast() + "_"
                                + trigger.getClass().getSimpleName();
                    }
                    return "";
				case 1:
					return bd.getBroadcastSentCount() + "__" +
                            df.format(bd.getWinnerCoalActivation()) + "__"
//                            + bd.getBroadcastContent().getSceneSite().getName()
                            ;
				case 2:
					return bd.getBroadcastContent().getNodes();

				default:
					return "";
			}
		}
	}

    @SuppressWarnings("unchecked")
    @Override
    public void display(Object o) {
        // Collections.unmodifiableCollection(coalitions)
        coalitions = (Collection<Coalition>) o;
        coalitionArray = coalitions.toArray(new Coalition[0]);

		((AbstractTableModel) winnersTable.getModel())
				.fireTableStructureChanged();

		((AbstractTableModel) coalitionsTable.getModel())
				.fireTableStructureChanged();
	}

    @Override
    public void learn(Coalition coalition) {
        // No learning in panel
    }

	@Override
	public void receiveBroadcast(Coalition coalition) {
		BroadcastDetail bd = new BroadcastDetail((NodeStructure) coalition
				.getContent(), coalition.getActivation(),
				(BroadcastTrigger) module
						.getModuleContent("lastBroadcastTrigger"), TaskManager
						.getCurrentTick(), module.getBroadcastSentCount());
		synchronized (this) {
			recentBbroadcasts.addFirst(bd);
			if (recentBbroadcasts.size() > recentBroadcastsSize) {
				recentBbroadcasts.pollLast();
			}
		}
	}

    private class BroadcastDetail {

        private final NodeStructure broadcastContent;
        private final double winnerCoalActivation;
        private final BroadcastTrigger lastBroadcastTrigger;
        private final long tickAtBroadcast;
        private final long broadcastSentCount;

		public BroadcastDetail(NodeStructure broadcastContent,
				double winnerCoalActivation,
				BroadcastTrigger lastBroadcastTrigger, long tickAtBroadcast,
				long broadcastSentCount) {
			this.broadcastContent = broadcastContent;
			this.winnerCoalActivation = winnerCoalActivation;
			this.lastBroadcastTrigger = lastBroadcastTrigger;
			this.tickAtBroadcast = tickAtBroadcast;
			this.broadcastSentCount = broadcastSentCount;
		}

        /**
         * @return the broadcastContent
         */
        public NodeStructure getBroadcastContent() {
            return broadcastContent;
        }

        /**
         * @return the winnerCoalActivation
         */
        public double getWinnerCoalActivation() {
            return winnerCoalActivation;
        }

        /**
         * @return the lastBroadcastTrigger
         */
        public BroadcastTrigger getLastBroadcastTrigger() {
            return lastBroadcastTrigger;
        }

        /**
         * @return the tickAtBroadcast
         */
        public long getTickAtBroadcast() {
            return tickAtBroadcast;
        }

        /**
         * @return the broadcastSentCount
         */
        public long getBroadcastSentCount() {
            return broadcastSentCount;
        }
    }
}
