/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.data.TermUtil;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamNode;
import edu.memphis.ccrg.linars.Memory;
import org.neo4j.graphdb.Relationship;
import org.neo4j.graphdb.Result;
import org.neo4j.graphdb.Transaction;

import java.util.Arrays;
import java.util.Map;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;


/**
 */
public class DoSimpleSceneTask extends FrameworkTaskImpl {
	private String sources;
	private String sinks;
	private Node sink;
	private Node source;
	private PAMemory pam;
	private NodeStructure sceneNs;

	private NodeStructure goalNs;
	private String actStamp;

	/**
	 * Default constructor
	 * @param pam {@link PAMemory}
	 */
	public DoSimpleSceneTask(Node sink, Node source, PAMemory pam, NodeStructure goalNs, String actStamp) {
		super(1, "tact");
		this.sink = sink;
		this.source = source;
		this.pam = pam;
//		this.sceneNs = sceneNs;
		this.goalNs = goalNs;
		this.actStamp = actStamp;
	}

//	public DoSimpleSceneTask(String sink, String source, PAMemory pam, NodeStructure sceneNs) {
//		super(1, "tact");
//		this.sinks = sink;
//		this.sources = source;
//		this.pam = pam;
//		this.sceneNs = sceneNs;
//	}

	/**
	 */
	@Override
	protected void runThisFrameworkTask() {
		// 用于语法与场景整合，适用场景与语法分离的情况。目前可能不分离
		pam.setSceneMainNode(sink);

		// 待生成父场景，根场景，单句
//		pam.getSceneNode(sink,sink.getTNname(),false);

		nar.addInputTo("(^say,{SELF}," + sink.getTNname() + ")! :|:", (Memory) goalNs);

		// 变量句，定义变量并赋值，用具体值实例化，提交到参数表中
		varTask(sink, sink.getTNname());
		// 用于某些初始化
		varTask(sink, null);

		System.out.println("执行时序中---|||-SimpleSceneTask");

		DoSuccTask doSuccTask = new DoSuccTask(sink,source,pam,60, actStamp);
		pam.getAssistingTaskSpawner().addTask(doSuccTask);

		System.out.println("目前任务总数-----------------》 " + pam.getAssistingTaskSpawner().getTasks().size());

		AgentStarter.isDoVar = true;
		AgentStarter.doStartick = TaskManager.getCurrentTick();

		cancel();
	}

	public static void varTask(Node sink, String change) {
		String query;
		if(change == null || change.equals("")) {
			// 初始化变量，没有参考句
			query = "match (n)-[r:初始句]->(m) where n.name = \'" + sink.getTNname()  + "\' return r";
		}else {
			query = "match (n)-[r:变量句]->(m) where n.name = \'" + sink.getTNname()  + "\' return r";
		}
		System.out.println("query = " + query);
		Link link1 = null;
		try (Transaction tx0 = graphDb.beginTx()) {
			try (Result result0 = tx0.execute(query, NeoUtil.parameters)) {
				Map<String, Object> row0;
				while (result0.hasNext()) {
					row0 = result0.next();
					Relationship actre;
					for (String key0 : result0.columns()) {
						actre = (Relationship) row0.get(key0);

						link1 = NeoUtil.CastNeoToLidaLink(actre, null);
						Node toNode = (Node) link1.getSink();

						if (change == null || change.equals("")) {
							// 初始化变量，没有参考句
							String[] gTerms = toNode.getTNname().split(",");
							if (gTerms[0].contains("$")) {
								PamNode type = TermUtil.getPamNode(gTerms[0], 20001 + 5);
								PamNode value = TermUtil.getPamNode(gTerms[1], 20001 + 5);
								IsaPamTask.makeNowisa(type, value);
							}
						}else {
							String[] pTerms = change.split(",");
							String[] gTerms = toNode.getTNname().split(",");

							if (pTerms.length != gTerms.length) {
								System.out.println("变量句中变量个数不匹配---" + Arrays.toString(pTerms) + "----"+ Arrays.toString(gTerms));
								return;
							} else {
								for (int i = 0; i < pTerms.length; i++) {
									if (gTerms[i].contains("$")) {
//									if(gTerms[i].contains("加数")) {
//										continue;
//									}
										// .replace("$","")
										PamNode type = TermUtil.getPamNode(gTerms[i].replace(")", ""), 20001 + i);
										PamNode value = TermUtil.getPamNode(pTerms[i].replace(")", ""), 20001 + i);
										IsaPamTask.makeNowisa(type, value);
										System.out.println("---------时序---变量定义----------" + type.toString() + " = " + value.toString());
									}
								}
							}
						}

//						System.out.println("---------时序---变量定义----------" + link1.toString());
					}
				}
			}
			tx0.commit();
		}
	}
}
