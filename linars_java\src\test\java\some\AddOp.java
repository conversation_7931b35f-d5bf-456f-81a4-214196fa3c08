package some;

public class AddOp {
    public static void main(String[] args) {
        String num1 = "123456789"; // 加数1
        String num2 = "987654321"; // 加数2

        String result = addLargeNumbers(num1, num2);
        System.out.println("最终得数是：" + result);
    }

    public static String addLargeNumbers(String num1, String num2) {
        StringBuilder result = new StringBuilder();
        int carry = 0; // 初始化进位为0
        int i = num1.length() - 1, j = num2.length() - 1; // 从两个加数的最低位（个位）开始

        // 第二步：从两个加数的最低位开始，即当前位数为个位数。对于每个当前位数，执行以下操作：
        while (i >= 0 || j >= 0 || carry != 0) {
            int sum = carry;
            if (i >= 0) {
                sum += num1.charAt(i--) - '0'; // 截取加数1的当前位数对应数字，称为数一数
            }
            if (j >= 0) {
                sum += num2.charAt(j--) - '0'; // 截取加数2的当前位数对应数字，称为数二数
            }

            // 使用字符串截取方法来代替直接计算和的操作
            String sumStr = String.valueOf(sum);
            carry = sum >= 10 ? 1 : 0; // 如果和大于等于10，设置进位为1，否则进位为0
            result.append(sumStr.charAt(sumStr.length() - 1)); // 从和的个位数得到当前位数的数字
        }

        // 第三步：检查最高位是否产生进位。如果进位不为0，需要在得数的最前面添加进位值。
        if (carry != 0) {
            result.append(carry);
        }

        // 第四步：从最高位开始，将所有位数的数字拼接起来，得到最终的得数。
        return result.reverse().toString();
    }

    public static String addLargeNumbers0(String num1, String num2) {
        // 第一步：初始化得数为空字符串。
        StringBuilder result = new StringBuilder();
        // 初始化进位为0
        int carry = 0;
        // 从两个加数的最低位（个位）开始
        int i = num1.length() - 1;
        int j = num2.length() - 1;

        char result_now = '0';

        // 第二步：从两个加数的最低位开始，即当前位数为个位数。
        // 对于每个当前位数，执行以下操作：
        while (i >= 0 || j >= 0 || carry != 0) {
            int sum = carry;
            if (i >= 0) {
                // 截取加数1的当前位数对应数字，称为数一数
                sum += num1.charAt(i--) - '0';
            }
            if (j >= 0) {
                // 截取加数2的当前位数对应数字，称为数二数
                sum += num2.charAt(j--) - '0';
            }

            // 使用字符串截取方法来代替直接计算和的操作
            String sumStr = String.valueOf(sum);
            // 如果和大于等于10，设置进位为1，否则进位为0
            if (sum >= 10) {
                carry = 1;
            } else {
                carry = 0;
            }
            // 从和的个位数得到当前位数的数字
            result_now = sumStr.charAt(sumStr.length() - 1);
            // 将当前位数的数字添加到得数中
            result.append(result_now);
        }

        // 第三步：检查最高位是否产生进位。如果进位不为0，需要在得数的最前面添加进位值。
        if (carry != 0) {
            result.append(carry);
        }

        // 第四步：从最高位开始，将所有位数的数字拼接起来，得到最终的得数。
        return result.reverse().toString();
    }

    // 自定义截取字符串方法，越界返回空字符串
    public static String safeSubstring(String str, int beginIndex) {
        if (str == null || beginIndex < 0 || beginIndex >= str.length()) {
            return null;
        }
        return str.substring(beginIndex, beginIndex + 1);
    }

    public static String addStrings(String num1, String num2) {
        // 获取两个字符串的最大长度
        int maxLength = Math.max(num1.length(), num2.length());
        StringBuilder result = new StringBuilder();
        int carry = 0; // 进位

        // 遍历每一位，直到达到最长长度或进位为0
        for (int i = 0; i < maxLength || carry > 0; i++) {
            // 获取num1当前位的数字
            int digit1 = getDigit(num1, maxLength - 1 - i);
            // 获取num2当前位的数字
            int digit2 = getDigit(num2, maxLength - 1 - i);
            // 计算当前位的和
            int sum = digit1 + digit2 + carry;

            
            // 这部分可以视为进位，我们直接截取字符串
            // int tens = sum / 10;
            // int ones = sum % 10;

            // 拆分和的十位和个位，分别获取
            int tens = getDigit(String.valueOf(sum), 1);
            int ones = getDigit(String.valueOf(sum), 0);

            // 以和的十位数为进位
            carry = tens;

            // 将个位数添加到结果字符串
            result.insert(0, ones);
        }

        // 将结果字符串转换为普通字符串并返回
        return result.toString();
    }

    // 调用自定义截取方法获取当前位数字，如果越界则返回0
    private static int getDigit(String num, int index) {
        String digitStr = safeSubstring(num, index);

        if (digitStr == null) {
            return 0;
        }else{
            return Integer.parseInt(digitStr);
        }

        // return digitStr.isEmpty() ? 0 : Integer.parseInt(digitStr);
    }
}