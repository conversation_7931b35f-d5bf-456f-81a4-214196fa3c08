package edu.memphis.ccrg.lida.motivation.proceduralmemory;

import edu.memphis.ccrg.lida.actionselection.ActionImpl;
import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.initialization.GlobalInitializer;
import edu.memphis.ccrg.lida.framework.shared.*;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTask;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.globalworkspace.Coalition;
import edu.memphis.ccrg.lida.proceduralmemory.ProceduralMemory;
import edu.memphis.ccrg.lida.proceduralmemory.ProceduralMemoryImpl;
import edu.memphis.ccrg.lida.proceduralmemory.Scheme;
import org.neo4j.graphdb.Relationship;
import org.neo4j.graphdb.Result;
import org.neo4j.graphdb.Transaction;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;

/**
 * First attempt at a motivationally supportive {@link ProceduralMemory}.
 * <AUTHOR> J McCall
 */
public class MotivationProceduralMemory extends ProceduralMemoryImpl {

	private static final Logger logger = Logger.getLogger(MotivationProceduralMemory.class.getCanonicalName());
	private boolean isLesioned;
	private LinkCategory temporalCategory;
	
	@Override
	public void init() {
		super.init();
		Object o = GlobalInitializer.getInstance().getAttribute("agent.isLesioned");
		if(o instanceof Boolean){
			isLesioned = (Boolean) o;
		}else{
			logger.log(Level.WARNING, "Failed to retrieve parameter: \"agent.isLesioned\" from Global Initializer");
		}
	}
	/**
	 * @param b flag specifying lesioned or not.
	 */
	public void setLesioned(boolean b) {
		isLesioned = b;
	}

	/**
	 * Sets the temporal {@link LinkCategory}.
	 * @param c the {@link LinkCategory}
	 */
	void setTemporalLinkCategory(LinkCategory c) {
		temporalCategory = c;
	}

	// todo 明确的语义时序在动机模块，参数化动作序列在执行模块，从上到下接地和从下到上抽象
	// 时序是动作的语义条件，动作是时序的参数结果。语义大时序不冲突，动作细部参数冲突，可融合动作，动机解决语义冲突
	// 学习时序时，语义时序先，动作参数后，熟练后，可内隐执行习惯性动作。这是反馈学习，一点点修正参数。多形态动作体系
	// 借助机算学习，降低遗忘率，抑制反馈学习，如直接识别骨架动作，一次性迁移，反馈学习只是调整参数，低精度低效耗时
	// 思维时序不经过动作模块，直接动机后调用思维时序线程，思维时序是内部接地，偶尔与动作模块交叉嵌套
	// 动机反馈在pam？反馈与注意对比，结果预测转注意。动机按帧输出动作？反馈帧可中断，直接执行整个时序难中断

	// 快速应激反应=无广播=无需整合多个激励，小脑等部分？全部不广播=只通达？应激=不通达
	// 广播和言行有不应期，且buffer需要积累+遍历+整体考虑+组装行为，需要独立task和模块
	// pam只是点边联动，分配信息到模块，不参与具体的模块交互，不过联动本身就是处理
	// 独立模块可以并行查记忆，建模动作和内容 = 行为+语言+视觉想象
	@Override
	public void receiveBroadcast(Coalition coalition) throws Exception {
		NodeStructure ns = (NodeStructure) coalition.getContent();

		Set<Scheme> schemes = new HashSet<>();
		Scheme sss;
		ActionImpl action;
		String query;
		// 	时序计划 = 最短路径？
		try (Transaction tx = graphDb.beginTx()) {
			NodeStructure seqbuffer = seqGraph.getBufferContent(null);
			int size = seqbuffer.getNodes().size();
			// 无序构建+无序遍历=不是直接找根节点等，允许中间时序随时插队执行，只要为真
			// 编译器是倒序执行，根据根节点遍历，精确但不灵活精简
			// 随时受动机调控插队，随时外显描述，动机可主动中断某线程？
			// 守株待兔不可靠+不可控，注意切换太频繁和随机，约束搜索后扩散激活
			// buffer点集内和边集内平等，关联=次序+条件结果，需要优先度？
			// 随时判断状态为真，主动激活对应时序内动作，直接执行下面，不用等每次遍历判断真假
			for (Node broadcastNode: seqbuffer.getNodes()) {
				boolean isreal = false;
				int lsize = 0;
				// 不能成真，再大的激励也只是虚拟计划，需要到点执行，时间也是条件之一
				for(Link link : seqbuffer.getLinksOfSink(broadcastNode.getTNname())){
					Node source = link.getSource();
					int sourcetrunth = source.getTruth();
					String cate = link.getCategory().getName();
					if (cate.equals("顺承")){
						lsize ++;
						if(sourcetrunth == 3 || sourcetrunth == 5) {
							isreal = true;	// eat来源，前者get-food
						}
					}
				}
				// 前序为0，说明孤立，或者时序内最前位，以计划图结构为准
				if (lsize == 0) {
					for (Link link : seqbuffer.getLinksOfSink(broadcastNode.getTNname())) {
						Node source = link.getSource();
						String cate = link.getCategory().getName();
						if (cate.equals("时序")) {
							// 时序间关联，当前动作如果是时序第一位，前序为真，则可执行
							for (Link slink : seqbuffer.getLinksOfSink(source.getTNname())) {
								Node ss = slink.getSource();
								String cate0 = slink.getCategory().getName();
								int sstrunth = ss.getTruth();
								if (cate0.equals("顺承")) {
									// 无论是直连顺承，还是通过上位关联，都算入前序
									lsize ++;
									if (sstrunth == 3 || sstrunth == 5) {
										isreal = true;
									}
								}
							}
						}
					}
				}

				if (lsize != 0 && !isreal) {
					continue;
				}

//				if (actBuffer.containsNode(broadcastNode.getNodeId())) {
//					actBuffer.decayNodeStructure(15);
//					if (!broadcastNode.getName().equals("ss1")) {
//						continue;
//					}
//				}

				for (String lb: broadcastNode.getLabels()){
					if (lb.equals("具身动作")){

//						actBuffer.addNode(broadcastNode,true);

						query = "match (n:具身动作{name:\'"+ broadcastNode.getTNname() +"\'})<-[r:动作]-() return r";
						try (Result result0 = tx.execute( query, NeoUtil.parameters) ) {

							Map<String, Object> row0;
							String schLabel;
							while (result0.hasNext()) {
								row0 = result0.next();
								Relationship re0;
								for ( String key0 : result0.columns() ) {
									re0 = (Relationship) row0.get(key0);

									sss = getNewScheme();
									schLabel = (String)re0.getStartNode().getProperty("name");
									sss.setName(schLabel);

									action = new ActionImpl(schLabel);
									action.setId(broadcastNode.getNodeId());

									sss.setIncentiveSalience(broadcastNode.getTotalIncentiveSalience());
                                    sss.setActivation(broadcastNode.getActivation());

									sss.setAction(action);
									schemes.add(sss);
								}
							}
						}
					}
				}
			}
			tx.commit();
		}

		contextSchemeMap.put(0,schemes);

		// Spawn a new task to activate and instantiate relevant schemes.
		// This task runs at the next time and only one time.
		//产生一个新任务来激活和实例化相关方案。 //此任务仅在下一次运行，异步

        FrameworkTask t = new MotivationProceduralMemoryTask(ns);
		taskSpawner.addTask(t);
	}

	public void dijkstra(NodeStructure ns, Node emotionNode, Node goalNode){
		int numOfVexs = ns.getNodes().size();
		boolean[] st = new boolean[numOfVexs];// 默认初始为false
		int[] distance = new int[numOfVexs];// 存放源点到其他点的距离

	}

	private class MotivationProceduralMemoryTask extends FrameworkTaskImpl {
		
		private NodeStructure bcastContent;

		public MotivationProceduralMemoryTask(NodeStructure ns) {
			bcastContent = ns;
		}

		@Override
		protected void runThisFrameworkTask() {

//			Set<Scheme> candidateSchemes = new HashSet<Scheme>();
//			for (Node n: broadcastBuffer.getNodes()) {
//				// 与以前方法不一样，但以前也有激励=如食物
//				double incentiveSalience = 0;
//				if(isLesioned){
//					incentiveSalience = n.getIncentiveSalience();
//				}else{
//					incentiveSalience = getModelBasedIncentiveSalience(n);
//				}
//				if (incentiveSalience >= 0) {
//					Set<Scheme> schemes = contextSchemeMap.get(n.getConditionId());
//					if(schemes != null){
//						candidateSchemes.addAll(schemes);
//					}
//				}
//			}
//			for (Scheme scheme: candidateSchemes) {
//				if (shouldInstantiate(scheme, broadcastBuffer)) {
//					createInstantiation(scheme);
//				}
//			}

			for (Scheme scheme: contextSchemeMap.get(0)) {
				if (shouldInstantiate(scheme, broadcastBuffer)) {

//				    scheme.setIncentiveSalience();

					createInstantiation(scheme);
				}
			}

			cancel();
		}

		private double mbIncentiveSalienceSum;
		private int mbNodeCount;

		// 整合所有激励
		//TODO pass variables in recursive call
		private double getModelBasedIncentiveSalience(Node n) {
//			logger.log(Level.INFO, "Content before MBIS: {0}", bcastContent.toString());

			mbIncentiveSalienceSum = n.getIncentiveSalience();
			mbNodeCount = 1;
			// 与以前无激励方法不一样，这里整合了所有激励=非单个+非恒正向，取平均？
			// 激励是价值本身，来源是多巴胺和情绪，绑定链接而非节点，因为单点多边，统计点总激励
            // 目标言行和目标事物都是点，lida原pm里n是条件池里的点，其他无法触发言行
			// 目标和言行都基本出现在csm，没意识无法选，无意识只对选择有影响，言行包括预测到的
			auxGetModelBasedIncSal(n);
			return mbIncentiveSalienceSum/mbNodeCount;
		}

		private void auxGetModelBasedIncSal(Node n){
//			Map<Linkable, Link> sinkMap = (Map<Linkable, Link>) bcastContent.getConnectedSinks(n);
			Set<Link> sinkSet = bcastContent.getConnectedSinks(n);
			for(Linkable lnk: sinkSet){
//				if(lnk instanceof Node && !(lnk instanceof FeelingNode)){
//					Link l = sinkMap.get(lnk);
//					if(l != null && temporalCategory.equals(l.getCategory())){
//						mbIncentiveSalienceSum += ((Node)lnk).getIncentiveSalience();
//						mbNodeCount++;
//						auxGetModelBasedIncSal((Node)lnk);
//					}
//				}
			}
		}
	}
}