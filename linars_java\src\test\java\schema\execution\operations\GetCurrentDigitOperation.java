package schema.execution.operations;

import schema.execution.ExecutionContext;
import schema.execution.Operation;

/**
 * 获取当前位操作，从操作数中获取当前处理的位
 */
public class GetCurrentDigitOperation implements Operation {
    @Override
    public Object execute(ExecutionContext context) {
        String operand1 = (String) context.getVariable("操作数1");
        String operand2 = (String) context.getVariable("操作数2");
        int currentIndex = (int) context.getVariable("当前位索引");
        
        int digit1 = (currentIndex < operand1.length()) ? 
                     Character.getNumericValue(operand1.charAt(operand1.length() - 1 - currentIndex)) : 0;
        int digit2 = (currentIndex < operand2.length()) ? 
                     Character.getNumericValue(operand2.charAt(operand2.length() - 1 - currentIndex)) : 0;
        
        context.setVariable("位1", digit1);
        context.setVariable("位2", digit2);
        
        return null;
    }
}
