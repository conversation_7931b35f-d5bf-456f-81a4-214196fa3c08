/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamNode;
import edu.memphis.ccrg.lida.pam.PamNodeImpl;
import edu.memphis.ccrg.linars.Memory;
import org.jetbrains.annotations.NotNull;
import org.neo4j.graphdb.Relationship;
import org.neo4j.graphdb.Result;
import org.neo4j.graphdb.Transaction;

import java.util.*;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;

/**
 */
public class DoSelectTreeTask extends FrameworkTaskImpl {
	private Node sink;
	private Node source;
	private Link link;
	private static PAMemory pam;
	private NodeStructure sceneNs;
	private NodeStructure goalNs;
	private NodeStructure seqNs;
	private NodeStructure yufaNs;
	private static int elsesize = 0;
	private static List<Link> elseLinks;
	private static Map<Long, List<Link>> elsemap;
	private  List<Link> cosclist;
	private String actStamp;
	/**
	 * Default constructor
	 * @param pam {@link PAMemory}
	 */
	public DoSelectTreeTask(Link link, PAMemory pam, NodeStructure goalNs, NodeStructure sceneNs, String actStamp) {
		super(1, "tact");
		this.link = link;
		this.pam = pam;
		this.sceneNs = sceneNs;
		this.goalNs = goalNs;
		this.actStamp = actStamp;
	}

	@Override
	protected void runThisFrameworkTask() {
		seqNs = pam.getWorkspaceBuffer("seq").getBufferContent(null);
		yufaNs = pam.getWorkspaceBuffer("yufa").getBufferContent(null);
		// 进入判断结构体内，需要存入上位时序，以便回溯，判断遍历也只需一条上位边
		seqNs.getDoMainPath_map().get(actStamp).add(link);
		// 如果有else，则需要用到，但无论有没有都要存下来
		elsemap = new HashMap<>();

		String query = "match (m:场景)-[r:判断首]->(i:场景) where m.name = \'" + link.getSink().getTNname() + "\' return r";
		// todo 动机不同，描述不同，具体心算按具体分支
		// 结构体执行，判断、循环等类似
		doSelectRoot(link, query);

		cancel();
	}

	private void doSelectRoot(Link link, String query) {
		sink = (Node) link.getSink();
		source = link.getSource();
		// 从判断首开始执行，递归查找到最上头时序
		System.out.println("query = " + query);
		Link link0 = null;
		try (Transaction tx0 = graphDb.beginTx()) {

//			link0 = getLink(query, link0, tx0);
			link0 = NeoUtil.getOneLinkTx(query, tx0);

			if (link0 != null) {
				Node headsink = (Node) link0.getSink();
				List<Link> cLinkList = new ArrayList<Link> ();
				int donenum = 0;
				cosclist = new ArrayList<Link>();

				Done done = getResult(link, headsink, cLinkList, donenum, cosclist);

				// todo 各种复杂条件嵌套，与或非是认知层，目前硬编码，先低层次小范围试错
				// 如果满足当前条件集，则直接输出当前，不继续进行下面判断，上面没满足，需要继续，直到满足或完成
				if((done.donenum == cLinkList.size() && done.conectstr.equals("且"))
						||(done.donenum == 1 && done.conectstr.equals("或"))
						|| (done.donenum == 1 && cLinkList.size() == 1)){

					if (done.conect != null) cosclist.add(done.conect);
//					pam.setSceneMainNode((Node) link0.getSink());

					StringBuilder sb0 = getStringBuilderSafe(cosclist);

					if(sb0.length() > 0){
						nar.addInputTo("(^say,{SELF},(#," + sb0.toString() + "))! :|:", (Memory) goalNs);
						System.out.println("---------判断首和判断---|||---s-a-y----" + sb0.toString());
					}else {
						System.out.println("---------判断首和判断----s-a-y----空--------------" + link0.toString());
					}

					// 自助激活语法
//					GrammarTask task = new GrammarTask(yufaNs, sceneNs,1,pam);
//					pam.getAssistingTaskSpawner().addTask(task);

					System.out.println("目前任务总数-------------》 " + pam.getAssistingTaskSpawner().getTasks().size());
					AgentStarter.isDoVar = true;
					AgentStarter.doStartick = TaskManager.getCurrentTick();

					Node isasource;
					Node isasink;
					for(Link scenelink : sceneNs.getLinks()){
						scenelink.setActivation(scenelink.getActivation() + 0.4);
						isasource = scenelink.getSource();
						isasink = (Node) scenelink.getSink();
						isasource.setActivation(isasource.getActivation() + 0.3);
						isasink.setActivation(isasink.getActivation() + 0.3);
					}
					System.out.println("选择结构执行时序中---|||-SelectTreeTask");

					//接下来是执行判断结构体内的时序，设置成任务，有效跟当前待生成错开时间
					DoSelectSeqTask selectSeqTask = new DoSelectSeqTask(link0, pam, 80, actStamp);
					pam.getAssistingTaskSpawner().addTask(selectSeqTask);
				}else {
					// 从判断首开始顺着时序链执行，link0是各条判断边
					query = "match (n:场景)-[r:判断]->(m:场景)<-[r0:顺承]-(i:场景) where n.name = \'" + link0.getSource().getTNname() + "\'  and i.name = \'" + link0.getSink().getTNname()  + "\' return r";

					doSelectRoot(link0, query);
				}
			}else {
				// 没有连续判断 else if 后的最后else，实际上还是一堆判断，且要汇总上面的所有反面，跟else if还是有区别
				// 不能直接说“否则”，“否则”是概括描述时简略说法，或者代码上的简略写法，实际描述需要有理有据，离不开“因为所以”
				// 但可以省略部分判断，直接否定else之前的所有判断结构即可，一个if或else if可以只需一部分即可否定
				query = "match (n:场景)-[r:else]->(m:场景)<-[r0:顺承]-(i:场景) where n.name = \'" + source.getTNname() + "\'  and i.name = \'" + sink.getTNname()  + "\' return r";

//				link0 = getLink(query, link0, tx0);
				link0 = NeoUtil.getOneLinkTx(query, tx0);

				if (link0 != null) {
					pam.addDefaultNode((Node) link0.getSink());
					pam.setSceneMainNode((Node) link0.getSink());
					int conum = 0;
					StringBuilder sb = new StringBuilder();
					for(Long linkid: elsemap.keySet()){
						for(Link cslink : elsemap.get(linkid)){
							if(cslink.getId() == linkid){
								conum ++;
								PamNode category = new PamNodeImpl();
								category.setNodeName("条件" + conum);

								// 新建各条件到else节点的链接，并激活相关语法，尾节点是else边尾节点
								Link elseLink = pam.addDefaultLink(cslink.getSource(), link0.getSink(), category);
//								pam.activGrammarLink(elseLink, elseLink.getTNname());

//								System.out.println("---------else条件---|||----say---" + elseLink.toString());
								String sn = elseLink.getSource().getTNname();
								if (sn.contains("(")){

								}else if (conum != elsemap.get(linkid).size()) {
									sb.append(sn + ",");
								} else {
									sb.append(sn);
								}
							}else {
								// 非条件边，直接激活语法
//								pam.activGrammarLink(cslink,cslink.getTNname());

//								System.out.println("---------else非条件---|||----say--" + cslink.toString());
								String sn = cslink.getSource().getTNname();
								if (sn.contains("(")){

								}else if (conum != elsemap.get(linkid).size()) {
									sb.append(sn + ",");
								} else {
									sb.append(sn);
								}
							}
						}
					}
					// sb不为null或者空字符串，则输出say
					if(sb.length() > 0){
						nar.addInputTo("(^say,{SELF},(#," + sb.toString() + "))! :|:", (Memory) goalNs);
						System.out.println("------else---判断首和判断---|||---s-a-y----" + sb.toString());
					}else {
						System.out.println("------else---判断首和判断----s-a-y----空--------------" + link0.toString());
					}

					if(elsemap.size() > 1){
						System.out.println("---------------else条件数量大于1------------------");
						// todo 复杂的与或非else，需要硬编码很多
					}

					// 自助激活语法
//					GrammarTask task = new GrammarTask(yufaNs, sceneNs,1,pam);
//					pam.getAssistingTaskSpawner().addTask(task);

					System.out.println("else执行时序中---|||-SelectTreeTask");

					//接下来是执行判断结构体内的时序，设置成任务，有效跟当前待生成错开时间，以else边为准往下执行
					DoSelectSeqTask selectSeqTask = new DoSelectSeqTask(link0, pam, 60, actStamp);
					pam.getAssistingTaskSpawner().addTask(selectSeqTask);

					System.out.println("目前任务总数-------------》 " + pam.getAssistingTaskSpawner().getTasks().size());

					AgentStarter.isDoVar = true;
					AgentStarter.doStartick = TaskManager.getCurrentTick();
				}else {
					// 执行条件=没有else+结束判断遍历，else不会与回溯同时进行
					List<Link> seqMainPath = new ArrayList<>();
					boolean isonlyif = false;
					// 判断时序执行完和判断遍历完，只会出现一个，时序执行完后，直接回溯上位时序，而非上位判断边
					seqMainPath = seqNs.getDoMainPath_map().get(actStamp);
					if(seqMainPath != null && !seqMainPath.isEmpty()){
						Link plink1 = seqMainPath.get(seqMainPath.size() - 1);
						// 判断边是连续互斥else if，而连续独立if else是连续时序
						if(plink1.getCategory().equals("判断") || plink1.getCategory().equals("判断首")){
							if(seqMainPath.size() > 1){
								plink1 = seqMainPath.get(seqMainPath.size() - 2);
								seqMainPath.remove(seqMainPath.size() - 1);
							}else {
								isonlyif = true;
							}
						}
						if(!isonlyif){
							// 回溯后按常规往下执行
							DoSuccTask doSuccTask = new DoSuccTask((Node) plink1.getSink(),plink1.getSource(),pam,30, actStamp);
							pam.getAssistingTaskSpawner().addTask(doSuccTask);
						}
						seqMainPath.remove(seqNs.getDoMainPath_map().get(actStamp).size() - 1);
					}

					System.out.println("------------------各判断都不满足，或者查询出错---------------------");
				}
			}
			tx0.commit();
		}
	}

	@NotNull
	public static DoSelectTreeTask.Done getResult(Link link, Node headsink, List<Link> cLinkList, int donenum, List<Link> cosclist) {
		String linktype;
		for (int i = 1; i < 3; i++) {
			linktype = "条件" + i;
			Link clink = NeoUtil.getOneLink(headsink, linktype, "<", "场景", "场景");
			if (clink != null) {
				cLinkList.add(clink);
			}
		}
		Link conect = NeoUtil.getOneLink(headsink, "关系12", "<", "场景", null);

		if (!cLinkList.isEmpty()) {
			donenum = getDonenum(link, cLinkList, donenum, conect, cosclist);
		}
		String conectstr = "";
		if(conect != null){
			conectstr = conect.getSource().getTNname();
		}
		Done result = new Done(donenum, conect, conectstr);
		return result;
	}

	public static class Done{
		public final int donenum;
		public final Link conect;
		public final String conectstr;

		public Done(int donenum, Link conect, String conectstr) {
			this.donenum = donenum;
			this.conect = conect;
			this.conectstr = conectstr;
		}
	}
	// 并发安全：通过创建cosclist的快照来避免并发修改异常。
	// 空指针防护：在调用getSource()后增加了空指针检查。
	// 代码清晰性：改善了变量命名，使其更具描述性。
	// 逻辑简化：简化了条件判断，使代码更加直观。
	@NotNull
	public static StringBuilder getStringBuilderSafe(List<Link> cosclist) {
		StringBuilder stringBuilder = new StringBuilder();
		int currentIndex = 0;
		List<Link> copyOfCosclist = new ArrayList<>(cosclist); // 创建cosclist的快照以防并发修改
//		System.out.println(copyOfCosclist);
		for (Link currentLink : copyOfCosclist) {
			Node source = currentLink.getSource();
			if (source == null) { // 防止空指针异常
				continue;
			}
			String sourceName = source.getTNname();
			if (sourceName.contains("(")) {
				continue;
			}
			stringBuilder.append(sourceName);
			if (currentIndex != copyOfCosclist.size() - 1) {
				stringBuilder.append(",");
			}
			currentIndex++;
		}
		return stringBuilder;
	}

	@NotNull
	public StringBuilder getStringBuilder() {
		StringBuilder sb0 = new StringBuilder();
		int conum = 0;
		// 当前判断的尾节点，满足条件生成语句，因为+条件+所以+下面时序
		// todo 报错 ：ConcurrentModificationException
		for(Link cslink : cosclist){
			// 只需遍历激活语法+加入场景buffer，无需按层次，剩下的构建场景框架留给语法task，流程一致
//						pam.activGrammarLink(cslink,cslink.getTNname());

//						System.out.println("---------判断首和判断---|||---say----" + cslink.toString());
			String sn = cslink.getSource().getTNname();
			if (sn.contains("(")){
//				continue;
			}else if (conum != cosclist.size() - 1) {
				sb0.append(sn + ",");
			} else {
				sb0.append(sn);
			}
		}
		return sb0;
	}

	private Link getLink(String query, Link link0 ,Transaction tx) {
		try (Result result0 = tx.execute(query, NeoUtil.parameters)) {
			Map<String, Object> row0;
			while (result0.hasNext()) {
				row0 = result0.next();
				Relationship actre;
				for (String key0 : result0.columns()) {
					actre = (Relationship) row0.get(key0);

					link0 = NeoUtil.CastNeoToLidaLink(actre,null);
					Node toNode = (Node)link0.getSink();
					toNode.setIncentiveSalience(sink.getIncentiveSalience());

					// 每个时序分别加入计划，以备执行，头节点已有，不用加入
					pam.getListener().receivePercept(toNode, ModuleName.SeqGraph);
					pam.getListener().receivePercept(link0, ModuleName.SeqGraph);

					System.out.println("判断首和判断---|||-" + link0.toString());

					// 即使当前层时序已经在这里找到并执行，还需要激活非时序节点，如满足和else？
					// 只需找到时序就行，时序节点具体是什么类型，再根据类型执行，往下就往下，如满足和else
//						propagateActivation(toNode, (PamLink) link0, 1.0, 1, "varmindplan");
				}
			}
		}
		return link0;
	}

	private static int getDonenum(Link link, List<Link> clinkList, int donenum, Link conect, List<Link> cosclist) {
		Set<Link> conscene;
		List<Link> halflist;
		String conectstr = "";
		if(conect != null){
			conectstr = conect.getSource().getTNname();
		}
		for (Link conlink : clinkList){
			conscene = NeoUtil.getSomeLinks(conlink.getSource(), null, "<", "场景", null);
			String type;
			Node source, sink;
			String shishi = "", shoushi = "", verb = "";
			boolean shinone = false, shounone = false, noshivar = false, noshouvar = false;
			halflist = new ArrayList<Link> ();
			boolean isdone = false;

			pam.addDefaultNode((Node)conlink.getSink());
			pam.addDefaultLink(conlink);

			for (Link cslink : conscene){
				source = cslink.getSource();
				sink = (Node) cslink.getSink();
				// 条件场景多个元素，还是要分个类
				pam.putMap(source,source.getTNname());
				pam.putMap(sink,sink.getTNname());
				type = cslink.getTNname();

				pam.addDefaultNode((Node)cslink.getSink());
				pam.addDefaultLink(cslink);
				// 如数一数、位数
				if(AgentStarter.varmap.containsKey(source.getTNname())){
					Map<String,Object> resultmap = pam.getIsaLink(source, (Node)cslink.getSink(), cslink.getCategory(),pam);
					if (resultmap.get("done").equals("yes")){
						cslink = (Link) resultmap.get("link");
						// todo 有些初始化了，另外空和o是不同的，空是没有，o是有，但是没有值
					}else {
						// todo 概括方法论描述中，也没有变量值
						switch (type){
							case "arg0":
								noshivar = true;
								break;
							case "arg2":
							case "arg3":
								noshouvar = true;
								break;
						}
					}
				}

				switch (type){
					case "arg0":
						shishi = cslink.getSource().getTNname();
						break;
					case "arg2":
					case "arg3"://类事和受事二选一，什么是什么，什么做什么
						shoushi = cslink.getSource().getTNname();
						break;
					case "arg1":
						verb = cslink.getSource().getTNname();
						break;
				}
				// todo 与或非 不太一样，或只需存一部分，与需要存所有，非需要所有（但可附属于与或）
				// 先存放一部分，等后半部分条件判断完成，是否加入待生成
				halflist.add(cslink);
			}
			Compare compare = new Compare(donenum, halflist, conlink, shishi, shoushi, noshivar, noshouvar, isdone);
			// 初始化，每次判断都是空列表，全局是跨方法需求
			elseLinks = new ArrayList<>();

			if(!shishi.isEmpty() && !shoushi.isEmpty() && !verb.isEmpty()){
				String verb0;
				switch (verb){
					case "为":
						if(shishi.equals(shoushi)){
							isdone = true;
							donenum ++;
						}else {
							elsesize ++;
							String cslinktype;
							for(Link cslink : halflist){
								cslinktype = cslink.getTNname();
								if (cslinktype.equals("动作") || cslinktype.equals("arg1")){
									cslink.getSource().setNodeName("不为");
								}
								elseLinks.add(cslink);
							}
							elseLinks.add(conlink);
							elsemap.put((long) conlink.getId(),elseLinks);
						}
						break;
					case "不为":
						if(!shishi.equals(shoushi)){
							isdone = true;
							donenum ++;
						}else {
							elsesize ++;
							String cslinktype;
							for(Link cslink : halflist){
								cslinktype = cslink.getTNname();
								if (cslinktype.equals("动作") || cslinktype.equals("arg1")){
									cslink.getSource().setNodeName("为");
								}
								elseLinks.add(cslink);
							}
							elseLinks.add(conlink);
							elsemap.put((long) conlink.getId(),elseLinks);
						}
						break;
					case "大于":
						verb0 = "不大于";
						compare = compare.invoke(verb0);
						donenum = compare.getDonenum();
						isdone = compare.isIsdone();
						break;
					case "小于":
						verb0 = "不小于";
						compare = compare.invoke(verb0);
						donenum = compare.getDonenum();
						isdone = compare.isIsdone();
						break;
					case "不大于":
						verb0 = "大于";
						compare = compare.invoke(verb0);
						donenum = compare.getDonenum();
						isdone = compare.isIsdone();
						break;
					case "小于等于":
						verb0 = "大于";
						compare = compare.invoke(verb0);
						donenum = compare.getDonenum();
						isdone = compare.isIsdone();
						break;
					case "不小于":
						verb0 = "小于";
						compare = compare.invoke(verb0);
						donenum = compare.getDonenum();
						isdone = compare.isIsdone();
						break;
					case "大于等于":
						verb0 = "小于";
						compare = compare.invoke(verb0);
						donenum = compare.getDonenum();
						isdone = compare.isIsdone();
						break;
					case "等于":
						verb0 = "不等于";
						compare = compare.invoke(verb0);
						donenum = compare.getDonenum();
						isdone = compare.isIsdone();
						break;
					case "不等于":
						verb0 = "等于";
						compare = compare.invoke(verb0);
						donenum = compare.getDonenum();
						isdone = compare.isIsdone();
						break;
				}
			}

			// 与短路，只要有一个不满足则后面的不用判断了，或只要一个满足即可
			if(conectstr.equals("且")){
				if(!isdone){
					break;
				}
				cosclist.add(conlink);
				cosclist.addAll(halflist);
			}else if (conectstr.equals("或") && isdone){
				cosclist.add(conlink);
				cosclist.addAll(halflist);
			}else if (conect == null){
				cosclist.add(conlink);
				cosclist.addAll(halflist);
			}
		}
		return donenum;
	}

	private void doSucc(Link link, Node sink, Node source) {
		String query;
		query = "match (n:场景)-[r:判断]->(m:场景)<-[r0:顺承]-(i:场景) where n.name = \'" + source.getTNname() + "\'  and i.name = \'" + sink.getTNname()  + "\' return r";
		System.out.println("query = " + query);
		Link link1 = null;
		try (Transaction tx0 = graphDb.beginTx()) {
			try (Result result0 = tx0.execute(query, NeoUtil.parameters)) {
				Map<String, Object> row0;
				while (result0.hasNext()) {
					row0 = result0.next();
					Relationship actre;
					for (String key0 : result0.columns()) {
						actre = (Relationship) row0.get(key0);

						link1 = NeoUtil.CastNeoToLidaLink(actre,null);
						Node toNode = (Node)link1.getSink();

						pam.getListener().receivePercept(toNode, ModuleName.SeqGraph);
						pam.getListener().receivePercept(link1, ModuleName.SeqGraph);

						toNode.setIncentiveSalience(sink.getIncentiveSalience());

						System.out.println("顺承判断序列---|||-" + link1.toString());

					}
				}
			}
			tx0.commit();
		}
		if (link1 != null) {
			// 如果下面还有判断，继续执行，
//			doSelectRoot(link1);
		}else {
		    System.out.println("------------------各判断都不满足，或者查询出错---------------------");
		}
	}

	public static class Compare {
		private int donenum;
		private List<Link> halflist;
		private Link conlink;
		private String shishi;
		private String shoushi;
		private boolean noshivar;
		private boolean noshouvar;
		private boolean isdone;

		public Compare(int donenum, List<Link> halflist, Link conlink, String shishi,
					   String shoushi, boolean noshivar, boolean noshouvar, boolean isdone) {
			this.donenum = donenum;
			this.halflist = halflist;
			this.conlink = conlink;
			this.shishi = shishi;
			this.shoushi = shoushi;
			this.noshivar = noshivar;
			this.noshouvar = noshouvar;
			this.isdone = isdone;
		}

		public int getDonenum() {
			return donenum;
		}

		public boolean isIsdone() {
			return isdone;
		}

		public Compare invoke(String verb) {
			boolean iselse = false;
			int shishi0 = Integer.parseInt(shishi);
			int shoushi0 = Integer.parseInt(shoushi);
			// 判断变量是否有现值，没有则无法比较，直接归为不满足
			// todo 概括描述无需现值，不是不满足，也没满足。
			//  两者为零，可能只是初始值，非运行后现值，如当前进位
			if((noshivar && noshouvar) || (noshouvar && !noshivar) || (!noshouvar && noshivar) || (shishi0 == 0 && shoushi0 == 0)){
				iselse = true;
			}else if(shishi0 > shoushi0){
				isdone = true;
				donenum ++;
			}
			if(iselse) {
				elsesize ++;
				for(Link cslink : halflist){
					if (cslink.getTNname().equals("动作") || cslink.getTNname().equals("arg1")){
						// 存入反命题，可能负负得正
						cslink.getSource().setNodeName(verb);
					}
					elseLinks.add(cslink);
				}
				// else部分：【或】不用存任何，【与】要存至少一个不满足条件，【非】附属于与或，单独非要存不满足
				// 满足部分：与存所有，或存一个，非附属，单独非存满足，与短路
				elseLinks.add(conlink);
				elsemap.put((long) conlink.getId(),elseLinks);
			}
			return this;
		}
	}
}
