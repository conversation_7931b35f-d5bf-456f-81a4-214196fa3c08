package schema.model.nodes;

import schema.model.Node;

/**
 * 控制节点，表示控制结构，如循环、条件判断等
 */
public class ControlNode extends Node {
    private String controlType; // 控制类型：sequence, conditional, loop
    private String condition;   // 条件表达式，用于条件和循环控制

    public ControlNode(String id, String name, String controlType, String condition) {
        super(id, name, "ControlNode");
        this.controlType = controlType;
        this.condition = condition;
        setProperty("control_type", controlType);
        setProperty("condition", condition);
    }

    public String getControlType() {
        return controlType;
    }

    public String getCondition() {
        return condition;
    }
}
