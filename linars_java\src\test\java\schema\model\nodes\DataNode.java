package schema.model.nodes;

import schema.model.Node;

/**
 * 数据节点，表示数据或变量，如操作数、结果、进位等
 */
public class DataNode extends Node {
    private String dataType; // 数据类型：string, integer, boolean, etc.
    private Object value;    // 数据值

    public DataNode(String id, String name, String dataType, Object value) {
        super(id, name, "DataNode");
        this.dataType = dataType;
        this.value = value;
        setProperty("data_type", dataType);
        setProperty("value", value);
    }

    public String getDataType() {
        return dataType;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
        setProperty("value", value);
    }
}
